import { Package, Eye, RotateCcw, LoaderCircle, Calendar, Truck } from 'lucide-react';
import React, { useState, useEffect, useCallback } from 'react';
import { apiClient } from '@/bootstrap';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface OrderItem {
    id: number;
    product_name: string;
    quantity: number;
    unit_price: number;
    total_price: number;
}

interface Order {
    id: number;
    order_number: string;
    status: string;
    payment_status: string;
    total_amount: number;
    currency: string;
    created_at: string;
    order_items: OrderItem[];
}

export default function OrderHistorySection() {
    const [orders, setOrders] = useState<Order[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [statusFilter, setStatusFilter] = useState<string>('all');

    const fetchOrders = useCallback(async () => {
        try {
            setLoading(true);
            const params = statusFilter !== 'all' ? { status: statusFilter } : {};
            const response = await apiClient.get('/orders', { params });
            if (response.data.success) {
                setOrders(response.data.data);
            } else {
                setError('Failed to load orders');
            }
        } catch (err) {
            setError('Failed to load orders');
            console.error('Error fetching orders:', err);
        } finally {
            setLoading(false);
        }
    }, [statusFilter]);

    useEffect(() => {
        fetchOrders();
    }, [statusFilter, fetchOrders]);

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'processing':
                return 'bg-blue-100 text-blue-800';
            case 'shipped':
                return 'bg-purple-100 text-purple-800';
            case 'delivered':
                return 'bg-green-100 text-green-800';
            case 'cancelled':
                return 'bg-red-100 text-red-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const getPaymentStatusColor = (status: string) => {
        switch (status) {
            case 'paid':
                return 'bg-green-100 text-green-800';
            case 'pending':
                return 'bg-yellow-100 text-yellow-800';
            case 'failed':
                return 'bg-red-100 text-red-800';
            case 'refunded':
                return 'bg-gray-100 text-gray-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    };

    const formatCurrency = (amount: number, currency: string) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: currency || 'PHP'
        }).format(amount);
    };

    const handleReorder = async () => {
        try {
            // In a real implementation, this would add all items from the order to cart
            alert('Reorder functionality would be implemented here');
        } catch (err) {
            console.error('Error reordering:', err);
            alert('Failed to reorder items');
        }
    };

    const handleViewOrder = (orderNumber: string) => {
        // Navigate to order details page
        window.location.href = `/orders/${orderNumber}`;
    };

    if (loading) {
        return (
            <Card>
                <CardContent className="p-6">
                    <div className="flex items-center justify-center">
                        <LoaderCircle className="w-6 h-6 animate-spin mr-2" />
                        Loading order history...
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (error) {
        return (
            <Card>
                <CardContent className="p-6">
                    <div className="text-center text-red-600">
                        <p>{error}</p>
                        <Button onClick={fetchOrders} className="mt-2">
                            Try Again
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <div>
                    <CardTitle>Order History</CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">
                        View and manage your past orders
                    </p>
                </div>
                <div className="flex items-center space-x-2">
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-40">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">All Orders</SelectItem>
                            <SelectItem value="pending">Pending</SelectItem>
                            <SelectItem value="processing">Processing</SelectItem>
                            <SelectItem value="shipped">Shipped</SelectItem>
                            <SelectItem value="delivered">Delivered</SelectItem>
                            <SelectItem value="cancelled">Cancelled</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
            </CardHeader>
            <CardContent>
                {orders.length === 0 ? (
                    <div className="text-center py-8">
                        <Package className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">
                            {statusFilter === 'all' ? 'No orders yet' : `No ${statusFilter} orders`}
                        </h3>
                        <p className="text-muted-foreground mb-4">
                            {statusFilter === 'all' 
                                ? 'Start shopping to see your orders here'
                                : `You don't have any ${statusFilter} orders`
                            }
                        </p>
                        {statusFilter === 'all' && (
                            <Button asChild>
                                <a href="/products">Start Shopping</a>
                            </Button>
                        )}
                    </div>
                ) : (
                    <div className="space-y-4">
                        {orders.map((order) => (
                            <div key={order.id} className="border rounded-lg p-4">
                                <div className="flex items-center justify-between mb-4">
                                    <div className="flex items-center space-x-4">
                                        <div>
                                            <h4 className="font-medium">Order #{order.order_number}</h4>
                                            <div className="flex items-center space-x-2 mt-1">
                                                <Calendar className="w-4 h-4 text-muted-foreground" />
                                                <span className="text-sm text-muted-foreground">
                                                    {formatDate(order.created_at)}
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <p className="font-semibold">
                                            {formatCurrency(order.total_amount, order.currency)}
                                        </p>
                                        <div className="flex items-center space-x-2 mt-1">
                                            <Badge className={getStatusColor(order.status)}>
                                                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                            </Badge>
                                            <Badge className={getPaymentStatusColor(order.payment_status)}>
                                                {order.payment_status.charAt(0).toUpperCase() + order.payment_status.slice(1)}
                                            </Badge>
                                        </div>
                                    </div>
                                </div>

                                <div className="mb-4">
                                    <h5 className="text-sm font-medium mb-2">Items ({order.order_items.length})</h5>
                                    <div className="space-y-1">
                                        {order.order_items.slice(0, 3).map((item) => (
                                            <div key={item.id} className="flex justify-between text-sm">
                                                <span className="text-muted-foreground">
                                                    {item.quantity}x {item.product_name}
                                                </span>
                                                <span>
                                                    {formatCurrency(item.total_price, order.currency)}
                                                </span>
                                            </div>
                                        ))}
                                        {order.order_items.length > 3 && (
                                            <p className="text-sm text-muted-foreground">
                                                +{order.order_items.length - 3} more items
                                            </p>
                                        )}
                                    </div>
                                </div>

                                <div className="flex justify-between items-center">
                                    <div className="flex space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleViewOrder(order.order_number)}
                                        >
                                            <Eye className="w-4 h-4 mr-1" />
                                            View Details
                                        </Button>
                                        {order.status === 'delivered' && (
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleReorder()}
                                            >
                                                <RotateCcw className="w-4 h-4 mr-1" />
                                                Reorder
                                            </Button>
                                        )}
                                    </div>
                                    {(order.status === 'shipped' || order.status === 'processing') && (
                                        <div className="flex items-center text-sm text-muted-foreground">
                                            <Truck className="w-4 h-4 mr-1" />
                                            Track Package
                                        </div>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
