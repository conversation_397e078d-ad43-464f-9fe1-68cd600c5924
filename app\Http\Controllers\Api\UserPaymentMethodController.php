<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserPaymentMethod;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class UserPaymentMethodController extends Controller
{
    /**
     * Display user's payment methods.
     */
    public function index(Request $request): JsonResponse
    {
        $paymentMethods = $request->user()->paymentMethods()
            ->orderBy('is_default', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $paymentMethods
        ]);
    }

    /**
     * Store a new payment method.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'type' => 'required|in:credit_card,gcash,paymaya,bpi_online,bdo_online,paypal',
            'label' => 'nullable|string|max:100',
            
            // Credit card fields
            'card_last_four' => 'required_if:type,credit_card|nullable|string|size:4',
            'card_brand' => 'required_if:type,credit_card|nullable|in:visa,mastercard,amex,jcb',
            'card_holder_name' => 'required_if:type,credit_card|nullable|string|max:100',
            'expiry_month' => 'required_if:type,credit_card|nullable|string|size:2',
            'expiry_year' => 'required_if:type,credit_card|nullable|string|size:4',
            
            // Mobile payment fields
            'mobile_number' => 'required_if:type,gcash,paymaya|nullable|string|regex:/^(\+63|0)[0-9]{10}$/',
            'account_name' => 'required_if:type,gcash,paymaya,bpi_online,bdo_online|nullable|string|max:100',
            
            // Bank fields
            'bank_name' => 'required_if:type,bpi_online,bdo_online|nullable|string|max:100',
            'account_number_last_four' => 'required_if:type,bpi_online,bdo_online|nullable|string|size:4',
            'account_holder_name' => 'required_if:type,bpi_online,bdo_online|nullable|string|max:100',
            
            // PayPal fields
            'paypal_email' => 'required_if:type,paypal|nullable|email|max:255',
            
            'is_default' => 'boolean',
        ]);

        $validated['user_id'] = $request->user()->id;

        // For security, we don't store full card numbers or sensitive data
        // In a real implementation, you'd integrate with payment processors
        $paymentMethod = UserPaymentMethod::create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Payment method added successfully',
            'data' => $paymentMethod
        ], 201);
    }

    /**
     * Display the specified payment method.
     */
    public function show(Request $request, UserPaymentMethod $paymentMethod): JsonResponse
    {
        // Ensure user can only view their own payment methods
        if ($paymentMethod->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to payment method'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $paymentMethod
        ]);
    }

    /**
     * Update the specified payment method.
     */
    public function update(Request $request, UserPaymentMethod $paymentMethod): JsonResponse
    {
        // Ensure user can only update their own payment methods
        if ($paymentMethod->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to payment method'
            ], 403);
        }

        $validated = $request->validate([
            'label' => 'nullable|string|max:100',
            'is_default' => 'boolean',
            // Only allow updating non-sensitive fields
            'account_name' => 'nullable|string|max:100',
        ]);

        $paymentMethod->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Payment method updated successfully',
            'data' => $paymentMethod
        ]);
    }

    /**
     * Remove the specified payment method.
     */
    public function destroy(Request $request, UserPaymentMethod $paymentMethod): JsonResponse
    {
        // Ensure user can only delete their own payment methods
        if ($paymentMethod->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to payment method'
            ], 403);
        }

        $paymentMethod->delete();

        return response()->json([
            'success' => true,
            'message' => 'Payment method deleted successfully'
        ]);
    }

    /**
     * Set payment method as default.
     */
    public function setDefault(Request $request, UserPaymentMethod $paymentMethod): JsonResponse
    {
        // Ensure user can only modify their own payment methods
        if ($paymentMethod->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to payment method'
            ], 403);
        }

        $paymentMethod->update(['is_default' => true]);

        return response()->json([
            'success' => true,
            'message' => 'Default payment method updated successfully',
            'data' => $paymentMethod
        ]);
    }

    /**
     * Verify payment method (placeholder for payment processor integration).
     */
    public function verify(Request $request, UserPaymentMethod $paymentMethod): JsonResponse
    {
        // Ensure user can only verify their own payment methods
        if ($paymentMethod->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to payment method'
            ], 403);
        }

        // In a real implementation, this would integrate with payment processors
        // For now, we'll just mark it as verified
        $paymentMethod->update([
            'is_verified' => true,
            'verified_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Payment method verified successfully',
            'data' => $paymentMethod
        ]);
    }
}
