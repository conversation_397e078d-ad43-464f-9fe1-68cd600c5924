import { Head, Link, usePage } from '@inertiajs/react';
import { ArrowLeft, Check, ShoppingCart, User, Truck, CreditCard, FileText } from 'lucide-react';
import React, { useState, useCallback, useEffect } from 'react';

// Import customer components
import Footer from '@/components/customer/Footer';
import Header from '@/components/customer/Header';
import { CartProviderWrapper } from '@/components/CartProviderWrapper';
import { useCart } from '@/hooks/use-cart';

// Import UI components

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

// Import checkout components
import CustomerInfoStep from '@/components/checkout/CustomerInfoStep';
import ShippingStep from '@/components/checkout/ShippingStep';
import PaymentStep from '@/components/checkout/PaymentStep';
import ReviewStep from '@/components/checkout/ReviewStep';

import { type SharedData } from '@/types';
import { type CheckoutData, type CheckoutStep } from '@/types/ecommerce';

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
interface CheckoutPageContentProps {}

const CheckoutPageContent: React.FC<CheckoutPageContentProps> = () => {
    const { auth } = usePage<SharedData>().props;
    const { cart, loading: cartLoading } = useCart();

    const [currentStep, setCurrentStep] = useState(0);
    const [loading, setLoading] = useState(false);
    const [hasCheckedCart, setHasCheckedCart] = useState(false);
    const [checkoutData, setCheckoutData] = useState<Partial<CheckoutData>>({
        customer_info: {
            email: auth?.user?.email || '',
            phone: '',
            create_account: false,
        },
        same_as_billing: true,
    });

    const steps: CheckoutStep[] = [
        {
            id: 'customer-info',
            title: 'Customer Information',
            description: 'Contact details and account preferences',
            completed: false,
            current: currentStep === 0,
        },
        {
            id: 'shipping',
            title: 'Shipping & Billing',
            description: 'Delivery and billing addresses',
            completed: false,
            current: currentStep === 1,
        },
        {
            id: 'payment',
            title: 'Payment Method',
            description: 'Choose your payment option',
            completed: false,
            current: currentStep === 2,
        },
        {
            id: 'review',
            title: 'Review Order',
            description: 'Confirm your order details',
            completed: false,
            current: currentStep === 3,
        },
    ];

    const cartItems = cart?.items || [];
    const totalItems = cart?.total_items || 0;
    const subtotal = cart?.subtotal || 0;

    // Redirect to cart if empty (but only after cart has been properly loaded)
    useEffect(() => {
        if (!cartLoading && cart !== null) {
            setHasCheckedCart(true);
            if (cartItems.length === 0) {
                // Add a small delay to ensure the page has rendered
                setTimeout(() => {
                    window.location.href = '/cart';
                }, 100);
            }
        }
    }, [cartLoading, cart, cartItems.length]);

    const updateCheckoutData = useCallback((updates: Partial<CheckoutData>) => {
        setCheckoutData(prev => ({ ...prev, ...updates }));
    }, []);

    const handleNextStep = useCallback(() => {
        if (currentStep < steps.length - 1) {
            setCurrentStep(prev => prev + 1);
        }
    }, [currentStep, steps.length]);

    const handlePrevStep = useCallback(() => {
        if (currentStep > 0) {
            setCurrentStep(prev => prev - 1);
        }
    }, [currentStep]);

    const handleStepClick = useCallback((stepIndex: number) => {
        // Allow navigation to previous steps or current step
        if (stepIndex <= currentStep) {
            setCurrentStep(stepIndex);
        }
    }, [currentStep]);

    const getStepIcon = (step: CheckoutStep, index: number) => {
        if (index < currentStep) {
            return <Check className="h-5 w-5 text-white" />;
        }
        
        switch (step.id) {
            case 'customer-info':
                return <User className="h-5 w-5" />;
            case 'shipping':
                return <Truck className="h-5 w-5" />;
            case 'payment':
                return <CreditCard className="h-5 w-5" />;
            case 'review':
                return <FileText className="h-5 w-5" />;
            default:
                return <span className="text-sm font-medium">{index + 1}</span>;
        }
    };

    if (cartLoading || !hasCheckedCart) {
        return (
            <div className="min-h-screen bg-background">
                <Header />
                <div className="container mx-auto px-4 py-8">
                    <div className="flex items-center justify-center min-h-[400px]">
                        <div className="text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                            <p className="text-muted-foreground">Loading checkout...</p>
                        </div>
                    </div>
                </div>
                <Footer />
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-background">
            <Header />
            
            <div className="container mx-auto px-4 py-8">
                {/* Breadcrumb */}
                <div className="flex items-center gap-2 text-sm text-muted-foreground mb-6">
                    <Link href="/" className="hover:text-foreground transition-colors">
                        Home
                    </Link>
                    <span>/</span>
                    <Link href="/cart" className="hover:text-foreground transition-colors">
                        Cart
                    </Link>
                    <span>/</span>
                    <span className="text-foreground font-medium">Checkout</span>
                </div>

                {/* Page Header */}
                <div className="flex items-center justify-between mb-8">
                    <div className="flex items-center gap-3">
                        <ShoppingCart className="h-8 w-8 text-primary" />
                        <div>
                            <h1 className="text-3xl font-bold">Checkout</h1>
                            <p className="text-muted-foreground">
                                Complete your order in {steps.length} easy steps
                            </p>
                        </div>
                    </div>
                    
                    <Link href="/cart">
                        <Button variant="outline" className="flex items-center gap-2">
                            <ArrowLeft className="h-4 w-4" />
                            Back to Cart
                        </Button>
                    </Link>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Main Content */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Progress Steps */}
                        <Card>
                            <CardContent className="p-6">
                                <div className="flex items-center justify-between">
                                    {steps.map((step, index) => (
                                        <div key={step.id} className="flex items-center">
                                            <button
                                                onClick={() => handleStepClick(index)}
                                                disabled={index > currentStep}
                                                className={`flex items-center justify-center w-10 h-10 rounded-full border-2 transition-all duration-200 ${
                                                    index < currentStep
                                                        ? 'bg-primary border-primary text-primary-foreground'
                                                        : index === currentStep
                                                        ? 'border-primary text-primary bg-primary/10'
                                                        : 'border-muted-foreground/30 text-muted-foreground'
                                                } ${index <= currentStep ? 'cursor-pointer hover:scale-105' : 'cursor-not-allowed'}`}
                                            >
                                                {getStepIcon(step, index)}
                                            </button>
                                            
                                            {index < steps.length - 1 && (
                                                <div className={`w-16 h-0.5 mx-2 ${
                                                    index < currentStep ? 'bg-primary' : 'bg-muted-foreground/30'
                                                }`} />
                                            )}
                                        </div>
                                    ))}
                                </div>
                                
                                <div className="mt-4 text-center">
                                    <h3 className="font-semibold text-lg">{steps[currentStep].title}</h3>
                                    <p className="text-sm text-muted-foreground">{steps[currentStep].description}</p>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Step Content */}
                        <Card>
                            <CardContent className="p-6">
                                {currentStep === 0 && (
                                    <CustomerInfoStep
                                        data={checkoutData.customer_info || {}}
                                        onUpdate={(data) => updateCheckoutData({ customer_info: data })}
                                        onNext={handleNextStep}
                                        isAuthenticated={!!auth?.user}
                                    />
                                )}
                                
                                {currentStep === 1 && (
                                    <ShippingStep
                                        billingAddress={checkoutData.billing_address}
                                        shippingAddress={checkoutData.shipping_address}
                                        sameAsBilling={checkoutData.same_as_billing || false}
                                        onUpdate={(data) => updateCheckoutData(data)}
                                        onNext={handleNextStep}
                                        onPrev={handlePrevStep}
                                    />
                                )}
                                
                                {currentStep === 2 && (
                                    <PaymentStep
                                        selectedMethod={checkoutData.payment_method}
                                        onUpdate={(data) => updateCheckoutData(data)}
                                        onNext={handleNextStep}
                                        onPrev={handlePrevStep}
                                        orderTotal={subtotal}
                                    />
                                )}
                                
                                {currentStep === 3 && (
                                    <ReviewStep
                                        checkoutData={checkoutData}
                                        cartItems={cartItems}
                                        onPrev={handlePrevStep}
                                        loading={loading}
                                        onPlaceOrder={async () => {
                                            setLoading(true);
                                            try {
                                                // TODO: Implement actual order placement API call
                                                console.log('Place order:', checkoutData);

                                                // Simulate API call
                                                await new Promise(resolve => setTimeout(resolve, 2000));

                                                // Redirect to order confirmation
                                                // window.location.href = '/orders/confirmation/123';
                                            } catch (error) {
                                                console.error('Order placement failed:', error);
                                                // Handle error (show toast, etc.)
                                            } finally {
                                                setLoading(false);
                                            }
                                        }}
                                    />
                                )}
                            </CardContent>
                        </Card>
                    </div>

                    {/* Order Summary Sidebar */}
                    <div className="space-y-6">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <ShoppingCart className="h-5 w-5" />
                                    Order Summary
                                </CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-3">
                                    {cartItems.slice(0, 3).map((item) => (
                                        <div key={item.id} className="flex items-center gap-3">
                                            <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center">
                                                <span className="text-xs font-medium">{item.quantity}</span>
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <p className="text-sm font-medium truncate">{item.product.name}</p>
                                                <p className="text-xs text-muted-foreground">
                                                    ₱{item.product.price.toLocaleString()}
                                                </p>
                                            </div>
                                            <p className="text-sm font-medium">
                                                ₱{(item.product.price * item.quantity).toLocaleString()}
                                            </p>
                                        </div>
                                    ))}
                                    
                                    {cartItems.length > 3 && (
                                        <p className="text-sm text-muted-foreground text-center">
                                            +{cartItems.length - 3} more items
                                        </p>
                                    )}
                                </div>
                                
                                <Separator />
                                
                                <div className="space-y-2">
                                    <div className="flex justify-between text-sm">
                                        <span>Subtotal ({totalItems} items)</span>
                                        <span>₱{subtotal.toLocaleString()}</span>
                                    </div>
                                    <div className="flex justify-between text-sm">
                                        <span>Shipping</span>
                                        <span className="text-muted-foreground">Calculated at next step</span>
                                    </div>
                                    <div className="flex justify-between text-sm">
                                        <span>Tax</span>
                                        <span className="text-muted-foreground">Calculated at next step</span>
                                    </div>
                                </div>
                                
                                <Separator />
                                
                                <div className="flex justify-between font-semibold">
                                    <span>Total</span>
                                    <span>₱{subtotal.toLocaleString()}+</span>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
            
            <Footer />
        </div>
    );
};

export default function CheckoutPage() {
    return (
        <>
            <Head title="Checkout" />
            <CartProviderWrapper>
                <CheckoutPageContent />
            </CartProviderWrapper>
        </>
    );
}
