import React, { useState, useCallback, useEffect } from 'react';
import { CreditCard, Smartphone, Building, Shield, Plus, BookOpen } from 'lucide-react';
import { apiClient } from '@/bootstrap';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

import { type PaymentMethod, type PaymentDetails } from '@/types/ecommerce';

interface SavedPaymentMethod {
    id: number;
    type: string;
    label: string | null;
    display_name: string;
    is_default: boolean;
    is_verified: boolean;
}

interface PaymentStepProps {
    selectedMethod?: PaymentMethod;
    onUpdate: (data: { payment_method: PaymentMethod; payment_details?: PaymentDetails }) => void;
    onNext: () => void;
    onPrev: () => void;
    orderTotal: number;
}

const PAYMENT_METHODS: PaymentMethod[] = [
    {
        id: 'cod',
        name: 'Cash on Delivery',
        description: 'Pay when your order is delivered to your doorstep',
        icon: '💵',
        is_available: true,
        requires_online_payment: false,
        processing_fee: 0,
    },
    {
        id: 'gcash',
        name: 'GCash',
        description: 'Pay securely using your GCash wallet',
        icon: '📱',
        is_available: true,
        requires_online_payment: true,
        processing_fee: 0,
    },
    {
        id: 'paymaya',
        name: 'PayMaya',
        description: 'Pay using your PayMaya account or card',
        icon: '💳',
        is_available: true,
        requires_online_payment: true,
        processing_fee: 0,
    },
    {
        id: 'bpi_online',
        name: 'BPI Online Banking',
        description: 'Pay directly from your BPI account',
        icon: '🏦',
        is_available: true,
        requires_online_payment: true,
        processing_fee: 15,
    },
    {
        id: 'bdo_online',
        name: 'BDO Online Banking',
        description: 'Pay directly from your BDO account',
        icon: '🏦',
        is_available: true,
        requires_online_payment: true,
        processing_fee: 15,
    },
    {
        id: 'paypal',
        name: 'PayPal',
        description: 'Pay securely with your PayPal account',
        icon: '🅿️',
        is_available: true,
        requires_online_payment: true,
        processing_fee: 0,
    },
    {
        id: 'credit_card',
        name: 'Credit/Debit Card',
        description: 'Visa, Mastercard, American Express',
        icon: '💳',
        is_available: true,
        requires_online_payment: true,
        processing_fee: 0,
    },
];

const PaymentStep: React.FC<PaymentStepProps> = ({
    selectedMethod,
    onUpdate,
    onNext,
    onPrev,
    orderTotal
}) => {
    const [savedPaymentMethods, setSavedPaymentMethods] = useState<SavedPaymentMethod[]>([]);
    const [loadingPaymentMethods, setLoadingPaymentMethods] = useState(false);
    const [selectedSavedMethodId, setSelectedSavedMethodId] = useState<number | null>(null);
    const [showNewPaymentForm, setShowNewPaymentForm] = useState(false);
    const [formData, setFormData] = useState({
        payment_method: selectedMethod,
        payment_details: {
            // Credit card fields
            card_number: '',
            card_name: '',
            expiry_month: '',
            expiry_year: '',
            cvv: '',
            // GCash/PayMaya fields
            mobile_number: '',
            // Bank fields
            account_number: '',
        }
    });

    const [errors, setErrors] = useState<Record<string, string>>({});

    // Load saved payment methods on component mount
    useEffect(() => {
        const loadSavedPaymentMethods = async () => {
            try {
                setLoadingPaymentMethods(true);
                const response = await apiClient.get('/payment-methods');
                if (response.data.success) {
                    const methods = response.data.data;
                    setSavedPaymentMethods(methods);

                    // Auto-select default payment method if no method is currently selected
                    if (!selectedMethod && !selectedSavedMethodId) {
                        const defaultMethod = methods.find((method: SavedPaymentMethod) => method.is_default);
                        if (defaultMethod) {
                            setSelectedSavedMethodId(defaultMethod.id);
                            handleSelectSavedPaymentMethod(defaultMethod);
                        }
                    }
                }
            } catch (error) {
                console.error('Failed to load saved payment methods:', error);
            } finally {
                setLoadingPaymentMethods(false);
            }
        };

        loadSavedPaymentMethods();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Handle selecting a saved payment method
    const handleSelectSavedPaymentMethod = (savedMethod: SavedPaymentMethod) => {
        setSelectedSavedMethodId(savedMethod.id);
        setShowNewPaymentForm(false);

        // Find the corresponding payment method from PAYMENT_METHODS
        const paymentMethod = PAYMENT_METHODS.find(method => method.id === savedMethod.type);
        if (paymentMethod) {
            setFormData(prev => ({
                ...prev,
                payment_method: paymentMethod,
                payment_details: {
                    ...prev.payment_details,
                    // Clear form details since we're using saved method
                }
            }));
        }
    };

    const handlePaymentMethodChange = useCallback((method: PaymentMethod) => {
        const updated = { ...formData, payment_method: method };
        setFormData(updated);

        // Create proper PaymentDetails object
        const paymentDetails: PaymentDetails = {
            method: method.id,
            ...updated.payment_details
        };

        onUpdate({ payment_method: method, payment_details: paymentDetails });

        // Clear errors when changing payment method
        setErrors({});
    }, [formData, onUpdate]);

    const handlePaymentDetailChange = useCallback((field: string, value: string) => {
        setFormData(prev => {
            const updated = {
                ...prev,
                payment_details: {
                    ...prev.payment_details,
                    [field]: value
                }
            };
            if (prev.payment_method) {
                // Create proper PaymentDetails object
                const paymentDetails: PaymentDetails = {
                    method: prev.payment_method.id,
                    ...updated.payment_details
                };
                onUpdate({ payment_method: prev.payment_method, payment_details: paymentDetails });
            }
            return updated;
        });

        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
        }
    }, [onUpdate, errors]);

    const validateForm = useCallback(() => {
        if (!formData.payment_method) {
            setErrors({ payment_method: 'Please select a payment method' });
            return false;
        }

        const validateCreditCard = () => {
            const newErrors: Record<string, string> = {};
            const { card_number, card_name, expiry_month, expiry_year, cvv } = formData.payment_details;

            if (!card_number || card_number.replace(/\s/g, '').length < 13) {
                newErrors.card_number = 'Please enter a valid card number';
            }
            if (!card_name) {
                newErrors.card_name = 'Cardholder name is required';
            }
            if (!expiry_month) {
                newErrors.expiry_month = 'Expiry month is required';
            }
            if (!expiry_year) {
                newErrors.expiry_year = 'Expiry year is required';
            }
            if (!cvv || cvv.length < 3) {
                newErrors.cvv = 'Please enter a valid CVV';
            }

            return newErrors;
        };

        const validateMobilePayment = () => {
            const newErrors: Record<string, string> = {};
            const { mobile_number } = formData.payment_details;

            if (!mobile_number || !/^(\+63|0)[0-9]{10}$/.test(mobile_number.replace(/\s/g, ''))) {
                newErrors.mobile_number = 'Please enter a valid Philippine mobile number';
            }

            return newErrors;
        };

        const validateBankPayment = () => {
            const newErrors: Record<string, string> = {};
            const { account_number } = formData.payment_details;

            if (!account_number || account_number.length < 10) {
                newErrors.account_number = 'Please enter a valid account number';
            }

            return newErrors;
        };

        let newErrors: Record<string, string> = {};

        switch (formData.payment_method.id) {
            case 'credit_card':
                newErrors = validateCreditCard();
                break;
            case 'gcash':
            case 'paymaya':
                newErrors = validateMobilePayment();
                break;
            case 'bpi_online':
            case 'bdo_online':
                newErrors = validateBankPayment();
                break;
            case 'cod':
            case 'paypal':
                // No additional validation needed
                break;
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    }, [formData]);

    const handleNext = useCallback(() => {
        if (validateForm()) {
            onNext();
        }
    }, [validateForm, onNext]);

    const formatCardNumber = (value: string) => {
        const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
        const matches = v.match(/\d{4,16}/g);
        const match = matches && matches[0] || '';
        const parts = [];
        for (let i = 0, len = match.length; i < len; i += 4) {
            parts.push(match.substring(i, i + 4));
        }
        if (parts.length) {
            return parts.join(' ');
        } else {
            return v;
        }
    };

    const renderPaymentDetails = () => {
        if (!formData.payment_method) return null;

        switch (formData.payment_method.id) {
            case 'credit_card':
                return (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <CreditCard className="h-5 w-5" />
                                Card Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="card-number">Card Number *</Label>
                                <Input
                                    id="card-number"
                                    placeholder="1234 5678 9012 3456"
                                    value={formData.payment_details.card_number}
                                    onChange={(e) => handlePaymentDetailChange('card_number', formatCardNumber(e.target.value))}
                                    maxLength={19}
                                    className={errors.card_number ? 'border-destructive' : ''}
                                />
                                {errors.card_number && (
                                    <p className="text-sm text-destructive">{errors.card_number}</p>
                                )}
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="card-name">Cardholder Name *</Label>
                                <Input
                                    id="card-name"
                                    placeholder="John Doe"
                                    value={formData.payment_details.card_name}
                                    onChange={(e) => handlePaymentDetailChange('card_name', e.target.value)}
                                    className={errors.card_name ? 'border-destructive' : ''}
                                />
                                {errors.card_name && (
                                    <p className="text-sm text-destructive">{errors.card_name}</p>
                                )}
                            </div>

                            <div className="grid grid-cols-3 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="expiry-month">Month *</Label>
                                    <Select
                                        value={formData.payment_details.expiry_month}
                                        onValueChange={(value) => handlePaymentDetailChange('expiry_month', value)}
                                    >
                                        <SelectTrigger className={errors.expiry_month ? 'border-destructive' : ''}>
                                            <SelectValue placeholder="MM" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Array.from({ length: 12 }, (_, i) => (
                                                <SelectItem key={i + 1} value={String(i + 1).padStart(2, '0')}>
                                                    {String(i + 1).padStart(2, '0')}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                    {errors.expiry_month && (
                                        <p className="text-sm text-destructive">{errors.expiry_month}</p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="expiry-year">Year *</Label>
                                    <Select
                                        value={formData.payment_details.expiry_year}
                                        onValueChange={(value) => handlePaymentDetailChange('expiry_year', value)}
                                    >
                                        <SelectTrigger className={errors.expiry_year ? 'border-destructive' : ''}>
                                            <SelectValue placeholder="YYYY" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {Array.from({ length: 10 }, (_, i) => {
                                                const year = new Date().getFullYear() + i;
                                                return (
                                                    <SelectItem key={year} value={String(year)}>
                                                        {year}
                                                    </SelectItem>
                                                );
                                            })}
                                        </SelectContent>
                                    </Select>
                                    {errors.expiry_year && (
                                        <p className="text-sm text-destructive">{errors.expiry_year}</p>
                                    )}
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="cvv">CVV *</Label>
                                    <Input
                                        id="cvv"
                                        placeholder="123"
                                        value={formData.payment_details.cvv}
                                        onChange={(e) => handlePaymentDetailChange('cvv', e.target.value.replace(/\D/g, ''))}
                                        maxLength={4}
                                        className={errors.cvv ? 'border-destructive' : ''}
                                    />
                                    {errors.cvv && (
                                        <p className="text-sm text-destructive">{errors.cvv}</p>
                                    )}
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                );

            case 'gcash':
            case 'paymaya':
                return (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Smartphone className="h-5 w-5" />
                                {formData.payment_method.name} Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="mobile-number">Mobile Number *</Label>
                                <Input
                                    id="mobile-number"
                                    placeholder="+63 ************"
                                    value={formData.payment_details.mobile_number}
                                    onChange={(e) => handlePaymentDetailChange('mobile_number', e.target.value)}
                                    className={errors.mobile_number ? 'border-destructive' : ''}
                                />
                                {errors.mobile_number && (
                                    <p className="text-sm text-destructive">{errors.mobile_number}</p>
                                )}
                                <p className="text-sm text-muted-foreground">
                                    Enter the mobile number linked to your {formData.payment_method.name} account
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                );

            case 'bpi_online':
            case 'bdo_online':
                return (
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Building className="h-5 w-5" />
                                {formData.payment_method.name} Information
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="account-number">Account Number *</Label>
                                <Input
                                    id="account-number"
                                    placeholder="Enter your account number"
                                    value={formData.payment_details.account_number}
                                    onChange={(e) => handlePaymentDetailChange('account_number', e.target.value)}
                                    className={errors.account_number ? 'border-destructive' : ''}
                                />
                                {errors.account_number && (
                                    <p className="text-sm text-destructive">{errors.account_number}</p>
                                )}
                                <p className="text-sm text-muted-foreground">
                                    You'll be redirected to {formData.payment_method.name.split(' ')[0]} online banking to complete the payment
                                </p>
                            </div>
                        </CardContent>
                    </Card>
                );

            case 'paypal':
                return (
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-center space-y-4">
                                <div className="text-4xl">🅿️</div>
                                <div>
                                    <h3 className="font-semibold">PayPal Payment</h3>
                                    <p className="text-sm text-muted-foreground">
                                        You'll be redirected to PayPal to complete your payment securely
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                );

            case 'cod':
                return (
                    <Card>
                        <CardContent className="p-6">
                            <div className="text-center space-y-4">
                                <div className="text-4xl">💵</div>
                                <div>
                                    <h3 className="font-semibold">Cash on Delivery</h3>
                                    <p className="text-sm text-muted-foreground">
                                        Pay with cash when your order is delivered. Please have the exact amount ready.
                                    </p>
                                </div>
                                <div className="bg-yellow-50 dark:bg-yellow-950/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
                                    <p className="text-sm text-yellow-800 dark:text-yellow-200">
                                        <strong>Note:</strong> COD orders may take longer to process and are subject to verification.
                                    </p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                );

            default:
                return null;
        }
    };

    const calculateTotal = () => {
        const processingFee = formData.payment_method?.processing_fee || 0;
        return orderTotal + processingFee;
    };

    // Render saved payment methods selection
    const renderSavedPaymentMethods = () => {
        if (savedPaymentMethods.length === 0) return null;

        return (
            <Card className="mb-6">
                <CardHeader>
                    <CardTitle className="flex items-center text-base">
                        <BookOpen className="h-4 w-4 mr-2" />
                        Saved Payment Methods
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                        {savedPaymentMethods.map((method) => (
                            <div
                                key={method.id}
                                className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                                    selectedSavedMethodId === method.id
                                        ? 'border-primary bg-primary/5'
                                        : 'border-border hover:border-primary/50'
                                }`}
                                onClick={() => handleSelectSavedPaymentMethod(method)}
                            >
                                <div className="flex items-start justify-between">
                                    <div className="flex-1">
                                        <div className="flex items-center space-x-2 mb-1">
                                            <h4 className="font-medium text-sm">
                                                {method.display_name}
                                            </h4>
                                            {method.is_default && (
                                                <span className="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded">
                                                    Default
                                                </span>
                                            )}
                                            {method.is_verified && (
                                                <span className="text-xs bg-green-100 text-green-600 px-2 py-0.5 rounded">
                                                    Verified
                                                </span>
                                            )}
                                        </div>
                                        {method.label && (
                                            <p className="text-xs text-muted-foreground">
                                                {method.label}
                                            </p>
                                        )}
                                        <p className="text-xs text-muted-foreground">
                                            {method.type.replace('_', ' ').toUpperCase()}
                                        </p>
                                    </div>
                                    <div className={`w-4 h-4 rounded-full border-2 ${
                                        selectedSavedMethodId === method.id
                                            ? 'border-primary bg-primary'
                                            : 'border-muted-foreground'
                                    }`}>
                                        {selectedSavedMethodId === method.id && (
                                            <div className="w-2 h-2 bg-white rounded-full m-0.5" />
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>

                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                            setSelectedSavedMethodId(null);
                            setShowNewPaymentForm(true);
                        }}
                        className="w-full"
                    >
                        <Plus className="h-4 w-4 mr-2" />
                        Add New Payment Method
                    </Button>
                </CardContent>
            </Card>
        );
    };

    return (
        <div className="space-y-6">
            <div>
                <h2 className="text-2xl font-semibold mb-2">Payment Method</h2>
                <p className="text-muted-foreground">
                    Choose how you'd like to pay for your order
                </p>
            </div>

            {/* Saved Payment Methods */}
            {!loadingPaymentMethods && renderSavedPaymentMethods()}

            {/* Payment Methods (show only when adding new or no saved methods selected) */}
            {(!selectedSavedMethodId || showNewPaymentForm) && (
                <div>
                    <h3 className="text-lg font-medium mb-4">
                        {savedPaymentMethods.length > 0 ? 'Or choose a new payment method' : 'Choose a payment method'}
                    </h3>
                    <div className="space-y-3">
                {PAYMENT_METHODS.map((method) => (
                    <div
                        key={method.id}
                        className={`p-4 border rounded-lg cursor-pointer transition-all ${
                            formData.payment_method?.id === method.id
                                ? 'border-primary bg-primary/5'
                                : 'border-border hover:border-primary/50'
                        } ${!method.is_available ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => method.is_available && handlePaymentMethodChange(method)}
                    >
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                                <div className={`w-4 h-4 rounded-full border-2 ${
                                    formData.payment_method?.id === method.id
                                        ? 'border-primary bg-primary'
                                        : 'border-muted-foreground'
                                }`}>
                                    {formData.payment_method?.id === method.id && (
                                        <div className="w-2 h-2 bg-white rounded-full m-0.5" />
                                    )}
                                </div>
                                <div className="text-2xl">{method.icon}</div>
                                <div>
                                    <h4 className="font-medium">{method.name}</h4>
                                    <p className="text-sm text-muted-foreground">{method.description}</p>
                                </div>
                            </div>
                            <div className="text-right">
                                {(method.processing_fee || 0) > 0 && (
                                    <p className="text-sm text-muted-foreground">
                                        +₱{method.processing_fee} fee
                                    </p>
                                )}
                                {method.requires_online_payment && (
                                    <div className="flex items-center gap-1 text-xs text-green-600">
                                        <Shield className="h-3 w-3" />
                                        Secure
                                    </div>
                                )}
                            </div>
                        </div>
                    </div>
                ))}
                    {errors.payment_method && (
                        <p className="text-sm text-destructive">{errors.payment_method}</p>
                    )}
                    </div>
                </div>
            )}

            {/* Payment Details */}
            {renderPaymentDetails()}

            {/* Order Total with Processing Fee */}
            {formData.payment_method && (
                <Card>
                    <CardContent className="p-4">
                        <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                                <span>Order Total</span>
                                <span>₱{orderTotal.toLocaleString()}</span>
                            </div>
                            {(formData.payment_method.processing_fee || 0) > 0 && (
                                <div className="flex justify-between text-sm">
                                    <span>Processing Fee</span>
                                    <span>₱{(formData.payment_method.processing_fee || 0).toLocaleString()}</span>
                                </div>
                            )}
                            <div className="flex justify-between font-semibold border-t pt-2">
                                <span>Total to Pay</span>
                                <span>₱{calculateTotal().toLocaleString()}</span>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-4">
                <Button variant="outline" onClick={onPrev} size="lg" className="px-8">
                    Back to Shipping
                </Button>
                <Button onClick={handleNext} size="lg" className="px-8">
                    Review Order
                </Button>
            </div>
        </div>
    );
};

export default PaymentStep;
