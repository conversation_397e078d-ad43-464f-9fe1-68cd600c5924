import { useCallback, useEffect, useState } from 'react';
import { useCart } from './use-cart';

export type AddToCartState = 'idle' | 'loading' | 'success' | 'error';

interface UseAddToCartAnimationProps {
    productId: number;
    quantity?: number;
    productOptions?: Record<string, unknown>;
    onSuccess?: () => void;
    onError?: (error: string) => void;
}

interface UseAddToCartAnimationReturn {
    state: AddToCartState;
    isLoading: boolean;
    isSuccess: boolean;
    isError: boolean;
    handleAddToCart: () => Promise<void>;
    resetState: () => void;
}

/**
 * Custom hook for managing add to cart animations and states
 * Provides visual feedback states for button animations
 */
export function useAddToCartAnimation({
    productId,
    quantity = 1,
    productOptions,
    onSuccess,
    onError
}: UseAddToCartAnimationProps): UseAddToCartAnimationReturn {
    const { addToCartOptimistic } = useCart();
    const [state, setState] = useState<AddToCartState>('idle');

    // Auto-reset success state after animation duration
    useEffect(() => {
        if (state === 'success') {
            const timer = setTimeout(() => {
                setState('idle');
            }, 2000); // 2 seconds to show success state

            return () => clearTimeout(timer);
        }
    }, [state]);

    // Auto-reset error state after showing error
    useEffect(() => {
        if (state === 'error') {
            const timer = setTimeout(() => {
                setState('idle');
            }, 3000); // 3 seconds to show error state

            return () => clearTimeout(timer);
        }
    }, [state]);

    const handleAddToCart = useCallback(async () => {
        if (state === 'loading') return; // Prevent multiple clicks

        setState('loading');

        try {
            const success = await addToCartOptimistic(productId, quantity, productOptions);
            
            if (success) {
                setState('success');
                onSuccess?.();
            } else {
                setState('error');
                onError?.('Failed to add item to cart');
            }
        } catch (error) {
            setState('error');
            const errorMessage = error instanceof Error ? error.message : 'Failed to add item to cart';
            onError?.(errorMessage);
        }
    }, [addToCartOptimistic, productId, quantity, productOptions, onSuccess, onError, state]);

    const resetState = useCallback(() => {
        setState('idle');
    }, []);

    return {
        state,
        isLoading: state === 'loading',
        isSuccess: state === 'success',
        isError: state === 'error',
        handleAddToCart,
        resetState
    };
}

/**
 * Hook for managing cart badge animations
 * Triggers animation when cart count changes
 */
export function useCartBadgeAnimation() {
    const { cart } = useCart();
    const [isAnimating, setIsAnimating] = useState(false);
    const [previousCount, setPreviousCount] = useState(cart?.total_items || 0);

    useEffect(() => {
        const currentCount = cart?.total_items || 0;
        
        // Trigger animation when count increases
        if (currentCount > previousCount) {
            setIsAnimating(true);
            
            // Reset animation state after animation completes
            const timer = setTimeout(() => {
                setIsAnimating(false);
            }, 600); // Match animation duration

            return () => clearTimeout(timer);
        }
        
        setPreviousCount(currentCount);
    }, [cart?.total_items, previousCount]);

    return {
        isAnimating,
        cartCount: cart?.total_items || 0
    };
}

/**
 * Hook for managing cart drawer button feedback
 * Provides subtle feedback when cart is updated
 */
export function useCartDrawerFeedback() {
    const { cart } = useCart();
    const [isHighlighted, setIsHighlighted] = useState(false);
    const [previousCount, setPreviousCount] = useState(cart?.total_items || 0);

    useEffect(() => {
        const currentCount = cart?.total_items || 0;
        
        // Trigger highlight when count changes
        if (currentCount !== previousCount && currentCount > 0) {
            setIsHighlighted(true);
            
            // Reset highlight state after animation
            const timer = setTimeout(() => {
                setIsHighlighted(false);
            }, 1000); // Brief highlight duration

            return () => clearTimeout(timer);
        }
        
        setPreviousCount(currentCount);
    }, [cart?.total_items, previousCount]);

    return {
        isHighlighted,
        cartCount: cart?.total_items || 0
    };
}

/**
 * Utility function to get button text based on state
 */
export function getAddToCartButtonText(state: AddToCartState): string {
    switch (state) {
        case 'loading':
            return 'Adding...';
        case 'success':
            return 'Added!';
        case 'error':
            return 'Try Again';
        default:
            return 'Add to Cart';
    }
}

/**
 * Utility function to get button classes for animations
 */
export function getAddToCartButtonClasses(state: AddToCartState, baseClasses: string = ''): string {
    const stateClasses = {
        idle: 'transform transition-all duration-300 ease-out hover:scale-105',
        loading: 'cursor-not-allowed opacity-80 transform transition-all duration-200',
        success: 'bg-green-600 hover:bg-green-700 transform scale-105 transition-all duration-400 ease-out shadow-lg',
        error: 'bg-red-600 hover:bg-red-700 animate-pulse transform transition-all duration-300'
    };

    return `${baseClasses} ${stateClasses[state]}`.trim();
}

/**
 * Utility function to get cart badge animation classes
 */
export function getCartBadgeClasses(isAnimating: boolean, baseClasses: string = ''): string {
    const animationClasses = isAnimating
        ? 'transform scale-125 bg-green-600 animate-pulse shadow-lg'
        : 'transform scale-100';

    return `${baseClasses} ${animationClasses} transition-all duration-500 ease-out`.trim();
}

/**
 * Utility function to get cart drawer button feedback classes
 */
export function getCartDrawerButtonClasses(isHighlighted: boolean, baseClasses: string = ''): string {
    const highlightClasses = isHighlighted
        ? 'ring-2 ring-primary ring-opacity-60 bg-primary/25 transform scale-105 shadow-md'
        : 'transform scale-100';

    return `${baseClasses} ${highlightClasses} transition-all duration-400 ease-out`.trim();
}
