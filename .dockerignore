# Dependencies (will be installed during build)
/node_modules
/vendor

# Environment files (will be set via environment variables)
.env
.env.backup
.env.production
.env.local

# Build artifacts that will be generated
/public/hot
/public/build

# Storage and cache
/storage/*.key
/bootstrap/cache/*.php

# Testing and development files
.phpunit.result.cache
Homestead.json
Homestead.yaml
npm-debug.log
yarn-error.log

# Version control and documentation
.git
.gitignore
README.md
docs/

# SSL certificates (excluded only for production builds)
# ssl/ - commented out to allow HTTPS development builds

# Docker files (not needed in container)
docker-compose.yml
.dockerignore
Dockerfile

# IDE and editor files
.editorconfig
.vscode
.idea

# OS files
.DS_Store
Thumbs.db
