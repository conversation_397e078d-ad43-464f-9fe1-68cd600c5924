<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\Product;
use App\Models\Review;
use App\Models\User;
use Illuminate\Database\Seeder;

class ReviewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = Product::all();
        $users = User::where('email', 'not like', '%@luckystar.com')->get();
        $deliveredOrders = Order::where('status', 'delivered')->with('orderItems.product')->get();

        if ($products->isEmpty() || $users->isEmpty()) {
            $this->command->warn('No products or users found. Please run ProductSeeder and UserSeeder first.');
            return;
        }

        // Sample review titles and comments for different ratings
        $reviewData = [
            5 => [
                'titles' => [
                    'Excellent product!',
                    'Perfect!',
                    'Amazing quality',
                    'Highly recommended',
                    'Outstanding!',
                    'Love it!',
                    'Exceeded expectations',
                    'Best purchase ever',
                ],
                'comments' => [
                    'This product exceeded all my expectations. The quality is outstanding and it works perfectly.',
                    'Absolutely love this! Great quality, fast shipping, and exactly as described.',
                    'Perfect product, perfect service. Couldn\'t be happier with my purchase.',
                    'Amazing quality and great value for money. Highly recommend to everyone!',
                    'This is exactly what I was looking for. Excellent build quality and works flawlessly.',
                    'Outstanding product! The attention to detail is impressive and it performs beautifully.',
                    'I\'m so glad I bought this. It\'s even better than I expected and arrived quickly.',
                    'Fantastic product with excellent quality. Will definitely buy from this store again.',
                ],
            ],
            4 => [
                'titles' => [
                    'Very good product',
                    'Great quality',
                    'Satisfied with purchase',
                    'Good value',
                    'Recommended',
                    'Pretty good',
                    'Happy with it',
                    'Good buy',
                ],
                'comments' => [
                    'Very good product overall. Minor issues but nothing major. Would recommend.',
                    'Great quality and good value for money. Delivery was fast and packaging was secure.',
                    'I\'m satisfied with this purchase. It works well and looks good.',
                    'Good product with nice features. A few minor improvements could make it perfect.',
                    'Pretty happy with this. Good quality and reasonable price.',
                    'Works as expected and good build quality. Shipping was quick.',
                    'Nice product, good value. Would consider buying again.',
                    'Solid product with good performance. Minor room for improvement but overall satisfied.',
                ],
            ],
            3 => [
                'titles' => [
                    'It\'s okay',
                    'Average product',
                    'Decent',
                    'Not bad',
                    'Acceptable',
                    'Fair quality',
                    'Could be better',
                    'Mixed feelings',
                ],
                'comments' => [
                    'It\'s an okay product. Does what it\'s supposed to do but nothing special.',
                    'Average quality for the price. Not amazing but not terrible either.',
                    'Decent product but I expected a bit more for the price.',
                    'It works fine but the quality could be better. Acceptable for the price.',
                    'Fair product with some good points and some not so good. Average overall.',
                    'Not bad but not great either. Does the job but could be improved.',
                    'Mixed feelings about this. Some aspects are good, others could be better.',
                    'Acceptable quality but I think there are better options available.',
                ],
            ],
            2 => [
                'titles' => [
                    'Disappointed',
                    'Not great',
                    'Below expectations',
                    'Poor quality',
                    'Not satisfied',
                    'Could be much better',
                    'Not impressed',
                    'Mediocre',
                ],
                'comments' => [
                    'Disappointed with this purchase. The quality is not as good as expected.',
                    'Not great quality for the price. Had some issues right out of the box.',
                    'Below my expectations. The product feels cheap and doesn\'t work as advertised.',
                    'Poor quality materials and construction. Not worth the money.',
                    'Not satisfied with this purchase. Several problems and poor customer service.',
                    'Could be much better. The product has several flaws and limitations.',
                    'Not impressed at all. Expected much better quality for this price.',
                    'Mediocre product with several issues. Would not recommend.',
                ],
            ],
            1 => [
                'titles' => [
                    'Terrible product',
                    'Waste of money',
                    'Very disappointed',
                    'Poor quality',
                    'Don\'t buy this',
                    'Awful',
                    'Complete failure',
                    'Regret buying',
                ],
                'comments' => [
                    'Terrible product that doesn\'t work as advertised. Complete waste of money.',
                    'Very disappointed with this purchase. Poor quality and doesn\'t function properly.',
                    'Don\'t waste your money on this. It broke within days of use.',
                    'Awful quality and terrible customer service. Would not recommend to anyone.',
                    'Complete failure of a product. Nothing works as it should.',
                    'Regret buying this. Poor materials, bad design, and doesn\'t work.',
                    'This product is a joke. Save your money and buy something else.',
                    'Extremely poor quality. Broke immediately and customer service was unhelpful.',
                ],
            ],
        ];

        $reviews = [];

        // Create reviews from delivered orders (verified purchases)
        foreach ($deliveredOrders as $order) {
            // 60% chance to create reviews for delivered orders
            if (rand(0, 100) < 60) {
                foreach ($order->orderItems as $orderItem) {
                    // 40% chance to review each product in the order
                    if (rand(0, 100) < 40) {
                        $rating = [5, 5, 5, 4, 4, 4, 3, 3, 2, 1][array_rand([5, 5, 5, 4, 4, 4, 3, 3, 2, 1])]; // Weighted towards higher ratings
                        $ratingData = $reviewData[$rating];

                        $reviews[] = [
                            'product_id' => $orderItem->product_id,
                            'user_id' => $order->user_id,
                            'order_id' => $order->id,
                            'rating' => $rating,
                            'title' => $ratingData['titles'][array_rand($ratingData['titles'])],
                            'comment' => $ratingData['comments'][array_rand($ratingData['comments'])],
                            'is_approved' => rand(0, 100) < 90, // 90% approved
                            'is_verified_purchase' => true,
                            'images' => rand(0, 100) < 20 ? [
                                '/images/reviews/review-' . rand(1, 100) . '.jpg'
                            ] : null,
                            'created_at' => $order->created_at->addDays(rand(1, 30)),
                        ];
                    }
                }
            }
        }

        // Create additional reviews from users without orders (non-verified purchases)
        $additionalReviewsCount = 20; // Reduced for simplicity
        for ($i = 0; $i < $additionalReviewsCount; $i++) {
            $rating = [5, 5, 4, 4, 4, 3, 3, 2, 1][array_rand([5, 5, 4, 4, 4, 3, 3, 2, 1])]; // Weighted towards higher ratings
            $ratingData = $reviewData[$rating];

            $reviews[] = [
                'product_id' => $products->random()->id,
                'user_id' => $users->random()->id,
                'order_id' => null,
                'rating' => $rating,
                'title' => $ratingData['titles'][array_rand($ratingData['titles'])],
                'comment' => $ratingData['comments'][array_rand($ratingData['comments'])],
                'is_approved' => rand(0, 100) < 85, // 85% approved for non-verified
                'is_verified_purchase' => false,
                'images' => rand(0, 100) < 15 ? [
                    '/images/reviews/review-' . rand(1, 100) . '.jpg'
                ] : null,
                'created_at' => now()->subDays(rand(1, 180)),
            ];
        }

        // Create all reviews
        foreach ($reviews as $reviewData) {
            // Check if review already exists for this user and product
            $existingReview = Review::where('product_id', $reviewData['product_id'])
                                  ->where('user_id', $reviewData['user_id'])
                                  ->first();

            if (!$existingReview) {
                Review::create($reviewData);
            }
        }

        $approvedCount = Review::where('is_approved', true)->count();
        $pendingCount = Review::where('is_approved', false)->count();
        $verifiedCount = Review::where('is_verified_purchase', true)->count();

        $this->command->info("Created reviews: {$approvedCount} approved, {$pendingCount} pending, {$verifiedCount} verified purchases.");
    }
}
