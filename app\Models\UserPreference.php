<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'email_marketing',
        'email_order_updates',
        'email_promotions',
        'sms_marketing',
        'sms_order_updates',
        'push_notifications',
        'language',
        'currency',
        'timezone',
        'preferred_delivery_time',
        'dietary_restrictions',
        'favorite_categories',
        'profile_public',
        'show_order_history',
        'allow_reviews_display',
    ];

    protected $casts = [
        'email_marketing' => 'boolean',
        'email_order_updates' => 'boolean',
        'email_promotions' => 'boolean',
        'sms_marketing' => 'boolean',
        'sms_order_updates' => 'boolean',
        'push_notifications' => 'boolean',
        'dietary_restrictions' => 'array',
        'favorite_categories' => 'array',
        'profile_public' => 'boolean',
        'show_order_history' => 'boolean',
        'allow_reviews_display' => 'boolean',
    ];

    /**
     * Get the user that owns the preferences.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get available languages.
     */
    public static function getAvailableLanguages(): array
    {
        return [
            'en' => 'English',
            'tl' => 'Filipino',
        ];
    }

    /**
     * Get available currencies.
     */
    public static function getAvailableCurrencies(): array
    {
        return [
            'PHP' => 'Philippine Peso',
            'USD' => 'US Dollar',
        ];
    }

    /**
     * Get available timezones.
     */
    public static function getAvailableTimezones(): array
    {
        return [
            'Asia/Manila' => 'Manila (GMT+8)',
            'UTC' => 'UTC (GMT+0)',
        ];
    }

    /**
     * Get available delivery times.
     */
    public static function getAvailableDeliveryTimes(): array
    {
        return [
            'morning' => 'Morning (8AM - 12PM)',
            'afternoon' => 'Afternoon (12PM - 5PM)',
            'evening' => 'Evening (5PM - 8PM)',
            'anytime' => 'Anytime',
        ];
    }

    /**
     * Get available dietary restrictions.
     */
    public static function getAvailableDietaryRestrictions(): array
    {
        return [
            'vegetarian' => 'Vegetarian',
            'vegan' => 'Vegan',
            'halal' => 'Halal',
            'kosher' => 'Kosher',
            'gluten_free' => 'Gluten Free',
            'dairy_free' => 'Dairy Free',
            'nut_free' => 'Nut Free',
        ];
    }

    /**
     * Check if user has specific dietary restriction.
     */
    public function hasDietaryRestriction(string $restriction): bool
    {
        return in_array($restriction, $this->dietary_restrictions ?? []);
    }

    /**
     * Check if category is in favorites.
     */
    public function isFavoriteCategory(int $categoryId): bool
    {
        return in_array($categoryId, $this->favorite_categories ?? []);
    }
}
