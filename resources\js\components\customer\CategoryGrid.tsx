import { Card, CardContent } from '@/components/ui/card';
import React from 'react';

interface Category {
    name: string;
    icon: string;
    count: string;
}

const CategoryGrid: React.FC = () => {
    const categories: Category[] = [
        { name: 'Fresh Produce', icon: '🥕', count: '300+ items' },
        { name: 'Dairy & Eggs', icon: '🥚', count: '85+ items' },
        { name: 'Meat & Seafood', icon: '🐟', count: '150+ items' },
        { name: 'Bakery & Bread', icon: '🥖', count: '75+ items' },
        { name: 'Beverages', icon: '🥤', count: '200+ items' },
        { name: 'Pantry & Dry Goods', icon: '🌾', count: '400+ items' },
        { name: 'Frozen Foods', icon: '🧊', count: '180+ items' },
        { name: 'Snacks & Confectionery', icon: '🍪', count: '250+ items' },
        { name: 'Health & Organic', icon: '🌱', count: '120+ items' },
    ];

    return (
        <section className="bg-background py-16">
            <div className="container mx-auto px-4">
                <div className="mb-12 text-center">
                    <h2 className="font-display mb-4 text-3xl font-bold text-foreground md:text-4xl">Shop by Category</h2>
                    <p className="mx-auto max-w-2xl text-muted-foreground">Discover our wide range of fresh products across different categories</p>
                </div>

                <div className="grid grid-cols-2 gap-6 md:grid-cols-3 lg:grid-cols-6">
                    {categories.map((category, index) => (
                        <Card
                            key={index}
                            className="group cursor-pointer bg-primary/5 transition-all duration-300 hover:-translate-y-1 hover:bg-primary/10 hover:shadow-xl"
                        >
                            <CardContent className="p-6 text-center">
                                <div className="mb-4 text-4xl transition-transform group-hover:scale-110">{category.icon}</div>
                                <h3 className="mb-2 font-semibold text-foreground">{category.name}</h3>
                                <p className="text-sm font-medium text-primary">{category.count}</p>
                            </CardContent>
                        </Card>
                    ))}
                </div>
            </div>
        </section>
    );
};

export default CategoryGrid;
