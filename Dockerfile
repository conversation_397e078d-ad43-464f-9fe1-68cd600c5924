# Use a multi-stage build for better optimization
FROM node:20-alpine AS node-builder

# Set working directory for Node.js build
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install Node.js dependencies and fix security vulnerabilities
RUN npm ci --omit=dev && \
    npm audit fix --force || echo "Some vulnerabilities could not be auto-fixed"

# Copy source files needed for build
COPY . .

# Build the React assets with Vite (optimized for production)
ENV NODE_ENV=production
RUN npm run build

# Main application stage
FROM richarvey/nginx-php-fpm:3.1.6

# Install Node.js in the main container for runtime if needed
RUN apk add --no-cache nodejs npm

# Copy application files
COPY . .

# Copy built assets from node-builder stage
COPY --from=node-builder /app/public/build /var/www/html/public/build

# Copy nginx configuration script
COPY configure-nginx.sh /usr/local/bin/configure-nginx.sh
RUN chmod +x /usr/local/bin/configure-nginx.sh

# Image config
ENV SKIP_COMPOSER=1
ENV WEBROOT=/var/www/html/public
ENV PHP_ERRORS_STDERR=1
ENV RUN_SCRIPTS=1
ENV REAL_IP_HEADER=1

# nginx configuration for Laravel
ENV NGINX_CLIENT_MAX_BODY_SIZE=100M
ENV NGINX_FASTCGI_READ_TIMEOUT=300

# Laravel config
ENV APP_ENV=production
ENV APP_DEBUG=false
ENV LOG_CHANNEL=stderr

# Allow composer to run as root
ENV COMPOSER_ALLOW_SUPERUSER=1

CMD ["/start.sh"]
