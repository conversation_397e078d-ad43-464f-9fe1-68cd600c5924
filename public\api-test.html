<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lucky Star API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
        }
        .endpoint {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .endpoint-url {
            font-family: monospace;
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 3px;
            display: inline-block;
            margin: 5px 0;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .response {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 10px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-left-color: #28a745;
        }
        .error {
            border-left-color: #dc3545;
        }
        .loading {
            color: #6c757d;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌟 Lucky Star API Test Dashboard</h1>
        <p>Test the comprehensive REST API endpoints for the Lucky Star e-commerce platform.</p>
    </div>

    <div class="container">
        <h2>🏥 API Health Check</h2>
        <div class="endpoint">
            <h3>API Health Status</h3>
            <div class="endpoint-url">GET /api/health</div>
            <button onclick="testEndpoint('/api/health', 'health-response')">Test API Health</button>
            <div id="health-response" class="response" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>📦 Products API</h2>
        <div class="endpoint">
            <h3>Get All Products</h3>
            <div class="endpoint-url">GET /api/v1/products</div>
            <button onclick="testEndpoint('/api/v1/products', 'products-response')">Test Products API</button>
            <div id="products-response" class="response" style="display: none;"></div>
        </div>

        <div class="endpoint">
            <h3>Get Single Product</h3>
            <div class="endpoint-url">GET /api/v1/products/1</div>
            <button onclick="testEndpoint('/api/v1/products/1', 'product-single-response')">Test Single Product</button>
            <div id="product-single-response" class="response" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>📂 Categories API</h2>
        <div class="endpoint">
            <h3>Get All Categories</h3>
            <div class="endpoint-url">GET /api/v1/categories</div>
            <button onclick="testEndpoint('/api/v1/categories', 'categories-response')">Test Categories API</button>
            <div id="categories-response" class="response" style="display: none;"></div>
        </div>

        <div class="endpoint">
            <h3>Get Category Products</h3>
            <div class="endpoint-url">GET /api/v1/categories/1/products</div>
            <button onclick="testEndpoint('/api/v1/categories/1/products', 'category-products-response')">Test Category Products</button>
            <div id="category-products-response" class="response" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>⭐ Reviews API</h2>
        <div class="endpoint">
            <h3>Get All Reviews</h3>
            <div class="endpoint-url">GET /api/v1/reviews</div>
            <button onclick="testEndpoint('/api/v1/reviews', 'reviews-response')">Test Reviews API</button>
            <div id="reviews-response" class="response" style="display: none;"></div>
        </div>

        <div class="endpoint">
            <h3>Get Product Reviews</h3>
            <div class="endpoint-url">GET /api/v1/products/1/reviews</div>
            <button onclick="testEndpoint('/api/v1/products/1/reviews', 'product-reviews-response')">Test Product Reviews</button>
            <div id="product-reviews-response" class="response" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>🛒 Shopping Cart API (Guest)</h2>
        <div class="endpoint">
            <h3>Get Cart Items</h3>
            <div class="endpoint-url">GET /api/v1/cart</div>
            <button onclick="testEndpoint('/api/v1/cart', 'cart-response')">Test Cart API</button>
            <div id="cart-response" class="response" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>🎟️ Coupons API</h2>
        <div class="endpoint">
            <h3>Validate Coupon</h3>
            <div class="endpoint-url">POST /api/v1/coupons/validate</div>
            <button onclick="testCouponValidation()">Test Coupon Validation</button>
            <div id="coupon-response" class="response" style="display: none;"></div>
        </div>
    </div>

    <script>
        async function testEndpoint(url, responseId) {
            const responseDiv = document.getElementById(responseId);
            responseDiv.style.display = 'block';
            responseDiv.innerHTML = '<div class="loading">Loading...</div>';
            responseDiv.className = 'response';

            try {
                const response = await fetch(url, {
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    responseDiv.className = 'response success';
                    responseDiv.innerHTML = `
                        <h4>✅ Success (${response.status})</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    responseDiv.className = 'response error';
                    responseDiv.innerHTML = `
                        <h4>❌ Error (${response.status})</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                responseDiv.className = 'response error';
                responseDiv.innerHTML = `
                    <h4>❌ Network Error</h4>
                    <p>${error.message}</p>
                `;
            }
        }

        async function testCouponValidation() {
            const responseDiv = document.getElementById('coupon-response');
            responseDiv.style.display = 'block';
            responseDiv.innerHTML = '<div class="loading">Testing coupon validation...</div>';
            responseDiv.className = 'response';

            try {
                const response = await fetch('/api/v1/coupons/validate', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        code: 'WELCOME10',
                        subtotal: 100.00
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    responseDiv.className = 'response success';
                    responseDiv.innerHTML = `
                        <h4>✅ Success (${response.status})</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    responseDiv.className = 'response error';
                    responseDiv.innerHTML = `
                        <h4>❌ Error (${response.status})</h4>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                responseDiv.className = 'response error';
                responseDiv.innerHTML = `
                    <h4>❌ Network Error</h4>
                    <p>${error.message}</p>
                `;
            }
        }

        // Auto-test the main endpoints on page load
        window.addEventListener('load', function() {
            setTimeout(() => {
                testEndpoint('/api/health', 'health-response');
            }, 200);
            setTimeout(() => {
                testEndpoint('/api/v1/products', 'products-response');
            }, 700);
            setTimeout(() => {
                testEndpoint('/api/v1/categories', 'categories-response');
            }, 1200);
        });
    </script>
</body>
</html>
