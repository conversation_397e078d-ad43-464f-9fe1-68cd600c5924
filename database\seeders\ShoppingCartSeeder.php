<?php

namespace Database\Seeders;

use App\Models\Product;
use App\Models\ShoppingCart;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ShoppingCartSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = Product::where('is_active', true)->get();
        $users = User::where('email', 'not like', '%@luckystar.com')->take(15)->get();

        if ($products->isEmpty() || $users->isEmpty()) {
            $this->command->warn('No active products or users found. Please run ProductSeeder and UserSeeder first.');
            return;
        }

        $cartItems = [];

        // Create cart items for logged-in users
        foreach ($users as $user) {
            // 70% chance this user has items in cart
            if (rand(0, 100) < 70) {
                $itemCount = rand(1, 3);
                
                for ($i = 0; $i < $itemCount; $i++) {
                    $product = $products->random();
                    $quantity = rand(1, 3);

                    // Simplified - no product options for cart items
                    $productOptions = null;

                    // Check if this user already has this product in cart
                    $existingItem = collect($cartItems)->first(function ($item) use ($user, $product) {
                        return $item['user_id'] === $user->id && $item['product_id'] === $product->id;
                    });

                    if (!$existingItem) {
                        $cartItems[] = [
                            'user_id' => $user->id,
                            'session_id' => null,
                            'product_id' => $product->id,
                            'quantity' => $quantity,
                            'product_options' => $productOptions,
                            'created_at' => now()->subDays(rand(1, 7)),
                            'updated_at' => now()->subDays(rand(1, 7)),
                        ];
                    }
                }
            }
        }

        // Create cart items for guest users (session-based)
        $guestSessionCount = 5; // Reduced for simplicity
        for ($i = 0; $i < $guestSessionCount; $i++) {
            $sessionId = Str::random(40);
            $itemCount = rand(1, 2);

            for ($j = 0; $j < $itemCount; $j++) {
                $product = $products->random();
                $quantity = rand(1, 2);

                // Check if this session already has this product in cart
                $existingItem = collect($cartItems)->first(function ($item) use ($sessionId, $product) {
                    return $item['session_id'] === $sessionId && $item['product_id'] === $product->id;
                });

                if (!$existingItem) {
                    $cartItems[] = [
                        'user_id' => null,
                        'session_id' => $sessionId,
                        'product_id' => $product->id,
                        'quantity' => $quantity,
                        'product_options' => null,
                        'created_at' => now()->subDays(rand(1, 3)),
                        'updated_at' => now()->subDays(rand(1, 3)),
                    ];
                }
            }
        }

        // Create some abandoned cart items (older items)
        $abandonedCartCount = 5; // Reduced for simplicity
        for ($i = 0; $i < $abandonedCartCount; $i++) {
            $user = $users->random();
            $product = $products->random();
            $quantity = rand(1, 3);

            $cartItems[] = [
                'user_id' => $user->id,
                'session_id' => null,
                'product_id' => $product->id,
                'quantity' => $quantity,
                'product_options' => null,
                'created_at' => now()->subDays(rand(8, 30)),
                'updated_at' => now()->subDays(rand(8, 30)),
            ];
        }

        // Insert all cart items
        foreach ($cartItems as $cartItem) {
            ShoppingCart::create($cartItem);
        }

        $userCartCount = ShoppingCart::whereNotNull('user_id')->count();
        $guestCartCount = ShoppingCart::whereNotNull('session_id')->count();
        $totalItems = ShoppingCart::sum('quantity');

        $this->command->info("Created shopping cart items: {$userCartCount} for users, {$guestCartCount} for guests, {$totalItems} total items.");
    }
}
