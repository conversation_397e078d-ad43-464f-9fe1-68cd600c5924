import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { Package, Search, Filter, Eye, Download, RotateCcw, Star } from 'lucide-react';
import React, { useState, useMemo } from 'react';

// Import customer components
import Footer from '@/components/customer/Footer';
import Header from '@/components/customer/Header';
import ProductImage from '@/components/customer/ProductImage';
import { CartProviderWrapper } from '@/components/CartProviderWrapper';

// Import UI components
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';


import { type Order, type PaginatedResponse } from '@/types/ecommerce';

interface OrdersPageProps {
    orders: PaginatedResponse<Order>;
}

interface OrdersContentProps {
    orders: PaginatedResponse<Order>;
}

const OrdersContent: React.FC<OrdersContentProps> = ({ orders: initialOrders }) => {
    const [searchQuery, setSearchQuery] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [sortBy, setSortBy] = useState('newest');

    const filteredOrders = useMemo(() => {
        let filtered = initialOrders.data || [];

        // Search filter
        if (searchQuery) {
            filtered = filtered.filter(order =>
                order.order_number.toLowerCase().includes(searchQuery.toLowerCase()) ||
                order.orderItems?.some(item =>
                    item.product_name.toLowerCase().includes(searchQuery.toLowerCase())
                )
            );
        }

        // Status filter
        if (statusFilter !== 'all') {
            filtered = filtered.filter(order => order.status === statusFilter);
        }

        // Sort
        filtered.sort((a, b) => {
            switch (sortBy) {
                case 'newest':
                    return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
                case 'oldest':
                    return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
                case 'amount_high':
                    return b.total_amount - a.total_amount;
                case 'amount_low':
                    return a.total_amount - b.total_amount;
                default:
                    return 0;
            }
        });

        return filtered;
    }, [initialOrders.data, searchQuery, statusFilter, sortBy]);

    const getStatusBadgeVariant = (status: string) => {
        switch (status) {
            case 'pending':
                return 'secondary';
            case 'processing':
                return 'default';
            case 'shipped':
                return 'default';
            case 'delivered':
                return 'default';
            case 'cancelled':
                return 'destructive';
            default:
                return 'secondary';
        }
    };



    const canReorder = (order: Order) => {
        return order.status === 'delivered' && order.orderItems && order.orderItems.length > 0;
    };

    const canReview = (order: Order) => {
        return order.status === 'delivered' && !order.reviews?.length;
    };

    const handleReorder = (order: Order) => {
        // TODO: Implement reorder functionality
        console.log('Reorder:', order.id);
    };

    return (
        <div className="min-h-screen bg-background">
            <Header />
            
            <div className="container mx-auto px-4 py-8">
                {/* Breadcrumb */}
                <div className="flex items-center gap-2 text-sm text-muted-foreground mb-6">
                    <Link href="/" className="hover:text-foreground transition-colors">
                        Home
                    </Link>
                    <span>/</span>
                    <Link href="/account" className="hover:text-foreground transition-colors">
                        Account
                    </Link>
                    <span>/</span>
                    <span className="text-foreground font-medium">Orders</span>
                </div>

                {/* Page Header */}
                <div className="flex items-center justify-between mb-8">
                    <div>
                        <h1 className="text-3xl font-bold">My Orders</h1>
                        <p className="text-muted-foreground">
                            Track and manage your order history
                        </p>
                    </div>
                    <div className="text-right">
                        <p className="text-2xl font-bold">{initialOrders.meta?.total || 0}</p>
                        <p className="text-sm text-muted-foreground">Total Orders</p>
                    </div>
                </div>

                {/* Filters and Search */}
                <Card className="mb-6">
                    <CardContent className="p-6">
                        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div className="space-y-2">
                                <label className="text-sm font-medium">Search Orders</label>
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                                    <Input
                                        placeholder="Order number or product name"
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                            </div>

                            <div className="space-y-2">
                                <label className="text-sm font-medium">Status</label>
                                <Select value={statusFilter} onValueChange={setStatusFilter}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">All Orders</SelectItem>
                                        <SelectItem value="pending">Pending</SelectItem>
                                        <SelectItem value="processing">Processing</SelectItem>
                                        <SelectItem value="shipped">Shipped</SelectItem>
                                        <SelectItem value="delivered">Delivered</SelectItem>
                                        <SelectItem value="cancelled">Cancelled</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <label className="text-sm font-medium">Sort By</label>
                                <Select value={sortBy} onValueChange={setSortBy}>
                                    <SelectTrigger>
                                        <SelectValue />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="newest">Newest First</SelectItem>
                                        <SelectItem value="oldest">Oldest First</SelectItem>
                                        <SelectItem value="amount_high">Highest Amount</SelectItem>
                                        <SelectItem value="amount_low">Lowest Amount</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="flex items-end">
                                <Button
                                    variant="outline"
                                    onClick={() => {
                                        setSearchQuery('');
                                        setStatusFilter('all');
                                        setSortBy('newest');
                                    }}
                                    className="w-full"
                                >
                                    <Filter className="h-4 w-4 mr-2" />
                                    Clear Filters
                                </Button>
                            </div>
                        </div>
                    </CardContent>
                </Card>

                {/* Orders List */}
                {filteredOrders.length === 0 ? (
                    <Card>
                        <CardContent className="p-12 text-center">
                            <Package className="h-16 w-16 text-muted-foreground/50 mx-auto mb-4" />
                            <h3 className="text-xl font-semibold mb-2">No Orders Found</h3>
                            <p className="text-muted-foreground mb-6">
                                {searchQuery || statusFilter !== 'all'
                                    ? 'No orders match your current filters.'
                                    : "You haven't placed any orders yet."
                                }
                            </p>
                            <Link href="/products">
                                <Button>Start Shopping</Button>
                            </Link>
                        </CardContent>
                    </Card>
                ) : (
                    <div className="space-y-6">
                        {filteredOrders.map((order) => (
                            <Card key={order.id}>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div>
                                            <CardTitle className="text-lg">Order #{order.order_number}</CardTitle>
                                            <p className="text-sm text-muted-foreground">
                                                Placed on {new Date(order.created_at).toLocaleDateString('en-PH', {
                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric'
                                                })}
                                            </p>
                                        </div>
                                        <div className="text-right">
                                            <Badge variant={getStatusBadgeVariant(order.status)} className="mb-2">
                                                {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                            </Badge>
                                            <p className="text-lg font-bold">₱{order.total_amount.toLocaleString()}</p>
                                        </div>
                                    </div>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {/* Order Items Preview */}
                                    <div className="space-y-3">
                                        {order.orderItems?.slice(0, 2).map((item) => (
                                            <div key={item.id} className="flex items-center gap-3">
                                                <ProductImage
                                                    src={item.product?.images?.[0] || ''}
                                                    alt={item.product_name}
                                                    className="w-12 h-12 rounded-lg object-cover"
                                                />
                                                <div className="flex-1 min-w-0">
                                                    <p className="font-medium truncate">{item.product_name}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        Qty: {item.quantity} • ₱{item.unit_price.toLocaleString()} each
                                                    </p>
                                                </div>
                                                <p className="font-medium">₱{item.total_price.toLocaleString()}</p>
                                            </div>
                                        ))}
                                        
                                        {order.orderItems && order.orderItems.length > 2 && (
                                            <p className="text-sm text-muted-foreground text-center">
                                                +{order.orderItems.length - 2} more items
                                            </p>
                                        )}
                                    </div>

                                    <Separator />

                                    {/* Order Actions */}
                                    <div className="flex flex-wrap gap-3">
                                        <Link href={`/orders/${order.id}`}>
                                            <Button variant="outline" size="sm">
                                                <Eye className="h-4 w-4 mr-2" />
                                                View Details
                                            </Button>
                                        </Link>
                                        
                                        <Link href={`/orders/track/${order.order_number}`}>
                                            <Button variant="outline" size="sm">
                                                <Package className="h-4 w-4 mr-2" />
                                                Track Order
                                            </Button>
                                        </Link>
                                        
                                        <Button variant="outline" size="sm">
                                            <Download className="h-4 w-4 mr-2" />
                                            Download Receipt
                                        </Button>
                                        
                                        {canReorder(order) && (
                                            <Button 
                                                variant="outline" 
                                                size="sm"
                                                onClick={() => handleReorder(order)}
                                            >
                                                <RotateCcw className="h-4 w-4 mr-2" />
                                                Reorder
                                            </Button>
                                        )}
                                        
                                        {canReview(order) && (
                                            <Button variant="outline" size="sm">
                                                <Star className="h-4 w-4 mr-2" />
                                                Write Review
                                            </Button>
                                        )}
                                    </div>

                                    {/* Delivery Status */}
                                    {order.status === 'shipped' && order.tracking_number && (
                                        <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-lg border border-blue-200 dark:border-blue-800">
                                            <p className="text-sm text-blue-800 dark:text-blue-200">
                                                <strong>Tracking Number:</strong> {order.tracking_number}
                                            </p>
                                        </div>
                                    )}
                                    
                                    {order.status === 'delivered' && order.delivered_at && (
                                        <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded-lg border border-green-200 dark:border-green-800">
                                            <p className="text-sm text-green-800 dark:text-green-200">
                                                <strong>Delivered on:</strong> {new Date(order.delivered_at).toLocaleDateString('en-PH', {
                                                    year: 'numeric',
                                                    month: 'long',
                                                    day: 'numeric',
                                                    hour: '2-digit',
                                                    minute: '2-digit'
                                                })}
                                            </p>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                )}

                {/* Pagination */}
                {initialOrders.meta && initialOrders.meta.last_page > 1 && (
                    <div className="flex justify-center mt-8">
                        <div className="flex items-center gap-2">
                            {initialOrders.links?.prev && (
                                <Link href={initialOrders.links.prev}>
                                    <Button variant="outline" size="sm">Previous</Button>
                                </Link>
                            )}
                            
                            <span className="text-sm text-muted-foreground px-4">
                                Page {initialOrders.meta.current_page} of {initialOrders.meta.last_page}
                            </span>
                            
                            {initialOrders.links?.next && (
                                <Link href={initialOrders.links.next}>
                                    <Button variant="outline" size="sm">Next</Button>
                                </Link>
                            )}
                        </div>
                    </div>
                )}
            </div>
            
            <Footer />
        </div>
    );
};

export default function OrdersPage({ orders }: OrdersPageProps) {
    return (
        <>
            <Head title="My Orders" />
            <CartProviderWrapper>
                <OrdersContent orders={orders} />
            </CartProviderWrapper>
        </>
    );
}
