import { Plus, CreditCard, Smartphone, Building, LoaderCircle } from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { apiClient } from '@/bootstrap';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface PaymentMethod {
    id: number;
    type: string;
    label: string | null;
    display_name: string;
    is_default: boolean;
    is_verified: boolean;
    created_at: string;
}

export default function PaymentMethodsSection() {
    const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    const fetchPaymentMethods = async () => {
        try {
            setLoading(true);
            const response = await apiClient.get('/payment-methods');
            if (response.data.success) {
                setPaymentMethods(response.data.data);
            } else {
                setError('Failed to load payment methods');
            }
        } catch (err) {
            setError('Failed to load payment methods');
            console.error('Error fetching payment methods:', err);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchPaymentMethods();
    }, []);

    const getPaymentIcon = (type: string) => {
        switch (type) {
            case 'credit_card':
                return <CreditCard className="w-5 h-5" />;
            case 'gcash':
            case 'paymaya':
                return <Smartphone className="w-5 h-5" />;
            case 'bpi_online':
            case 'bdo_online':
                return <Building className="w-5 h-5" />;
            default:
                return <CreditCard className="w-5 h-5" />;
        }
    };

    const getTypeColor = (type: string) => {
        switch (type) {
            case 'credit_card':
                return 'bg-blue-100 text-blue-800';
            case 'gcash':
                return 'bg-green-100 text-green-800';
            case 'paymaya':
                return 'bg-orange-100 text-orange-800';
            case 'bpi_online':
                return 'bg-red-100 text-red-800';
            case 'bdo_online':
                return 'bg-blue-100 text-blue-800';
            case 'paypal':
                return 'bg-purple-100 text-purple-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    if (loading) {
        return (
            <Card>
                <CardContent className="p-6">
                    <div className="flex items-center justify-center">
                        <LoaderCircle className="w-6 h-6 animate-spin mr-2" />
                        Loading payment methods...
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (error) {
        return (
            <Card>
                <CardContent className="p-6">
                    <div className="text-center text-red-600">
                        <p>{error}</p>
                        <Button onClick={fetchPaymentMethods} className="mt-2">
                            Try Again
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <div>
                    <CardTitle>Payment Methods</CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">
                        Manage your saved payment methods for faster checkout
                    </p>
                </div>
                <Button>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Payment Method
                </Button>
            </CardHeader>
            <CardContent>
                {paymentMethods.length === 0 ? (
                    <div className="text-center py-8">
                        <CreditCard className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">No payment methods yet</h3>
                        <p className="text-muted-foreground mb-4">
                            Add your payment methods to make checkout faster and easier
                        </p>
                        <Button>
                            <Plus className="w-4 h-4 mr-2" />
                            Add Your First Payment Method
                        </Button>
                    </div>
                ) : (
                    <div className="space-y-4">
                        {paymentMethods.map((method) => (
                            <div
                                key={method.id}
                                className="border rounded-lg p-4 flex items-center justify-between"
                            >
                                <div className="flex items-center space-x-4">
                                    <div className="p-2 bg-muted rounded-lg">
                                        {getPaymentIcon(method.type)}
                                    </div>
                                    <div>
                                        <div className="flex items-center space-x-2 mb-1">
                                            <h4 className="font-medium">{method.display_name}</h4>
                                            {method.is_default && (
                                                <Badge variant="secondary" className="text-xs">
                                                    Default
                                                </Badge>
                                            )}
                                            {method.is_verified && (
                                                <Badge variant="outline" className="text-xs text-green-600">
                                                    Verified
                                                </Badge>
                                            )}
                                        </div>
                                        <div className="flex items-center space-x-2">
                                            <Badge className={getTypeColor(method.type)}>
                                                {method.type.replace('_', ' ').toUpperCase()}
                                            </Badge>
                                            {method.label && (
                                                <Badge variant="outline">{method.label}</Badge>
                                            )}
                                        </div>
                                    </div>
                                </div>
                                <div className="flex space-x-2">
                                    <Button variant="outline" size="sm">
                                        Edit
                                    </Button>
                                    <Button variant="outline" size="sm">
                                        Remove
                                    </Button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
