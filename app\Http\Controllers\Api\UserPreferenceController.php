<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserPreference;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class UserPreferenceController extends Controller
{
    /**
     * Display user's preferences.
     */
    public function show(Request $request): JsonResponse
    {
        $preferences = $request->user()->preferences;

        // Create default preferences if none exist
        if (!$preferences) {
            $preferences = UserPreference::create([
                'user_id' => $request->user()->id,
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => $preferences
        ]);
    }

    /**
     * Update user's preferences.
     */
    public function update(Request $request): JsonResponse
    {
        $validated = $request->validate([
            // Communication preferences
            'email_marketing' => 'boolean',
            'email_order_updates' => 'boolean',
            'email_promotions' => 'boolean',
            'sms_marketing' => 'boolean',
            'sms_order_updates' => 'boolean',
            'push_notifications' => 'boolean',
            
            // Language and locale preferences
            'language' => 'string|in:en,tl',
            'currency' => 'string|in:PHP,USD',
            'timezone' => 'string|in:Asia/Manila,UTC',
            
            // Shopping preferences
            'preferred_delivery_time' => 'nullable|string|in:morning,afternoon,evening,anytime',
            'dietary_restrictions' => 'nullable|array',
            'dietary_restrictions.*' => 'string|in:vegetarian,vegan,halal,kosher,gluten_free,dairy_free,nut_free',
            'favorite_categories' => 'nullable|array',
            'favorite_categories.*' => 'integer|exists:categories,id',
            
            // Privacy preferences
            'profile_public' => 'boolean',
            'show_order_history' => 'boolean',
            'allow_reviews_display' => 'boolean',
        ]);

        $preferences = $request->user()->preferences;

        if (!$preferences) {
            $validated['user_id'] = $request->user()->id;
            $preferences = UserPreference::create($validated);
        } else {
            $preferences->update($validated);
        }

        return response()->json([
            'success' => true,
            'message' => 'Preferences updated successfully',
            'data' => $preferences
        ]);
    }

    /**
     * Get available preference options.
     */
    public function options(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'languages' => UserPreference::getAvailableLanguages(),
                'currencies' => UserPreference::getAvailableCurrencies(),
                'timezones' => UserPreference::getAvailableTimezones(),
                'delivery_times' => UserPreference::getAvailableDeliveryTimes(),
                'dietary_restrictions' => UserPreference::getAvailableDietaryRestrictions(),
            ]
        ]);
    }

    /**
     * Update communication preferences only.
     */
    public function updateCommunication(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'email_marketing' => 'boolean',
            'email_order_updates' => 'boolean',
            'email_promotions' => 'boolean',
            'sms_marketing' => 'boolean',
            'sms_order_updates' => 'boolean',
            'push_notifications' => 'boolean',
        ]);

        $preferences = $request->user()->preferences;

        if (!$preferences) {
            $validated['user_id'] = $request->user()->id;
            $preferences = UserPreference::create($validated);
        } else {
            $preferences->update($validated);
        }

        return response()->json([
            'success' => true,
            'message' => 'Communication preferences updated successfully',
            'data' => $preferences
        ]);
    }

    /**
     * Update privacy preferences only.
     */
    public function updatePrivacy(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'profile_public' => 'boolean',
            'show_order_history' => 'boolean',
            'allow_reviews_display' => 'boolean',
        ]);

        $preferences = $request->user()->preferences;

        if (!$preferences) {
            $validated['user_id'] = $request->user()->id;
            $preferences = UserPreference::create($validated);
        } else {
            $preferences->update($validated);
        }

        return response()->json([
            'success' => true,
            'message' => 'Privacy preferences updated successfully',
            'data' => $preferences
        ]);
    }
}
