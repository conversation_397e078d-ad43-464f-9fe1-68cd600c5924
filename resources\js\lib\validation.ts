/**
 * Validation utilities for Philippine-specific data and common form validation
 */

export interface ValidationResult {
    isValid: boolean;
    message?: string;
}

/**
 * Validate Philippine mobile phone numbers
 * Accepts formats: 09XXXXXXXXX, +639XXXXXXXXX, 639XXXXXXXXX
 */
export function validatePhilippinePhone(phone: string): ValidationResult {
    if (!phone) {
        return { isValid: true }; // Optional field
    }

    // Remove all non-digit characters except +
    const cleaned = phone.replace(/[^\d+]/g, '');
    
    // Check various Philippine phone number formats
    const patterns = [
        /^09\d{9}$/,           // 09XXXXXXXXX
        /^\+639\d{9}$/,        // +639XXXXXXXXX
        /^639\d{9}$/,          // 639XXXXXXXXX
        /^9\d{9}$/,            // 9XXXXXXXXX (without leading 0)
    ];

    const isValid = patterns.some(pattern => pattern.test(cleaned));
    
    return {
        isValid,
        message: isValid ? undefined : 'Please enter a valid Philippine phone number (e.g., 09XX XXX XXXX)'
    };
}

/**
 * Validate email addresses
 */
export function validateEmail(email: string): ValidationResult {
    if (!email) {
        return { isValid: false, message: 'Email is required' };
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const isValid = emailRegex.test(email);

    return {
        isValid,
        message: isValid ? undefined : 'Please enter a valid email address'
    };
}

/**
 * Validate required fields
 */
export function validateRequired(value: string, fieldName: string): ValidationResult {
    const isValid = Boolean(value && value.trim());
    
    return {
        isValid,
        message: isValid ? undefined : `${fieldName} is required`
    };
}

/**
 * Validate password strength
 */
export function validatePassword(password: string): ValidationResult {
    if (!password) {
        return { isValid: false, message: 'Password is required' };
    }

    if (password.length < 8) {
        return { isValid: false, message: 'Password must be at least 8 characters long' };
    }

    // Check for at least one uppercase, one lowercase, and one number
    const hasUppercase = /[A-Z]/.test(password);
    const hasLowercase = /[a-z]/.test(password);
    const hasNumber = /\d/.test(password);

    if (!hasUppercase || !hasLowercase || !hasNumber) {
        return {
            isValid: false,
            message: 'Password must contain at least one uppercase letter, one lowercase letter, and one number'
        };
    }

    return { isValid: true };
}

/**
 * Validate password confirmation
 */
export function validatePasswordConfirmation(password: string, confirmation: string): ValidationResult {
    if (!confirmation) {
        return { isValid: false, message: 'Password confirmation is required' };
    }

    const isValid = password === confirmation;
    
    return {
        isValid,
        message: isValid ? undefined : 'Passwords do not match'
    };
}

/**
 * Validate Philippine postal codes
 */
export function validatePhilippinePostalCode(postalCode: string): ValidationResult {
    if (!postalCode) {
        return { isValid: false, message: 'Postal code is required' };
    }

    // Philippine postal codes are 4 digits
    const isValid = /^\d{4}$/.test(postalCode);
    
    return {
        isValid,
        message: isValid ? undefined : 'Please enter a valid 4-digit postal code'
    };
}

/**
 * Validate credit card numbers (basic Luhn algorithm)
 */
export function validateCreditCard(cardNumber: string): ValidationResult {
    if (!cardNumber) {
        return { isValid: false, message: 'Card number is required' };
    }

    // Remove spaces and non-digits
    const cleaned = cardNumber.replace(/\D/g, '');
    
    if (cleaned.length < 13 || cleaned.length > 19) {
        return { isValid: false, message: 'Please enter a valid card number' };
    }

    // Luhn algorithm
    let sum = 0;
    let isEven = false;
    
    for (let i = cleaned.length - 1; i >= 0; i--) {
        let digit = parseInt(cleaned[i]);
        
        if (isEven) {
            digit *= 2;
            if (digit > 9) {
                digit -= 9;
            }
        }
        
        sum += digit;
        isEven = !isEven;
    }
    
    const isValid = sum % 10 === 0;
    
    return {
        isValid,
        message: isValid ? undefined : 'Please enter a valid card number'
    };
}

/**
 * Validate CVV codes
 */
export function validateCVV(cvv: string, cardType?: string): ValidationResult {
    if (!cvv) {
        return { isValid: false, message: 'CVV is required' };
    }

    const cleaned = cvv.replace(/\D/g, '');
    
    // American Express uses 4 digits, others use 3
    const expectedLength = cardType === 'amex' ? 4 : 3;
    const isValid = cleaned.length === expectedLength;
    
    return {
        isValid,
        message: isValid ? undefined : `Please enter a valid ${expectedLength}-digit CVV`
    };
}

/**
 * Validate expiry date (MM/YY format)
 */
export function validateExpiryDate(month: string, year: string): ValidationResult {
    if (!month || !year) {
        return { isValid: false, message: 'Expiry date is required' };
    }

    const monthNum = parseInt(month);
    const yearNum = parseInt(year);
    
    if (monthNum < 1 || monthNum > 12) {
        return { isValid: false, message: 'Please enter a valid month (01-12)' };
    }

    const currentDate = new Date();
    const currentYear = currentDate.getFullYear() % 100; // Get last 2 digits
    const currentMonth = currentDate.getMonth() + 1;
    
    if (yearNum < currentYear || (yearNum === currentYear && monthNum < currentMonth)) {
        return { isValid: false, message: 'Card has expired' };
    }

    return { isValid: true };
}

/**
 * Format Philippine phone number for display
 */
export function formatPhilippinePhone(phone: string): string {
    if (!phone) return '';
    
    const cleaned = phone.replace(/\D/g, '');
    
    if (cleaned.startsWith('63')) {
        // +63 format
        const number = cleaned.slice(2);
        return `+63 ${number.slice(0, 3)} ${number.slice(3, 6)} ${number.slice(6)}`;
    } else if (cleaned.startsWith('0')) {
        // 0 format
        return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;
    }
    
    return phone;
}

/**
 * Format credit card number for display
 */
export function formatCreditCardNumber(cardNumber: string): string {
    if (!cardNumber) return '';
    
    const cleaned = cardNumber.replace(/\D/g, '');
    const groups = cleaned.match(/.{1,4}/g) || [];
    
    return groups.join(' ').trim();
}

/**
 * Get credit card type from number
 */
export function getCreditCardType(cardNumber: string): string {
    const cleaned = cardNumber.replace(/\D/g, '');
    
    if (/^4/.test(cleaned)) return 'visa';
    if (/^5[1-5]/.test(cleaned)) return 'mastercard';
    if (/^3[47]/.test(cleaned)) return 'amex';
    if (/^35/.test(cleaned)) return 'jcb';
    
    return 'unknown';
}

/**
 * Validate form data with multiple fields
 */
export function validateForm(data: Record<string, unknown>, rules: Record<string, string[]>): Record<string, string> {
    const errors: Record<string, string> = {};
    
    for (const [field, fieldRules] of Object.entries(rules)) {
        const value = data[field];
        
        for (const rule of fieldRules) {
            let result: ValidationResult;
            
            switch (rule) {
                case 'required':
                    result = validateRequired(value, field.replace('_', ' '));
                    break;
                case 'email':
                    result = validateEmail(value);
                    break;
                case 'phone':
                    result = validatePhilippinePhone(value);
                    break;
                case 'postal_code':
                    result = validatePhilippinePostalCode(value);
                    break;
                case 'password':
                    result = validatePassword(value);
                    break;
                case 'credit_card':
                    result = validateCreditCard(value);
                    break;
                default:
                    continue;
            }
            
            if (!result.isValid && result.message) {
                errors[field] = result.message;
                break; // Stop at first error for this field
            }
        }
    }
    
    return errors;
}
