import { Head, Link, usePage } from '@inertiajs/react';
import { Package, MapPin, Clock, CheckCircle, Truck, Home, Search } from 'lucide-react';
import React, { useState } from 'react';

// Import customer components
import Footer from '@/components/customer/Footer';
import Header from '@/components/customer/Header';
import ProductImage from '@/components/customer/ProductImage';
import { CartProviderWrapper } from '@/components/CartProviderWrapper';

// Import UI components
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';


import { type SharedData } from '@/types';
import { type Order } from '@/types/ecommerce';

interface OrderTrackingPageProps {
    order?: Order;
    tracking_token?: string;
}

interface TrackingEvent {
    id: string;
    status: string;
    description: string;
    location?: string;
    timestamp: string;
    is_current: boolean;
    is_completed: boolean;
}

interface OrderTrackingContentProps {
    order?: Order;
    tracking_token?: string;
}

const OrderTrackingContent: React.FC<OrderTrackingContentProps> = ({ order, tracking_token }) => {
    const { auth } = usePage<SharedData>().props;
    const [searchQuery, setSearchQuery] = useState(tracking_token || '');
    const [loading, setLoading] = useState(false);

    // Mock tracking events - in real app, this would come from API
    const trackingEvents: TrackingEvent[] = order ? [
        {
            id: '1',
            status: 'Order Placed',
            description: 'Your order has been successfully placed and is being processed.',
            timestamp: order.created_at,
            is_current: false,
            is_completed: true,
        },
        {
            id: '2',
            status: 'Payment Confirmed',
            description: 'Payment has been confirmed and your order is being prepared.',
            timestamp: order.created_at,
            is_current: false,
            is_completed: order.payment_status === 'paid',
        },
        {
            id: '3',
            status: 'Processing',
            description: 'Your items are being picked and packed for shipment.',
            timestamp: order.created_at,
            is_current: order.status === 'processing',
            is_completed: ['shipped', 'delivered'].includes(order.status),
        },
        {
            id: '4',
            status: 'Shipped',
            description: 'Your order has been shipped and is on its way to you.',
            location: 'Manila Distribution Center',
            timestamp: order.shipped_at || '',
            is_current: order.status === 'shipped',
            is_completed: order.status === 'delivered',
        },
        {
            id: '5',
            status: 'Out for Delivery',
            description: 'Your order is out for delivery and will arrive today.',
            location: 'Local Delivery Hub',
            timestamp: '',
            is_current: false,
            is_completed: order.status === 'delivered',
        },
        {
            id: '6',
            status: 'Delivered',
            description: 'Your order has been successfully delivered.',
            timestamp: order.delivered_at || '',
            is_current: false,
            is_completed: order.status === 'delivered',
        },
    ] : [];

    const handleSearch = async () => {
        if (!searchQuery.trim()) return;
        
        setLoading(true);
        try {
            // In real app, this would make an API call
            window.location.href = `/orders/track/${searchQuery}`;
        } catch (error) {
            console.error('Error tracking order:', error);
        } finally {
            setLoading(false);
        }
    };

    const getStatusIcon = (event: TrackingEvent) => {
        if (event.is_completed) {
            return <CheckCircle className="h-5 w-5 text-green-600" />;
        } else if (event.is_current) {
            return <Clock className="h-5 w-5 text-primary animate-pulse" />;
        } else {
            return <div className="w-5 h-5 rounded-full border-2 border-muted-foreground/30" />;
        }
    };

    const getEstimatedDelivery = () => {
        if (!order) return null;
        
        const orderDate = new Date(order.created_at);
        const estimatedDate = new Date(orderDate);
        estimatedDate.setDate(orderDate.getDate() + 5); // Add 5 days
        
        return estimatedDate.toLocaleDateString('en-PH', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    return (
        <div className="min-h-screen bg-background">
            <Header />
            
            <div className="container mx-auto px-4 py-8">
                {/* Breadcrumb */}
                <div className="flex items-center gap-2 text-sm text-muted-foreground mb-6">
                    <Link href="/" className="hover:text-foreground transition-colors">
                        Home
                    </Link>
                    <span>/</span>
                    <span className="text-foreground font-medium">Order Tracking</span>
                </div>

                {/* Page Header */}
                <div className="text-center mb-8">
                    <h1 className="text-3xl font-bold mb-2">Track Your Order</h1>
                    <p className="text-muted-foreground">
                        Enter your order number or tracking token to see the latest updates
                    </p>
                </div>

                {!order ? (
                    /* Search Form */
                    <div className="max-w-md mx-auto mb-8">
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-center">Find Your Order</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="search">Order Number or Tracking Token</Label>
                                    <Input
                                        id="search"
                                        placeholder="e.g., ORD-2024-001 or track_abc123"
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                        onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                                    />
                                </div>
                                <Button 
                                    onClick={handleSearch} 
                                    className="w-full"
                                    disabled={loading || !searchQuery.trim()}
                                >
                                    {loading ? (
                                        <>
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                            Searching...
                                        </>
                                    ) : (
                                        <>
                                            <Search className="h-4 w-4 mr-2" />
                                            Track Order
                                        </>
                                    )}
                                </Button>
                                
                                {!auth?.user && (
                                    <div className="text-center pt-4">
                                        <p className="text-sm text-muted-foreground mb-2">
                                            Have an account?
                                        </p>
                                        <Link href="/login">
                                            <Button variant="outline" size="sm">
                                                Sign in to view all orders
                                            </Button>
                                        </Link>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                ) : (
                    /* Order Tracking Details */
                    <div className="max-w-4xl mx-auto space-y-6">
                        {/* Order Header */}
                        <Card>
                            <CardHeader>
                                <div className="flex items-center justify-between">
                                    <div>
                                        <CardTitle className="text-xl">Order #{order.order_number}</CardTitle>
                                        <p className="text-muted-foreground">
                                            Placed on {new Date(order.created_at).toLocaleDateString('en-PH', {
                                                year: 'numeric',
                                                month: 'long',
                                                day: 'numeric'
                                            })}
                                        </p>
                                    </div>
                                    <Badge variant="default" className="text-sm">
                                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                    </Badge>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                    <div className="text-center">
                                        <div className="text-2xl font-bold text-primary">₱{order.total_amount.toLocaleString()}</div>
                                        <p className="text-sm text-muted-foreground">Total Amount</p>
                                    </div>
                                    <div className="text-center">
                                        <div className="text-2xl font-bold">{order.orderItems?.length || 0}</div>
                                        <p className="text-sm text-muted-foreground">Items</p>
                                    </div>
                                    <div className="text-center">
                                        <div className="text-lg font-bold">{getEstimatedDelivery()}</div>
                                        <p className="text-sm text-muted-foreground">Estimated Delivery</p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>

                        {/* Tracking Timeline */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <Package className="h-5 w-5" />
                                    Tracking Timeline
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-6">
                                    {trackingEvents.map((event, index) => (
                                        <div key={event.id} className="flex gap-4">
                                            <div className="flex flex-col items-center">
                                                {getStatusIcon(event)}
                                                {index < trackingEvents.length - 1 && (
                                                    <div className={`w-0.5 h-12 mt-2 ${
                                                        event.is_completed ? 'bg-green-600' : 'bg-muted-foreground/30'
                                                    }`} />
                                                )}
                                            </div>
                                            <div className="flex-1 pb-6">
                                                <div className="flex items-center justify-between mb-1">
                                                    <h4 className={`font-medium ${
                                                        event.is_current ? 'text-primary' : 
                                                        event.is_completed ? 'text-foreground' : 'text-muted-foreground'
                                                    }`}>
                                                        {event.status}
                                                    </h4>
                                                    {event.timestamp && (
                                                        <span className="text-sm text-muted-foreground">
                                                            {new Date(event.timestamp).toLocaleDateString('en-PH', {
                                                                month: 'short',
                                                                day: 'numeric',
                                                                hour: '2-digit',
                                                                minute: '2-digit'
                                                            })}
                                                        </span>
                                                    )}
                                                </div>
                                                <p className="text-sm text-muted-foreground mb-1">
                                                    {event.description}
                                                </p>
                                                {event.location && (
                                                    <p className="text-sm text-muted-foreground flex items-center gap-1">
                                                        <MapPin className="h-3 w-3" />
                                                        {event.location}
                                                    </p>
                                                )}
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Delivery Information */}
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Home className="h-5 w-5" />
                                        Delivery Address
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-2">
                                        <p className="font-medium">
                                            {order.shipping_first_name} {order.shipping_last_name}
                                        </p>
                                        {order.shipping_company && (
                                            <p className="text-sm text-muted-foreground">{order.shipping_company}</p>
                                        )}
                                        <p className="text-sm">
                                            {[
                                                order.shipping_address_line_1,
                                                order.shipping_address_line_2,
                                                order.shipping_city,
                                                order.shipping_state,
                                                order.shipping_postal_code,
                                                order.shipping_country
                                            ].filter(Boolean).join(', ')}
                                        </p>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Truck className="h-5 w-5" />
                                        Shipping Details
                                    </CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="space-y-3">
                                        <div>
                                            <p className="text-sm font-medium">Shipping Method</p>
                                            <p className="text-sm text-muted-foreground">
                                                {order.shipping_method || 'Standard Delivery'}
                                            </p>
                                        </div>
                                        {order.tracking_number && (
                                            <div>
                                                <p className="text-sm font-medium">Tracking Number</p>
                                                <p className="text-sm text-muted-foreground font-mono">
                                                    {order.tracking_number}
                                                </p>
                                            </div>
                                        )}
                                        <div>
                                            <p className="text-sm font-medium">Carrier</p>
                                            <p className="text-sm text-muted-foreground">
                                                LBC Express
                                            </p>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>

                        {/* Order Items */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Order Items</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {order.orderItems?.map((item) => (
                                    <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg">
                                        <div className="flex-shrink-0">
                                            <ProductImage
                                                src={item.product?.images?.[0] || ''}
                                                alt={item.product_name}
                                                className="w-16 h-16 rounded-lg object-cover"
                                            />
                                        </div>
                                        <div className="flex-1 min-w-0">
                                            <h4 className="font-medium truncate">{item.product_name}</h4>
                                            <p className="text-sm text-muted-foreground">SKU: {item.product_sku}</p>
                                            <p className="text-sm text-muted-foreground">Quantity: {item.quantity}</p>
                                        </div>
                                        <div className="text-right">
                                            <p className="font-medium">₱{item.total_price.toLocaleString()}</p>
                                        </div>
                                    </div>
                                ))}
                            </CardContent>
                        </Card>

                        {/* Actions */}
                        <div className="flex gap-4">
                            <Link href="/products" className="flex-1">
                                <Button variant="outline" className="w-full">
                                    Continue Shopping
                                </Button>
                            </Link>
                            {auth?.user && (
                                <Link href="/account/orders" className="flex-1">
                                    <Button className="w-full">
                                        View All Orders
                                    </Button>
                                </Link>
                            )}
                        </div>
                    </div>
                )}
            </div>
            
            <Footer />
        </div>
    );
};

export default function OrderTrackingPage({ order, tracking_token }: OrderTrackingPageProps) {
    return (
        <>
            <Head title="Order Tracking" />
            <CartProviderWrapper>
                <OrderTrackingContent order={order} tracking_token={tracking_token} />
            </CartProviderWrapper>
        </>
    );
}
