<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\ShoppingCart;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Str;

class ShoppingCartController extends Controller
{
    /**
     * Display shopping cart items (handles both guest and authenticated users).
     */
    public function index(Request $request): JsonResponse
    {
        // Check if user is authenticated
        if ($request->user()) {
            // Return authenticated user's cart
            return $this->userCart($request);
        }

        // Handle guest cart
        $sessionId = $request->session()->getId();

        $cartItems = ShoppingCart::with('product')
            ->where('session_id', $sessionId)
            ->whereNull('user_id')
            ->get();

        $total = $cartItems->sum(function ($item) {
            return $item->quantity * $item->product->price;
        });

        return response()->json([
            'success' => true,
            'data' => [
                'items' => $cartItems,
                'total_items' => $cartItems->sum('quantity'),
                'subtotal' => $total,
            ]
        ]);
    }

    /**
     * Display authenticated user's shopping cart.
     */
    public function userCart(Request $request): JsonResponse
    {
        $cartItems = ShoppingCart::with('product')
            ->where('user_id', $request->user()->id)
            ->get();

        $total = $cartItems->sum(function ($item) {
            return $item->quantity * $item->product->price;
        });

        return response()->json([
            'success' => true,
            'data' => [
                'items' => $cartItems,
                'total_items' => $cartItems->sum('quantity'),
                'subtotal' => $total,
            ]
        ]);
    }

    /**
     * Add item to cart (handles both guest and authenticated users).
     */
    public function store(Request $request): JsonResponse
    {
        // Check if user is authenticated
        if ($request->user()) {
            // Use authenticated user's cart method
            return $this->addToUserCart($request);
        }

        // Handle guest cart
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1|max:99',
            'product_options' => 'nullable|array',
        ]);

        $product = Product::findOrFail($validated['product_id']);

        // Check if product is active
        if (!$product->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Product is not available'
            ], 422);
        }

        // Check stock availability
        if ($product->track_inventory && $product->stock_quantity < $validated['quantity']) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient stock available',
                'available_stock' => $product->stock_quantity
            ], 422);
        }

        $sessionId = $request->session()->getId();

        // Check if item already exists in cart
        $existingItem = ShoppingCart::where('session_id', $sessionId)
            ->where('product_id', $validated['product_id'])
            ->whereNull('user_id')
            ->first();

        if ($existingItem) {
            // Update quantity
            $newQuantity = $existingItem->quantity + $validated['quantity'];
            
            // Check stock for new quantity
            if ($product->track_inventory && $product->stock_quantity < $newQuantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient stock for requested quantity',
                    'available_stock' => $product->stock_quantity,
                    'current_cart_quantity' => $existingItem->quantity
                ], 422);
            }

            $existingItem->update([
                'quantity' => $newQuantity,
                'product_options' => $validated['product_options'] ?? $existingItem->product_options,
            ]);

            $cartItem = $existingItem;
        } else {
            // Create new cart item
            $cartItem = ShoppingCart::create([
                'session_id' => $sessionId,
                'product_id' => $validated['product_id'],
                'quantity' => $validated['quantity'],
                'product_options' => $validated['product_options'] ?? null,
            ]);
        }

        $cartItem->load('product');

        return response()->json([
            'success' => true,
            'message' => 'Item added to cart successfully',
            'data' => $cartItem
        ], 201);
    }

    /**
     * Add item to authenticated user's cart.
     */
    public function addToUserCart(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1|max:99',
            'product_options' => 'nullable|array',
        ]);

        $product = Product::findOrFail($validated['product_id']);

        // Check if product is active
        if (!$product->is_active) {
            return response()->json([
                'success' => false,
                'message' => 'Product is not available'
            ], 422);
        }

        // Check stock availability
        if ($product->track_inventory && $product->stock_quantity < $validated['quantity']) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient stock available',
                'available_stock' => $product->stock_quantity
            ], 422);
        }

        // Check if item already exists in cart
        $existingItem = ShoppingCart::where('user_id', $request->user()->id)
            ->where('product_id', $validated['product_id'])
            ->first();

        if ($existingItem) {
            // Update quantity
            $newQuantity = $existingItem->quantity + $validated['quantity'];
            
            // Check stock for new quantity
            if ($product->track_inventory && $product->stock_quantity < $newQuantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Insufficient stock for requested quantity',
                    'available_stock' => $product->stock_quantity,
                    'current_cart_quantity' => $existingItem->quantity
                ], 422);
            }

            $existingItem->update([
                'quantity' => $newQuantity,
                'product_options' => $validated['product_options'] ?? $existingItem->product_options,
            ]);

            $cartItem = $existingItem;
        } else {
            // Create new cart item
            $cartItem = ShoppingCart::create([
                'user_id' => $request->user()->id,
                'product_id' => $validated['product_id'],
                'quantity' => $validated['quantity'],
                'product_options' => $validated['product_options'] ?? null,
            ]);
        }

        $cartItem->load('product');

        return response()->json([
            'success' => true,
            'message' => 'Item added to cart successfully',
            'data' => $cartItem
        ], 201);
    }

    /**
     * Update cart item quantity (handles both guest and authenticated users).
     */
    public function update(Request $request, ShoppingCart $cartItem): JsonResponse
    {
        // Check if user is authenticated
        if ($request->user()) {
            // Use authenticated user's cart method
            return $this->updateUserCart($request, $cartItem);
        }

        // Handle guest cart
        $sessionId = $request->session()->getId();

        // Ensure cart item belongs to current session
        if ($cartItem->session_id !== $sessionId || $cartItem->user_id !== null) {
            return response()->json([
                'success' => false,
                'message' => 'Cart item not found'
            ], 404);
        }

        $validated = $request->validate([
            'quantity' => 'required|integer|min:1|max:99',
            'product_options' => 'nullable|array',
        ]);

        // Check stock availability
        $product = $cartItem->product;
        if ($product->track_inventory && $product->stock_quantity < $validated['quantity']) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient stock available',
                'available_stock' => $product->stock_quantity
            ], 422);
        }

        $cartItem->update($validated);
        $cartItem->load('product');

        return response()->json([
            'success' => true,
            'message' => 'Cart item updated successfully',
            'data' => $cartItem
        ]);
    }

    /**
     * Update authenticated user's cart item.
     */
    public function updateUserCart(Request $request, ShoppingCart $cartItem): JsonResponse
    {
        // Ensure cart item belongs to current user
        if ($cartItem->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Cart item not found'
            ], 404);
        }

        $validated = $request->validate([
            'quantity' => 'required|integer|min:1|max:99',
            'product_options' => 'nullable|array',
        ]);

        // Check stock availability
        $product = $cartItem->product;
        if ($product->track_inventory && $product->stock_quantity < $validated['quantity']) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient stock available',
                'available_stock' => $product->stock_quantity
            ], 422);
        }

        $cartItem->update($validated);
        $cartItem->load('product');

        return response()->json([
            'success' => true,
            'message' => 'Cart item updated successfully',
            'data' => $cartItem
        ]);
    }

    /**
     * Remove item from cart (handles both guest and authenticated users).
     */
    public function destroy(Request $request, ShoppingCart $cartItem): JsonResponse
    {
        // Check if user is authenticated
        if ($request->user()) {
            // Use authenticated user's cart method
            return $this->removeFromUserCart($request, $cartItem);
        }

        // Handle guest cart
        $sessionId = $request->session()->getId();

        // Ensure cart item belongs to current session
        if ($cartItem->session_id !== $sessionId || $cartItem->user_id !== null) {
            return response()->json([
                'success' => false,
                'message' => 'Cart item not found'
            ], 404);
        }

        $cartItem->delete();

        return response()->json([
            'success' => true,
            'message' => 'Item removed from cart successfully'
        ]);
    }

    /**
     * Remove item from authenticated user's cart.
     */
    public function removeFromUserCart(Request $request, ShoppingCart $cartItem): JsonResponse
    {
        // Ensure cart item belongs to current user
        if ($cartItem->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Cart item not found'
            ], 404);
        }

        $cartItem->delete();

        return response()->json([
            'success' => true,
            'message' => 'Item removed from cart successfully'
        ]);
    }

    /**
     * Clear cart (handles both guest and authenticated users).
     */
    public function clear(Request $request): JsonResponse
    {
        // Check if user is authenticated
        if ($request->user()) {
            // Use authenticated user's cart method
            return $this->clearUserCart($request);
        }

        // Handle guest cart
        $sessionId = $request->session()->getId();

        $deletedCount = ShoppingCart::where('session_id', $sessionId)
            ->whereNull('user_id')
            ->delete();

        return response()->json([
            'success' => true,
            'message' => "Cart cleared successfully. {$deletedCount} items removed."
        ]);
    }

    /**
     * Clear authenticated user's cart.
     */
    public function clearUserCart(Request $request): JsonResponse
    {
        $deletedCount = ShoppingCart::where('user_id', $request->user()->id)
            ->delete();

        return response()->json([
            'success' => true,
            'message' => "Cart cleared successfully. {$deletedCount} items removed."
        ]);
    }

    /**
     * Transfer guest cart items to authenticated user's cart.
     */
    public function transferGuestCart(Request $request): JsonResponse
    {
        $sessionId = $request->session()->getId();
        $userId = $request->user()->id;

        // Check if localStorage cart items are provided
        if ($request->has('guest_cart_items')) {
            return $this->transferLocalStorageCart($request);
        }

        // Fallback: Get guest cart items from session (existing functionality)
        $guestCartItems = ShoppingCart::with('product')
            ->where('session_id', $sessionId)
            ->whereNull('user_id')
            ->get();

        if ($guestCartItems->isEmpty()) {
            return response()->json([
                'success' => true,
                'message' => 'No guest cart items to transfer',
                'transferred_count' => 0
            ]);
        }

        $transferredCount = 0;
        $errors = [];

        foreach ($guestCartItems as $guestItem) {
            try {
                // Check if user already has this product in their cart
                $existingUserItem = ShoppingCart::where('user_id', $userId)
                    ->where('product_id', $guestItem->product_id)
                    ->first();

                if ($existingUserItem) {
                    // Merge quantities
                    $newQuantity = $existingUserItem->quantity + $guestItem->quantity;

                    // Check stock availability
                    if ($guestItem->product->track_inventory &&
                        $guestItem->product->stock_quantity < $newQuantity) {
                        $errors[] = "Insufficient stock for {$guestItem->product->name}. Available: {$guestItem->product->stock_quantity}";
                        continue;
                    }

                    $existingUserItem->update([
                        'quantity' => $newQuantity,
                        'product_options' => $guestItem->product_options ?? $existingUserItem->product_options,
                    ]);
                } else {
                    // Check stock availability for new item
                    if ($guestItem->product->track_inventory &&
                        $guestItem->product->stock_quantity < $guestItem->quantity) {
                        $errors[] = "Insufficient stock for {$guestItem->product->name}. Available: {$guestItem->product->stock_quantity}";
                        continue;
                    }

                    // Create new user cart item
                    ShoppingCart::create([
                        'user_id' => $userId,
                        'product_id' => $guestItem->product_id,
                        'quantity' => $guestItem->quantity,
                        'product_options' => $guestItem->product_options,
                    ]);
                }

                // Remove guest cart item
                $guestItem->delete();
                $transferredCount++;

            } catch (\Exception $e) {
                $errors[] = "Failed to transfer {$guestItem->product->name}: " . $e->getMessage();
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Cart transfer completed. {$transferredCount} items transferred.",
            'transferred_count' => $transferredCount,
            'errors' => $errors
        ]);
    }

    /**
     * Transfer localStorage guest cart items to authenticated user's cart.
     */
    private function transferLocalStorageCart(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'guest_cart_items' => 'required|array|max:50',
            'guest_cart_items.*.product_id' => 'required|integer|exists:products,id',
            'guest_cart_items.*.quantity' => 'required|integer|min:1|max:99',
            'guest_cart_items.*.product_options' => 'nullable|array',
        ]);

        $userId = $request->user()->id;
        $guestCartItems = $validated['guest_cart_items'];

        if (empty($guestCartItems)) {
            return response()->json([
                'success' => true,
                'message' => 'No guest cart items to transfer',
                'transferred_count' => 0
            ]);
        }

        $transferredCount = 0;
        $errors = [];

        foreach ($guestCartItems as $guestItem) {
            try {
                $product = Product::findOrFail($guestItem['product_id']);

                // Check if product is still active
                if (!$product->is_active) {
                    $errors[] = "Product {$product->name} is no longer available";
                    continue;
                }

                // Check if user already has this product in their cart
                $existingUserItem = ShoppingCart::where('user_id', $userId)
                    ->where('product_id', $guestItem['product_id'])
                    ->first();

                if ($existingUserItem) {
                    // Merge quantities
                    $newQuantity = $existingUserItem->quantity + $guestItem['quantity'];

                    // Check stock availability
                    if ($product->track_inventory && $product->stock_quantity < $newQuantity) {
                        $errors[] = "Insufficient stock for {$product->name}. Available: {$product->stock_quantity}";
                        continue;
                    }

                    $existingUserItem->update([
                        'quantity' => $newQuantity,
                        'product_options' => $guestItem['product_options'] ?? $existingUserItem->product_options,
                    ]);
                } else {
                    // Check stock availability for new item
                    if ($product->track_inventory && $product->stock_quantity < $guestItem['quantity']) {
                        $errors[] = "Insufficient stock for {$product->name}. Available: {$product->stock_quantity}";
                        continue;
                    }

                    // Create new user cart item
                    ShoppingCart::create([
                        'user_id' => $userId,
                        'product_id' => $guestItem['product_id'],
                        'quantity' => $guestItem['quantity'],
                        'product_options' => $guestItem['product_options'],
                    ]);
                }

                $transferredCount++;

            } catch (\Exception $e) {
                $errors[] = "Failed to transfer product ID {$guestItem['product_id']}: " . $e->getMessage();
            }
        }

        return response()->json([
            'success' => true,
            'message' => "Cart transfer completed. {$transferredCount} items transferred.",
            'transferred_count' => $transferredCount,
            'errors' => $errors
        ]);
    }

    /**
     * Admin view of all shopping carts.
     */
    public function adminIndex(Request $request): JsonResponse
    {
        $query = ShoppingCart::with(['user', 'product'])
            ->orderBy('created_at', 'desc');

        // Filter by user
        if ($request->has('user_id')) {
            $query->where('user_id', $request->get('user_id'));
        }

        // Filter by product
        if ($request->has('product_id')) {
            $query->where('product_id', $request->get('product_id'));
        }

        // Filter by cart type (user vs guest)
        if ($request->has('cart_type')) {
            if ($request->get('cart_type') === 'user') {
                $query->whereNotNull('user_id');
            } elseif ($request->get('cart_type') === 'guest') {
                $query->whereNull('user_id');
            }
        }

        $perPage = min($request->get('per_page', 20), 100);
        $cartItems = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $cartItems->items(),
            'meta' => [
                'current_page' => $cartItems->currentPage(),
                'last_page' => $cartItems->lastPage(),
                'per_page' => $cartItems->perPage(),
                'total' => $cartItems->total(),
            ]
        ]);
    }
}
