Questions:
Can you describe the data flow in your project? How did your React front-end make requests to and receive data from your Laravel back-end API?

How did you handle user authentication and authorization? Explain the roles of Laravel Sanctum (or Passport) and how you managed tokens or session state on the React side.

Explain how you used Lara<PERSON>'s Eloquent ORM to interact with your PostgreSQL database. Can you give an example of a relationship you defined between models, like a hasMany or belongsTo?

Describe your process for managing database schema changes. How did you use Laravel migrations to update your PostgreSQL database structure in a team environment?

What do you see as the primary advantages and disadvantages of using a separate back-end framework (Laravel) and front-end library (React) compared to a full-stack framework?