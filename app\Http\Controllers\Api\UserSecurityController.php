<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\UserSecuritySetting;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules\Password;

class UserSecurityController extends Controller
{
    /**
     * Display user's security settings.
     */
    public function show(Request $request): JsonResponse
    {
        $securitySettings = $request->user()->securitySettings;

        // Create default security settings if none exist
        if (!$securitySettings) {
            $securitySettings = UserSecuritySetting::create([
                'user_id' => $request->user()->id,
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => $securitySettings
        ]);
    }

    /**
     * Update user's security settings.
     */
    public function update(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'login_notifications' => 'boolean',
            'logout_other_devices' => 'boolean',
            'session_timeout_minutes' => 'integer|min:15|max:480', // 15 minutes to 8 hours
        ]);

        $securitySettings = $request->user()->securitySettings;

        if (!$securitySettings) {
            $validated['user_id'] = $request->user()->id;
            $securitySettings = UserSecuritySetting::create($validated);
        } else {
            $securitySettings->update($validated);
        }

        return response()->json([
            'success' => true,
            'message' => 'Security settings updated successfully',
            'data' => $securitySettings
        ]);
    }

    /**
     * Change user password.
     */
    public function changePassword(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'current_password' => 'required|string',
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        $user = $request->user();

        // Verify current password
        if (!Hash::check($validated['current_password'], $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Current password is incorrect',
                'errors' => ['current_password' => ['Current password is incorrect']]
            ], 422);
        }

        // Update password
        $user->update([
            'password' => Hash::make($validated['password'])
        ]);

        // Update security settings
        $securitySettings = $user->securitySettings;
        if ($securitySettings) {
            $securitySettings->update([
                'password_changed_at' => now(),
                'failed_login_attempts' => 0,
                'locked_until' => null,
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Password changed successfully'
        ]);
    }

    /**
     * Enable two-factor authentication.
     */
    public function enableTwoFactor(Request $request): JsonResponse
    {
        $user = $request->user();
        $securitySettings = $user->securitySettings;

        if (!$securitySettings) {
            $securitySettings = UserSecuritySetting::create([
                'user_id' => $user->id,
            ]);
        }

        if ($securitySettings->two_factor_enabled) {
            return response()->json([
                'success' => false,
                'message' => 'Two-factor authentication is already enabled'
            ], 422);
        }

        // In a real implementation, you would:
        // 1. Generate a secret key
        // 2. Generate QR code for authenticator app
        // 3. Require user to verify with a code before enabling

        $secret = 'TEMP_SECRET_' . $user->id; // Placeholder
        $recoveryCodes = $securitySettings->generateRecoveryCodes();

        $securitySettings->update([
            'two_factor_enabled' => true,
            'two_factor_secret' => $secret,
            'two_factor_confirmed_at' => now(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Two-factor authentication enabled successfully',
            'data' => [
                'recovery_codes' => $recoveryCodes,
                'qr_code_url' => null, // Would generate QR code in real implementation
            ]
        ]);
    }

    /**
     * Disable two-factor authentication.
     */
    public function disableTwoFactor(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'password' => 'required|string',
        ]);

        $user = $request->user();

        // Verify password
        if (!Hash::check($validated['password'], $user->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Password is incorrect',
                'errors' => ['password' => ['Password is incorrect']]
            ], 422);
        }

        $securitySettings = $user->securitySettings;
        if ($securitySettings) {
            $securitySettings->update([
                'two_factor_enabled' => false,
                'two_factor_secret' => null,
                'two_factor_recovery_codes' => null,
                'two_factor_confirmed_at' => null,
            ]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Two-factor authentication disabled successfully'
        ]);
    }

    /**
     * Get trusted devices.
     */
    public function trustedDevices(Request $request): JsonResponse
    {
        $securitySettings = $request->user()->securitySettings;
        $devices = $securitySettings ? $securitySettings->trusted_devices : [];

        return response()->json([
            'success' => true,
            'data' => $devices ?? []
        ]);
    }

    /**
     * Remove trusted device.
     */
    public function removeTrustedDevice(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'device_fingerprint' => 'required|string',
        ]);

        $securitySettings = $request->user()->securitySettings;
        if ($securitySettings) {
            $securitySettings->removeTrustedDevice($validated['device_fingerprint']);
        }

        return response()->json([
            'success' => true,
            'message' => 'Trusted device removed successfully'
        ]);
    }

    /**
     * Get login activity (placeholder for future implementation).
     */
    public function loginActivity(Request $request): JsonResponse
    {
        // In a real implementation, you would track login sessions
        return response()->json([
            'success' => true,
            'data' => [
                'current_session' => [
                    'ip_address' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                    'login_time' => now(),
                ],
                'recent_logins' => []
            ]
        ]);
    }
}
