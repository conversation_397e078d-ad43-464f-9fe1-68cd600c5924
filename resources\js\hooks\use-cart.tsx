import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import axios from '../bootstrap';
import {
    getGuestCartFromStorage,
    clearGuestCartFromStorage,
    addItemToGuestCart,
    updateGuestCartItem,
    removeItemFromGuestCart,
    isLocalStorageAvailable,
    convertGuestCartForTransfer,
    type LocalStorageCartData
} from '@/utils/localStorage-cart';

interface CartItem {
    id: number | string; // Support both API (number) and localStorage (string) IDs
    product_id: number;
    quantity: number;
    product: {
        id: number;
        name: string;
        price: number;
        images: string[];
        sku: string;
        stock_quantity: number;
    };
    product_options?: Record<string, unknown>;
}

interface CartData {
    items: CartItem[];
    total_items: number;
    subtotal: number;
}

interface CartContextType {
    cart: CartData | null;
    loading: boolean;
    error: string | null;
    isGuestMode: boolean;
    addToCart: (productId: number, quantity?: number, productOptions?: Record<string, unknown>) => Promise<boolean>;
    updateCartItem: (itemId: number | string, quantity: number) => Promise<boolean>;
    removeFromCart: (itemId: number | string) => Promise<boolean>;
    clearCart: () => Promise<boolean>;
    refreshCart: () => Promise<void>;
    transferGuestCart: () => Promise<boolean>;
    // Optimistic update methods
    addToCartOptimistic: (productId: number, quantity?: number, productOptions?: Record<string, unknown>) => Promise<boolean>;
    updateCartItemOptimistic: (itemId: number | string, quantity: number) => Promise<boolean>;
    removeFromCartOptimistic: (itemId: number | string) => Promise<boolean>;
}

const CartContext = createContext<CartContextType | undefined>(undefined);

export const useCart = () => {
    const context = useContext(CartContext);
    if (context === undefined) {
        throw new Error('useCart must be used within a CartProvider');
    }
    return context;
};

interface CartProviderProps {
    children: React.ReactNode;
    isAuthenticated?: boolean;
}

export const CartProvider: React.FC<CartProviderProps> = ({ children, isAuthenticated = false }) => {

    const [cart, setCart] = useState<CartData | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [isGuestMode, setIsGuestMode] = useState(!isAuthenticated);
    const [, setOptimisticOperations] = useState<Set<string>>(new Set());

    // Check if we should use localStorage (guest mode)
    const shouldUseLocalStorage = !isAuthenticated && isLocalStorageAvailable();

    // Get the appropriate API endpoint based on authentication status
    const getCartEndpoint = useCallback(() => {
        return '/api/v1/cart';
    }, []);

    // Load cart data from localStorage with product details
    const loadGuestCartWithProducts = useCallback(async (guestCart: LocalStorageCartData): Promise<CartData> => {
        if (guestCart.items.length === 0) {
            return { items: [], total_items: 0, subtotal: 0 };
        }

        try {
            // Get product details for all items in guest cart
            const productIds = guestCart.items.map(item => item.product_id);
            const response = await axios.post('/api/v1/products/batch', { ids: productIds });

            if (response.data.success) {
                const products = response.data.data;
                const cartItems: CartItem[] = guestCart.items.map(guestItem => {
                    const product = products.find((p: { id: number }) => p.id === guestItem.product_id);
                    if (!product) {
                        console.warn(`Product ${guestItem.product_id} not found, removing from cart`);
                        return null;
                    }
                    return {
                        id: guestItem.id,
                        product_id: guestItem.product_id,
                        quantity: guestItem.quantity,
                        product_options: guestItem.product_options,
                        product: {
                            id: product.id,
                            name: product.name,
                            price: product.price,
                            images: product.images || []
                        }
                    };
                }).filter(Boolean) as CartItem[];

                const subtotal = cartItems.reduce((sum, item) => sum + (item.quantity * item.product.price), 0);
                const total_items = cartItems.reduce((sum, item) => sum + item.quantity, 0);

                return { items: cartItems, total_items, subtotal };
            }
        } catch (err) {
            console.error('Error loading product details for guest cart:', err);
        }

        // Fallback: return cart without product details
        return {
            items: guestCart.items.map(item => ({
                id: item.id,
                product_id: item.product_id,
                quantity: item.quantity,
                product_options: item.product_options,
                product: {
                    id: item.product_id,
                    name: 'Unknown Product',
                    price: 0,
                    images: [],
                    sku: '',
                    stock_quantity: 0
                }
            })),
            total_items: guestCart.total_items,
            subtotal: 0
        };
    }, []);

    // Fetch cart data
    const refreshCart = useCallback(async () => {
        try {
            setLoading(true);
            setError(null);

            if (shouldUseLocalStorage) {
                // Load from localStorage for guest users
                const guestCart = getGuestCartFromStorage();
                if (guestCart) {
                    const cartWithProducts = await loadGuestCartWithProducts(guestCart);
                    setCart(cartWithProducts);
                } else {
                    setCart({ items: [], total_items: 0, subtotal: 0 });
                }
                setIsGuestMode(true);
            } else {
                // Load from API for authenticated users
                const response = await axios.get(getCartEndpoint());

                if (response.data.success) {
                    setCart(response.data.data);
                } else {
                    setError('Failed to load cart');
                }
                setIsGuestMode(false);
            }
        } catch (err) {
            console.error('Error fetching cart:', err);
            setError('Failed to load cart');
            // Initialize empty cart on error
            setCart({ items: [], total_items: 0, subtotal: 0 });
        } finally {
            setLoading(false);
        }
    }, [getCartEndpoint, shouldUseLocalStorage, loadGuestCartWithProducts]);

    // Add item to cart
    const addToCart = useCallback(async (productId: number, quantity: number = 1, productOptions?: Record<string, unknown>): Promise<boolean> => {
        try {
            setLoading(true);
            setError(null);

            if (shouldUseLocalStorage) {
                // Add to localStorage for guest users
                addItemToGuestCart(productId, quantity, productOptions);
                await refreshCart(); // Refresh to get updated cart with product details
                return true;
            } else {
                // Add via API for authenticated users
                const response = await axios.post(getCartEndpoint(), {
                    product_id: productId,
                    quantity: quantity,
                    product_options: productOptions
                });

                if (response.data.success) {
                    await refreshCart();
                    return true;
                } else {
                    setError(response.data.message || 'Failed to add item to cart');
                    return false;
                }
            }
        } catch (err: unknown) {
            console.error('Error adding to cart:', err);
            const errorMessage = (err as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to add item to cart';
            setError(errorMessage);
            return false;
        } finally {
            setLoading(false);
        }
    }, [getCartEndpoint, refreshCart, shouldUseLocalStorage]);

    // Update cart item quantity
    const updateCartItem = useCallback(async (itemId: number | string, quantity: number): Promise<boolean> => {
        try {
            setLoading(true);
            setError(null);

            if (shouldUseLocalStorage && typeof itemId === 'string') {
                // Update in localStorage for guest users
                updateGuestCartItem(itemId, quantity);
                await refreshCart(); // Refresh to get updated cart with product details
                return true;
            } else if (!shouldUseLocalStorage && typeof itemId === 'number') {
                // Update via API for authenticated users
                const response = await axios.put(`${getCartEndpoint()}/${itemId}`, {
                    quantity: quantity
                });

                if (response.data.success) {
                    await refreshCart();
                    return true;
                } else {
                    setError(response.data.message || 'Failed to update cart item');
                    return false;
                }
            } else {
                setError('Invalid item ID for current cart mode');
                return false;
            }
        } catch (err: unknown) {
            console.error('Error updating cart item:', err);
            const errorMessage = (err as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to update cart item';
            setError(errorMessage);
            return false;
        } finally {
            setLoading(false);
        }
    }, [getCartEndpoint, refreshCart, shouldUseLocalStorage]);

    // Remove item from cart
    const removeFromCart = useCallback(async (itemId: number | string): Promise<boolean> => {
        try {
            setLoading(true);
            setError(null);

            if (shouldUseLocalStorage && typeof itemId === 'string') {
                // Remove from localStorage for guest users
                removeItemFromGuestCart(itemId);
                await refreshCart(); // Refresh to get updated cart
                return true;
            } else if (!shouldUseLocalStorage && typeof itemId === 'number') {
                // Remove via API for authenticated users
                const response = await axios.delete(`${getCartEndpoint()}/${itemId}`);

                if (response.data.success) {
                    await refreshCart();
                    return true;
                } else {
                    setError(response.data.message || 'Failed to remove item from cart');
                    return false;
                }
            } else {
                setError('Invalid item ID for current cart mode');
                return false;
            }
        } catch (err: unknown) {
            console.error('Error removing from cart:', err);
            const errorMessage = (err as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to remove item from cart';
            setError(errorMessage);
            return false;
        } finally {
            setLoading(false);
        }
    }, [getCartEndpoint, refreshCart, shouldUseLocalStorage]);

    // Clear entire cart
    const clearCart = useCallback(async (): Promise<boolean> => {
        try {
            setLoading(true);
            setError(null);

            if (shouldUseLocalStorage) {
                // Clear localStorage for guest users
                clearGuestCartFromStorage();
                setCart({ items: [], total_items: 0, subtotal: 0 });
                return true;
            } else {
                // Clear via API for authenticated users
                const response = await axios.delete(getCartEndpoint());

                if (response.data.success) {
                    setCart({ items: [], total_items: 0, subtotal: 0 });
                    return true;
                } else {
                    setError(response.data.message || 'Failed to clear cart');
                    return false;
                }
            }
        } catch (err: unknown) {
            console.error('Error clearing cart:', err);
            const errorMessage = (err as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to clear cart';
            setError(errorMessage);
            return false;
        } finally {
            setLoading(false);
        }
    }, [getCartEndpoint, shouldUseLocalStorage]);

    // Silent refresh cart - same as refreshCart but without loading states
    // Used for background updates during optimistic operations
    const silentRefreshCart = useCallback(async () => {
        try {
            setError(null);

            if (shouldUseLocalStorage) {
                // Load from localStorage for guest users
                const guestCart = getGuestCartFromStorage();
                if (guestCart) {
                    const cartWithProducts = await loadGuestCartWithProducts(guestCart);
                    setCart(cartWithProducts);
                } else {
                    setCart({ items: [], total_items: 0, subtotal: 0 });
                }
                setIsGuestMode(true);
            } else {
                // Load from API for authenticated users
                const response = await axios.get(getCartEndpoint());

                if (response.data.success) {
                    setCart(response.data.data);
                } else {
                    setError('Failed to load cart');
                }
                setIsGuestMode(false);
            }
        } catch (err) {
            console.error('Error fetching cart silently:', err);
            setError('Failed to load cart');
            // Initialize empty cart on error
            setCart({ items: [], total_items: 0, subtotal: 0 });
        }
        // Note: No setLoading(false) here since we never set it to true
    }, [getCartEndpoint, shouldUseLocalStorage, loadGuestCartWithProducts]);

    // Transfer guest cart to authenticated user
    const transferGuestCart = useCallback(async (): Promise<boolean> => {
        if (!isAuthenticated) {
            return false;
        }

        try {
            setLoading(true);
            setError(null);

            // Get guest cart from localStorage
            const guestCart = getGuestCartFromStorage();

            if (!guestCart || guestCart.items.length === 0) {
                // No guest cart to transfer, just refresh authenticated cart
                await refreshCart();
                return true;
            }

            // Convert localStorage cart format for API
            const cartItemsForTransfer = convertGuestCartForTransfer(guestCart);

            // Send guest cart items to backend for merging
            const response = await axios.post('/api/v1/cart/transfer-guest-cart', {
                guest_cart_items: cartItemsForTransfer
            });

            if (response.data.success) {
                // Clear localStorage after successful transfer
                clearGuestCartFromStorage();
                // Refresh cart to get merged data
                await refreshCart();
                return true;
            } else {
                setError(response.data.message || 'Failed to transfer cart');
                return false;
            }
        } catch (err: unknown) {
            console.error('Error transferring guest cart:', err);
            const errorMessage = (err as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to transfer cart';
            setError(errorMessage);
            return false;
        } finally {
            setLoading(false);
        }
    }, [isAuthenticated, refreshCart]);

    // Optimistic add to cart - updates UI immediately, then syncs with backend
    const addToCartOptimistic = useCallback(async (productId: number, quantity: number = 1, productOptions?: Record<string, unknown>): Promise<boolean> => {
        const operationId = `add-${productId}-${Date.now()}`;
        setOptimisticOperations(prev => new Set(prev).add(operationId));

        // Store original cart state for rollback
        const originalCart = cart;

        try {
            // Optimistic update - add item to cart immediately
            if (shouldUseLocalStorage) {
                // For guest users, update localStorage and then update cart state immediately
                addItemToGuestCart(productId, quantity, productOptions);

                // Load the updated cart with full product data for proper display
                const updatedGuestCart = getGuestCartFromStorage();
                if (updatedGuestCart) {
                    const cartWithProducts = await loadGuestCartWithProducts(updatedGuestCart);
                    setCart(cartWithProducts);
                }
            } else {
                // For authenticated users, we need to handle both existing and new items
                const existingItemIndex = cart?.items.findIndex(item => item.product_id === productId) ?? -1;

                if (existingItemIndex >= 0) {
                    // Update existing item immediately
                    setCart(prevCart => {
                        if (!prevCart) return prevCart;

                        const updatedItems = [...prevCart.items];
                        updatedItems[existingItemIndex] = {
                            ...updatedItems[existingItemIndex],
                            quantity: updatedItems[existingItemIndex].quantity + quantity
                        };

                        return {
                            ...prevCart,
                            items: updatedItems,
                            total_items: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
                            subtotal: updatedItems.reduce((sum, item) => sum + (item.quantity * item.product.price), 0)
                        };
                    });
                } else {
                    // For new items, fetch product data and add optimistically
                    try {
                        const productResponse = await axios.get(`/api/v1/products/${productId}`);
                        if (productResponse.data.success) {
                            const productData = productResponse.data.data;

                            // Create optimistic cart item
                            const optimisticItem: CartItem = {
                                id: `temp-${productId}-${Date.now()}`, // Temporary ID
                                product_id: productId,
                                quantity,
                                product: {
                                    id: productData.id,
                                    name: productData.name,
                                    price: parseFloat(productData.price),
                                    images: productData.images || [],
                                    sku: productData.sku || '',
                                    stock_quantity: productData.stock_quantity || 0
                                },
                                product_options: productOptions
                            };

                            // Add to cart immediately
                            setCart(prevCart => {
                                const currentItems = prevCart?.items || [];
                                const updatedItems = [...currentItems, optimisticItem];

                                return {
                                    items: updatedItems,
                                    total_items: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
                                    subtotal: updatedItems.reduce((sum, item) => sum + (item.quantity * item.product.price), 0)
                                };
                            });
                        }
                    } catch (productError) {
                        console.warn('Failed to fetch product data for optimistic update:', productError);
                        // Continue with the API call, but without optimistic update
                    }
                }
            }

            // Perform actual API call in the background (only for authenticated users)
            // For guest users, the localStorage update is sufficient
            if (!shouldUseLocalStorage) {
                try {
                    const response = await axios.post(getCartEndpoint(), {
                        product_id: productId,
                        quantity: quantity,
                        product_options: productOptions
                    });

                    if (response.data.success) {
                        // Refresh cart to get the real data from server and replace optimistic data
                        await silentRefreshCart();
                        return true;
                    } else {
                        // Rollback optimistic update on API failure
                        setCart(originalCart);
                        setError(response.data.message || 'Failed to add item to cart');
                        return false;
                    }
                } catch (apiError) {
                    // Rollback optimistic update on API error
                    setCart(originalCart);
                    console.error('Error adding to cart via API:', apiError);
                    const errorMessage = (apiError as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to add item to cart';
                    setError(errorMessage);
                    return false;
                }
            }

            return true; // For guest users, localStorage update was successful
        } catch {
            // Rollback optimistic update on error
            setCart(originalCart);
            return false;
        } finally {
            setOptimisticOperations(prev => {
                const newSet = new Set(prev);
                newSet.delete(operationId);
                return newSet;
            });
        }
    }, [shouldUseLocalStorage, getCartEndpoint, silentRefreshCart, cart, loadGuestCartWithProducts]);

    // Optimistic update cart item quantity
    const updateCartItemOptimistic = useCallback(async (itemId: number | string, quantity: number): Promise<boolean> => {
        const operationId = `update-${itemId}-${Date.now()}`;
        setOptimisticOperations(prev => new Set(prev).add(operationId));

        // Store original cart state for rollback
        const originalCart = cart;

        try {
            // Optimistic update
            if (shouldUseLocalStorage && typeof itemId === 'string') {
                // Update localStorage first
                updateGuestCartItem(itemId, quantity);

                // Update cart state immediately for instant UI feedback
                setCart(prevCart => {
                    if (!prevCart) return prevCart;

                    const updatedItems = prevCart.items.map(item =>
                        item.id === itemId ? { ...item, quantity } : item
                    ).filter(item => item.quantity > 0); // Remove items with 0 quantity

                    return {
                        ...prevCart,
                        items: updatedItems,
                        total_items: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
                        subtotal: updatedItems.reduce((sum, item) => sum + (item.quantity * item.product.price), 0)
                    };
                });

                // Refresh with full product data in the background
                silentRefreshCart();
            } else {
                // Update UI immediately for authenticated users
                setCart(prevCart => {
                    if (!prevCart) return prevCart;

                    const updatedItems = prevCart.items.map(item =>
                        item.id === itemId ? { ...item, quantity } : item
                    ).filter(item => item.quantity > 0); // Remove items with 0 quantity

                    return {
                        ...prevCart,
                        items: updatedItems,
                        total_items: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
                        subtotal: updatedItems.reduce((sum, item) => sum + (item.quantity * item.product.price), 0)
                    };
                });
            }

            // Perform actual API call (only for authenticated users)
            // For guest users, the localStorage update is sufficient
            if (!shouldUseLocalStorage) {
                try {
                    const response = await axios.put(`${getCartEndpoint()}/${itemId}`, {
                        quantity: quantity
                    });

                    if (response.data.success) {
                        // Refresh cart to get the real data from server
                        await silentRefreshCart();
                        return true;
                    } else {
                        // Rollback optimistic update on API failure
                        setCart(originalCart);
                        setError(response.data.message || 'Failed to update cart item');
                        return false;
                    }
                } catch (apiError) {
                    // Rollback optimistic update on API error
                    setCart(originalCart);
                    console.error('Error updating cart item via API:', apiError);
                    const errorMessage = (apiError as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to update cart item';
                    setError(errorMessage);
                    return false;
                }
            }

            return true; // For guest users, localStorage update was successful
        } catch {
            // Rollback on error
            if (!shouldUseLocalStorage) {
                setCart(originalCart);
            }
            return false;
        } finally {
            setOptimisticOperations(prev => {
                const newSet = new Set(prev);
                newSet.delete(operationId);
                return newSet;
            });
        }
    }, [cart, shouldUseLocalStorage, getCartEndpoint, silentRefreshCart]);

    // Optimistic remove from cart
    const removeFromCartOptimistic = useCallback(async (itemId: number | string): Promise<boolean> => {
        const operationId = `remove-${itemId}-${Date.now()}`;
        setOptimisticOperations(prev => new Set(prev).add(operationId));

        // Store original cart state for rollback
        const originalCart = cart;

        try {
            // Optimistic update
            if (shouldUseLocalStorage && typeof itemId === 'string') {
                // Update localStorage first
                removeItemFromGuestCart(itemId);

                // Update cart state immediately for instant UI feedback
                setCart(prevCart => {
                    if (!prevCart) return prevCart;

                    const updatedItems = prevCart.items.filter(item => item.id !== itemId);

                    return {
                        ...prevCart,
                        items: updatedItems,
                        total_items: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
                        subtotal: updatedItems.reduce((sum, item) => sum + (item.quantity * item.product.price), 0)
                    };
                });

                // Refresh with full product data in the background
                silentRefreshCart();
            } else {
                // Update UI immediately for authenticated users
                setCart(prevCart => {
                    if (!prevCart) return prevCart;

                    const updatedItems = prevCart.items.filter(item => item.id !== itemId);

                    return {
                        ...prevCart,
                        items: updatedItems,
                        total_items: updatedItems.reduce((sum, item) => sum + item.quantity, 0),
                        subtotal: updatedItems.reduce((sum, item) => sum + (item.quantity * item.product.price), 0)
                    };
                });
            }

            // Perform actual API call (only for authenticated users)
            // For guest users, the localStorage update is sufficient
            if (!shouldUseLocalStorage) {
                try {
                    const response = await axios.delete(`${getCartEndpoint()}/${itemId}`);

                    if (response.data.success) {
                        // Refresh cart to get the real data from server
                        await silentRefreshCart();
                        return true;
                    } else {
                        // Rollback optimistic update on API failure
                        setCart(originalCart);
                        setError(response.data.message || 'Failed to remove cart item');
                        return false;
                    }
                } catch (apiError) {
                    // Rollback optimistic update on API error
                    setCart(originalCart);
                    console.error('Error removing cart item via API:', apiError);
                    const errorMessage = (apiError as { response?: { data?: { message?: string } } })?.response?.data?.message || 'Failed to remove cart item';
                    setError(errorMessage);
                    return false;
                }
            }

            return true; // For guest users, localStorage update was successful
        } catch {
            // Rollback on error
            if (!shouldUseLocalStorage) {
                setCart(originalCart);
            }
            return false;
        } finally {
            setOptimisticOperations(prev => {
                const newSet = new Set(prev);
                newSet.delete(operationId);
                return newSet;
            });
        }
    }, [cart, shouldUseLocalStorage, getCartEndpoint, silentRefreshCart]);

    // Update guest mode when authentication status changes
    useEffect(() => {
        setIsGuestMode(!isAuthenticated);
    }, [isAuthenticated]);

    // Load cart on mount and when authentication status changes
    useEffect(() => {
        refreshCart();
    }, [refreshCart]);

    // Transfer guest cart when user becomes authenticated
    useEffect(() => {
        if (isAuthenticated && isLocalStorageAvailable()) {
            const guestCart = getGuestCartFromStorage();
            if (guestCart && guestCart.items.length > 0) {
                // Transfer localStorage cart to authenticated user
                transferGuestCart();
            }
        }
    }, [isAuthenticated, transferGuestCart]);

    const value: CartContextType = {
        cart,
        loading,
        error,
        isGuestMode,
        addToCart,
        updateCartItem,
        removeFromCart,
        clearCart,
        refreshCart,
        transferGuestCart,
        addToCartOptimistic,
        updateCartItemOptimistic,
        removeFromCartOptimistic
    };

    return (
        <CartContext.Provider value={value}>
            {children}
        </CartContext.Provider>
    );
};
