import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import React from 'react';

const SpecialOffer: React.FC = () => {
    return (
        <section className="bg-background py-16">
            <div className="container mx-auto px-4">
                <Card className="overflow-hidden border-none bg-gradient-to-r from-green-400 to-emerald-500">
                    <CardContent className="grid items-center gap-8 p-8 md:grid-cols-2 md:p-12">
                        <div className="text-white">
                            <h2 className="font-display mb-4 text-3xl font-bold md:text-5xl">Special Offer</h2>
                            <p className="mb-2 text-xl text-white/90">Fruit Basket</p>
                            <p className="mb-6 text-lg text-white/80">Easy Healthy, Happy Life</p>
                            <div className="mb-8">
                                <span className="text-4xl font-bold">₱250.00</span>
                                <span className="ml-3 text-lg text-white/80 line-through">₱350.00</span>
                            </div>
                            <Button size="lg" className="bg-white font-semibold text-green-600 hover:bg-gray-100">
                                Shop Now
                            </Button>
                        </div>
                        <div className="text-center">
                            <Card className="border-none bg-white/10 backdrop-blur-sm">
                                <CardContent className="p-8">
                                    <div className="mb-4 text-8xl">🧺</div>
                                    <h3 className="text-xl font-semibold text-white">Fresh Fruit Basket</h3>
                                    <p className="text-white/80">Mixed seasonal fruits</p>
                                </CardContent>
                            </Card>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </section>
    );
};

export default SpecialOffer;
