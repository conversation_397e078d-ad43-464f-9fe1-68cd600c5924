import React from 'react';

const Footer: React.FC = () => {
    const footerSections = {
        account: ['Sign In', 'Register', 'Order History', 'My Wishlist', 'Track My Order'],
        help: ['Shipping & Returns', 'Secure Shopping', 'Testimonials', 'About Us', 'Contact Us'],
        information: ['Privacy Policy', 'Terms & Conditions', 'Return Policy', 'Site Map', 'Store Locations'],
    };

    return (
        <footer className="border-t bg-card py-12">
            <div className="container mx-auto px-4">
                <div className="grid grid-cols-1 gap-8 md:grid-cols-4">
                    {/* Company Info */}
                    <div>
                        <div className="mb-6 flex items-center space-x-3">
                            <div className="rounded-lg bg-gradient-to-r from-green-500 to-emerald-500 p-2 text-white">
                                <span className="text-xl font-bold">🌟</span>
                            </div>
                            <div>
                                <h3 className="font-display text-xl font-bold text-foreground">Lucky Star</h3>
                                <p className="text-sm text-muted-foreground">Grocery</p>
                            </div>
                        </div>
                        <p className="mb-4 text-muted-foreground">Your trusted partner for fresh, quality groceries delivered to your doorstep.</p>
                        <div className="flex space-x-4">
                            <span className="cursor-pointer text-2xl transition-colors hover:text-primary">📘</span>
                            <span className="cursor-pointer text-2xl transition-colors hover:text-primary">📷</span>
                            <span className="cursor-pointer text-2xl transition-colors hover:text-primary">🐦</span>
                        </div>
                    </div>

                    {/* My Account */}
                    <div>
                        <h4 className="mb-4 text-lg font-semibold text-foreground">My Account</h4>
                        <ul className="space-y-2 text-muted-foreground">
                            {footerSections.account.map((item) => (
                                <li key={item}>
                                    <a href="#" className="transition-colors hover:text-foreground">
                                        {item}
                                    </a>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Help */}
                    <div>
                        <h4 className="mb-4 text-lg font-semibold text-foreground">Why Buy From Us</h4>
                        <ul className="space-y-2 text-muted-foreground">
                            {footerSections.help.map((item) => (
                                <li key={item}>
                                    <a href="#" className="transition-colors hover:text-foreground">
                                        {item}
                                    </a>
                                </li>
                            ))}
                        </ul>
                    </div>

                    {/* Information */}
                    <div>
                        <h4 className="mb-4 text-lg font-semibold text-foreground">Information</h4>
                        <ul className="space-y-2 text-muted-foreground">
                            {footerSections.information.map((item) => (
                                <li key={item}>
                                    <a href="#" className="transition-colors hover:text-foreground">
                                        {item}
                                    </a>
                                </li>
                            ))}
                        </ul>
                    </div>
                </div>

                <div className="mt-8 border-t border-border pt-8 text-center text-muted-foreground">
                    <p>&copy; 2025 Lucky Star Grocery. All rights reserved.</p>
                </div>
            </div>
        </footer>
    );
};

export default Footer;
