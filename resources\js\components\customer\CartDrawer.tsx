import { Minus, Plus, ShoppingCart, Trash2 } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { useCart } from '@/hooks/use-cart';
import ProductImage from './ProductImage';

// Utility function for price formatting (consistent with existing patterns)
const formatPrice = (price: string | number | null | undefined): string => {
    if (price === null || price === undefined || price === '') {
        return '₱0.00';
    }

    const numericPrice = typeof price === 'string' ? parseFloat(price) : price;

    if (isNaN(numericPrice)) {
        return '₱0.00';
    }

    return `₱${numericPrice.toFixed(2)}`;
};

interface CartDrawerProps {
    isOpen: boolean;
    onClose: () => void;
}

const CartDrawer: React.FC<CartDrawerProps> = ({ isOpen, onClose }) => {
    const { cart, loading, error, updateCartItemOptimistic, removeFromCartOptimistic, clearCart, isGuestMode } = useCart();

    const handleQuantityChange = async (itemId: number | string, newQuantity: number) => {
        if (newQuantity < 1) {
            await removeFromCartOptimistic(itemId);
        } else {
            await updateCartItemOptimistic(itemId, newQuantity);
        }
    };

    const handleRemoveItem = async (itemId: number | string) => {
        await removeFromCartOptimistic(itemId);
    };

    const handleClearCart = async () => {
        await clearCart();
    };

    const handleCheckout = () => {
        // Navigate to checkout page
        window.location.href = '/checkout';
    };

    const handleViewCart = () => {
        // Navigate to cart page
        window.location.href = '/cart';
    };

    const cartItems = cart?.items || [];
    const totalItems = cart?.total_items || 0;
    const subtotal = cart?.subtotal || 0;

    return (
        <Sheet open={isOpen} onOpenChange={onClose}>
            <SheetContent className="flex w-full flex-col sm:max-w-lg h-full">
                <SheetHeader className="space-y-4 pb-6 pr-6">
                    <SheetTitle className="flex items-center gap-3 text-lg font-semibold">
                        <ShoppingCart className="h-6 w-6 text-primary" />
                        Shopping Cart
                        {totalItems > 0 && (
                            <Badge variant="secondary" className="ml-auto px-2.5 py-1 text-sm font-medium">
                                {totalItems} {totalItems === 1 ? 'item' : 'items'}
                            </Badge>
                        )}
                    </SheetTitle>
                    {isGuestMode && (
                        <div className="rounded-lg bg-blue-50 px-4 py-3 text-sm text-blue-700 dark:bg-blue-950/50 dark:text-blue-300">
                            <div className="flex items-center gap-2">
                                <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                                Items will be saved locally until you log in
                            </div>
                        </div>
                    )}
                </SheetHeader>

                {error && (
                    <div className="mx-1 mb-4 rounded-lg bg-destructive/10 px-4 py-3 text-sm text-destructive border border-destructive/20">
                        <div className="flex items-center gap-2">
                            <div className="h-2 w-2 rounded-full bg-destructive"></div>
                            {error}
                        </div>
                    </div>
                )}

                <div className="flex flex-1 flex-col min-h-0">
                    {loading ? (
                        <div className="flex flex-1 items-center justify-center py-12">
                            <div className="text-center space-y-4">
                                <div className="h-10 w-10 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto"></div>
                                <p className="text-sm text-muted-foreground font-medium">Loading cart...</p>
                            </div>
                        </div>
                    ) : cartItems.length === 0 ? (
                        <div className="flex flex-1 flex-col items-center justify-center space-y-6 py-12 px-4">
                            <div className="rounded-full bg-muted/50 p-8 ring-1 ring-muted">
                                <ShoppingCart className="h-16 w-16 text-muted-foreground" />
                            </div>
                            <div className="text-center space-y-2">
                                <h3 className="text-lg font-semibold text-foreground">Your cart is empty</h3>
                                <p className="text-sm text-muted-foreground max-w-sm">
                                    Discover amazing products and add them to your cart to get started
                                </p>
                            </div>
                            <Button onClick={onClose} className="w-full max-w-xs h-12 font-medium">
                                Continue Shopping
                            </Button>
                        </div>
                    ) : (
                        <div className="flex flex-1 flex-col min-h-0">
                            {/* Cart Items - Scrollable Section */}
                            <div className="flex-1 overflow-y-auto min-h-0">
                                <div className="space-y-4 p-1 pb-4">
                                    {cartItems.map((item, index) => (
                                        <div key={item.id} className={`group relative rounded-xl border border-border/50 bg-card p-4 shadow-sm transition-all duration-200 hover:border-border hover:shadow-md ${index !== cartItems.length - 1 ? 'mb-3' : ''}`}>
                                            <div className="flex gap-4">
                                                <div className="flex-shrink-0">
                                                    <ProductImage
                                                        src={item.product.images[0]}
                                                        alt={item.product.name}
                                                        size="md"
                                                        className="h-20 w-20 rounded-lg"
                                                    />
                                                </div>
                                                
                                                <div className="flex flex-1 flex-col gap-3">
                                                    <div className="flex items-start justify-between gap-3">
                                                        <div className="flex-1 min-w-0 space-y-1">
                                                            <h4 className="font-medium text-base leading-tight text-foreground line-clamp-2">
                                                                {item.product.name}
                                                            </h4>
                                                            <p className="text-base font-semibold text-primary">
                                                                {formatPrice(item.product.price)}
                                                            </p>
                                                        </div>
                                                        <Button
                                                            variant="ghost"
                                                            size="sm"
                                                            className="h-9 w-9 p-0 text-muted-foreground hover:text-destructive hover:bg-destructive/10 transition-colors opacity-0 group-hover:opacity-100"
                                                            onClick={() => handleRemoveItem(item.id)}
                                                            disabled={loading}
                                                            title="Remove item"
                                                        >
                                                            <Trash2 className="h-4 w-4" />
                                                        </Button>
                                                    </div>
                                                    
                                                    <div className="flex items-center justify-between pt-2">
                                                        <div className="flex items-center gap-3 rounded-lg border border-border/50 bg-muted/30 p-1">
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                className="h-9 w-9 p-0 hover:bg-background transition-colors"
                                                                onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                                                                disabled={loading || item.quantity <= 1}
                                                                title="Decrease quantity"
                                                            >
                                                                <Minus className="h-4 w-4" />
                                                            </Button>
                                                            <span className="min-w-[2rem] text-center text-base font-semibold text-foreground">
                                                                {item.quantity}
                                                            </span>
                                                            <Button
                                                                variant="ghost"
                                                                size="sm"
                                                                className="h-9 w-9 p-0 hover:bg-background transition-colors"
                                                                onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                                                                disabled={loading}
                                                                title="Increase quantity"
                                                            >
                                                                <Plus className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                        <div className="text-base font-bold text-foreground">
                                                            {formatPrice(item.product.price * item.quantity)}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Fixed Bottom Section - Summary and Actions */}
                            <div className="flex-shrink-0 border-t border-border/60 bg-background">
                                <div className="p-4 space-y-6">
                                    {/* Cart Summary */}
                                    <div className="space-y-6 bg-muted/30 rounded-xl p-5 border border-border/50">
                                        <div className="space-y-3">
                                            <div className="flex items-center justify-between">
                                                <span className="text-base text-muted-foreground font-medium">Subtotal</span>
                                                <span className="text-base font-semibold text-foreground">{formatPrice(subtotal)}</span>
                                            </div>
                                            <Separator className="bg-border/40" />
                                            <div className="flex items-center justify-between">
                                                <span className="text-lg font-bold text-foreground">Total</span>
                                                <span className="text-xl font-bold text-primary">{formatPrice(subtotal)}</span>
                                            </div>
                                        </div>

                                        {/* Action Buttons */}
                                        <div className="space-y-4 pt-2">
                                            <Button
                                                onClick={handleCheckout}
                                                className="w-full h-12 text-base font-semibold shadow-sm hover:shadow-md transition-all duration-200"
                                                size="lg"
                                                disabled={loading}
                                            >
                                                Proceed to Checkout
                                            </Button>
                                            <div className="flex gap-3">
                                                <Button
                                                    variant="outline"
                                                    onClick={handleViewCart}
                                                    className="flex-1 h-11 font-medium border-border/60 hover:bg-muted/50 transition-colors"
                                                    disabled={loading}
                                                >
                                                    View Cart
                                                </Button>
                                                <Button
                                                    variant="outline"
                                                    onClick={onClose}
                                                    className="flex-1 h-11 font-medium border-border/60 hover:bg-muted/50 transition-colors"
                                                    disabled={loading}
                                                >
                                                    Continue Shopping
                                                </Button>
                                            </div>
                                            <Button
                                                variant="outline"
                                                onClick={handleClearCart}
                                                className="w-full h-11 font-medium border-border/60 hover:bg-destructive/10 hover:text-destructive hover:border-destructive/30 transition-colors"
                                                disabled={loading}
                                            >
                                                Clear Cart
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </SheetContent>
        </Sheet>
    );
};

export default CartDrawer;
