<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Crypt;

class UserSecuritySetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'two_factor_enabled',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'two_factor_confirmed_at',
        'login_notifications',
        'trusted_devices',
        'password_changed_at',
        'failed_login_attempts',
        'locked_until',
        'logout_other_devices',
        'session_timeout_minutes',
        'security_questions',
    ];

    protected $casts = [
        'two_factor_enabled' => 'boolean',
        'two_factor_recovery_codes' => 'array',
        'two_factor_confirmed_at' => 'datetime',
        'login_notifications' => 'boolean',
        'trusted_devices' => 'array',
        'password_changed_at' => 'datetime',
        'locked_until' => 'datetime',
        'logout_other_devices' => 'boolean',
        'security_questions' => 'array',
    ];

    protected $hidden = [
        'two_factor_secret',
        'two_factor_recovery_codes',
        'security_questions',
    ];

    /**
     * Get the user that owns the security settings.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Encrypt the two-factor secret.
     */
    public function setTwoFactorSecretAttribute($value)
    {
        $this->attributes['two_factor_secret'] = $value ? Crypt::encryptString($value) : null;
    }

    /**
     * Decrypt the two-factor secret.
     */
    public function getTwoFactorSecretAttribute($value)
    {
        return $value ? Crypt::decryptString($value) : null;
    }

    /**
     * Check if account is currently locked.
     */
    public function isLocked(): bool
    {
        return $this->locked_until && $this->locked_until->isFuture();
    }

    /**
     * Check if device is trusted.
     */
    public function isTrustedDevice(string $deviceFingerprint): bool
    {
        return in_array($deviceFingerprint, $this->trusted_devices ?? []);
    }

    /**
     * Add a trusted device.
     */
    public function addTrustedDevice(string $deviceFingerprint): void
    {
        $devices = $this->trusted_devices ?? [];
        if (!in_array($deviceFingerprint, $devices)) {
            $devices[] = $deviceFingerprint;
            $this->trusted_devices = $devices;
            $this->save();
        }
    }

    /**
     * Remove a trusted device.
     */
    public function removeTrustedDevice(string $deviceFingerprint): void
    {
        $devices = $this->trusted_devices ?? [];
        $devices = array_filter($devices, fn($device) => $device !== $deviceFingerprint);
        $this->trusted_devices = array_values($devices);
        $this->save();
    }

    /**
     * Increment failed login attempts.
     */
    public function incrementFailedAttempts(): void
    {
        $this->increment('failed_login_attempts');
        
        // Lock account after 5 failed attempts for 30 minutes
        if ($this->failed_login_attempts >= 5) {
            $this->locked_until = now()->addMinutes(30);
            $this->save();
        }
    }

    /**
     * Reset failed login attempts.
     */
    public function resetFailedAttempts(): void
    {
        $this->update([
            'failed_login_attempts' => 0,
            'locked_until' => null,
        ]);
    }

    /**
     * Generate recovery codes.
     */
    public function generateRecoveryCodes(): array
    {
        $codes = [];
        for ($i = 0; $i < 8; $i++) {
            $codes[] = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8));
        }
        
        $this->two_factor_recovery_codes = $codes;
        $this->save();
        
        return $codes;
    }

    /**
     * Use a recovery code.
     */
    public function useRecoveryCode(string $code): bool
    {
        $codes = $this->two_factor_recovery_codes ?? [];
        $index = array_search(strtoupper($code), $codes);
        
        if ($index !== false) {
            unset($codes[$index]);
            $this->two_factor_recovery_codes = array_values($codes);
            $this->save();
            return true;
        }
        
        return false;
    }
}
