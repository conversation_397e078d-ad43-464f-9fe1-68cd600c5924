import React, { useState, useCallback, useEffect } from 'react';
import { MapPin, Truck, Building, Plus, BookOpen } from 'lucide-react';
import { apiClient } from '@/bootstrap';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';


import { type Address, type ShippingMethod } from '@/types/ecommerce';

interface SavedAddress {
    id: number;
    type: string;
    label: string | null;
    first_name: string;
    last_name: string;
    company: string | null;
    address_line_1: string;
    address_line_2: string | null;
    city: string;
    state_province: string;
    postal_code: string;
    country: string;
    phone: string | null;
    is_default: boolean;
}

interface ShippingStepProps {
    billingAddress?: Address;
    shippingAddress?: Address;
    sameAsBilling: boolean;
    onUpdate: (data: {
        billing_address?: Address;
        shipping_address?: Address;
        same_as_billing?: boolean;
        shipping_method?: ShippingMethod;
    }) => void;
    onNext: () => void;
    onPrev: () => void;
}

const PHILIPPINES_REGIONS = [
    'Metro Manila', 'Calabarzon', 'Central Luzon', 'Western Visayas', 'Central Visayas',
    'Northern Mindanao', 'Davao Region', 'Soccsksargen', 'Ilocos Region', 'Cagayan Valley',
    'Bicol Region', 'Eastern Visayas', 'Zamboanga Peninsula', 'Caraga', 'Mimaropa',
    'Cordillera Administrative Region', 'Bangsamoro'
];

const SHIPPING_METHODS: ShippingMethod[] = [
    {
        id: 'standard',
        name: 'Standard Delivery',
        description: 'Regular delivery within Metro Manila',
        price: 150,
        estimated_days: '3-5 business days',
        carrier: 'LBC Express',
        is_available: true,
    },
    {
        id: 'express',
        name: 'Express Delivery',
        description: 'Fast delivery within Metro Manila',
        price: 250,
        estimated_days: '1-2 business days',
        carrier: 'J&T Express',
        is_available: true,
    },
    {
        id: 'same_day',
        name: 'Same Day Delivery',
        description: 'Same day delivery (Metro Manila only)',
        price: 400,
        estimated_days: 'Same day',
        carrier: 'Grab Express',
        is_available: true,
    },
    {
        id: 'pickup',
        name: 'Store Pickup',
        description: 'Pick up from our store in Makati',
        price: 0,
        estimated_days: 'Ready in 2-4 hours',
        carrier: 'Self Pickup',
        is_available: true,
    },
];

const ShippingStep: React.FC<ShippingStepProps> = ({
    billingAddress,
    shippingAddress,
    sameAsBilling,
    onUpdate,
    onNext,
    onPrev
}) => {
    const [savedAddresses, setSavedAddresses] = useState<SavedAddress[]>([]);
    const [loadingAddresses, setLoadingAddresses] = useState(false);
    const [selectedBillingId, setSelectedBillingId] = useState<number | null>(null);
    const [selectedShippingId, setSelectedShippingId] = useState<number | null>(null);
    const [showNewBillingForm, setShowNewBillingForm] = useState(false);
    const [showNewShippingForm, setShowNewShippingForm] = useState(false);
    const [formData, setFormData] = useState({
        billing_address: billingAddress || {
            first_name: '',
            last_name: '',
            company: '',
            address_line_1: '',
            address_line_2: '',
            city: '',
            state: '',
            postal_code: '',
            country: 'Philippines',
            phone: '',
        },
        shipping_address: shippingAddress || {
            first_name: '',
            last_name: '',
            company: '',
            address_line_1: '',
            address_line_2: '',
            city: '',
            state: '',
            postal_code: '',
            country: 'Philippines',
        },
        same_as_billing: sameAsBilling,
        shipping_method: undefined as ShippingMethod | undefined,
    });

    const [errors, setErrors] = useState<Record<string, string>>({});

    // Load saved addresses on component mount
    useEffect(() => {
        const loadSavedAddresses = async () => {
            try {
                setLoadingAddresses(true);
                const response = await apiClient.get('/addresses');
                if (response.data.success) {
                    const addresses = response.data.data;
                    setSavedAddresses(addresses);

                    // Auto-select default addresses if no address is currently selected
                    if (!billingAddress && !selectedBillingId) {
                        const defaultBilling = addresses.find((addr: SavedAddress) =>
                            (addr.type === 'billing' || addr.type === 'both') && addr.is_default
                        );
                        if (defaultBilling) {
                            setSelectedBillingId(defaultBilling.id);
                            handleSelectSavedAddress(defaultBilling, 'billing');
                        }
                    }

                    if (!shippingAddress && !selectedShippingId && !sameAsBilling) {
                        const defaultShipping = addresses.find((addr: SavedAddress) =>
                            (addr.type === 'shipping' || addr.type === 'both') && addr.is_default
                        );
                        if (defaultShipping) {
                            setSelectedShippingId(defaultShipping.id);
                            handleSelectSavedAddress(defaultShipping, 'shipping');
                        }
                    }
                }
            } catch (error) {
                console.error('Failed to load saved addresses:', error);
            } finally {
                setLoadingAddresses(false);
            }
        };

        loadSavedAddresses();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    // Convert saved address to checkout address format
    const convertSavedAddress = (savedAddr: SavedAddress): Address => ({
        first_name: savedAddr.first_name,
        last_name: savedAddr.last_name,
        company: savedAddr.company || '',
        address_line_1: savedAddr.address_line_1,
        address_line_2: savedAddr.address_line_2 || '',
        city: savedAddr.city,
        state: savedAddr.state_province,
        postal_code: savedAddr.postal_code,
        country: savedAddr.country === 'PH' ? 'Philippines' : savedAddr.country,
        phone: savedAddr.phone || '',
    });

    // Handle selecting a saved address
    const handleSelectSavedAddress = (savedAddr: SavedAddress, type: 'billing' | 'shipping') => {
        const convertedAddress = convertSavedAddress(savedAddr);

        if (type === 'billing') {
            setSelectedBillingId(savedAddr.id);
            setShowNewBillingForm(false);
            setFormData(prev => ({
                ...prev,
                billing_address: convertedAddress
            }));
        } else {
            setSelectedShippingId(savedAddr.id);
            setShowNewShippingForm(false);
            setFormData(prev => ({
                ...prev,
                shipping_address: convertedAddress
            }));
        }
    };

    useEffect(() => {
        if (formData.same_as_billing && formData.billing_address) {
            setFormData(prev => ({
                ...prev,
                shipping_address: {
                    ...prev.billing_address,
                    phone: undefined, // Don't copy phone to shipping
                }
            }));
        }
    }, [formData.same_as_billing, formData.billing_address]);

    const handleAddressChange = useCallback((
        type: 'billing_address' | 'shipping_address',
        field: keyof Address,
        value: string
    ) => {
        setFormData(prev => {
            const updated = {
                ...prev,
                [type]: {
                    ...prev[type],
                    [field]: value
                }
            };
            
            onUpdate(updated);
            return updated;
        });

        // Clear error when user starts typing
        const errorKey = `${type}.${field}`;
        if (errors[errorKey]) {
            setErrors(prev => ({ ...prev, [errorKey]: '' }));
        }
    }, [onUpdate, errors]);

    const handleSameAsBillingChange = useCallback((checked: boolean) => {
        setFormData(prev => {
            const updated = { ...prev, same_as_billing: checked };
            onUpdate(updated);
            return updated;
        });
    }, [onUpdate]);

    const handleShippingMethodChange = useCallback((method: ShippingMethod) => {
        setFormData(prev => {
            const updated = { ...prev, shipping_method: method };
            onUpdate(updated);
            return updated;
        });
    }, [onUpdate]);

    const validateAddress = (address: Address, prefix: string) => {
        const newErrors: Record<string, string> = {};

        if (!address.first_name) newErrors[`${prefix}.first_name`] = 'First name is required';
        if (!address.last_name) newErrors[`${prefix}.last_name`] = 'Last name is required';
        if (!address.address_line_1) newErrors[`${prefix}.address_line_1`] = 'Address is required';
        if (!address.city) newErrors[`${prefix}.city`] = 'City is required';
        if (!address.state) newErrors[`${prefix}.state`] = 'Region is required';
        if (!address.postal_code) newErrors[`${prefix}.postal_code`] = 'Postal code is required';

        // Phone validation for billing address
        if (prefix === 'billing_address' && address.phone) {
            if (!/^(\+63|0)[0-9]{10}$/.test(address.phone.replace(/\s/g, ''))) {
                newErrors[`${prefix}.phone`] = 'Please enter a valid Philippine phone number';
            }
        }

        return newErrors;
    };

    const validateForm = useCallback(() => {
        const newErrors: Record<string, string> = {};

        // Validate billing address
        Object.assign(newErrors, validateAddress(formData.billing_address, 'billing_address'));

        // Validate shipping address (if different from billing)
        if (!formData.same_as_billing) {
            Object.assign(newErrors, validateAddress(formData.shipping_address, 'shipping_address'));
        }

        // Validate shipping method
        if (!formData.shipping_method) {
            newErrors.shipping_method = 'Please select a shipping method';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    }, [formData]);

    const handleNext = useCallback(() => {
        if (validateForm()) {
            onNext();
        }
    }, [validateForm, onNext]);

    // Render saved address selection
    const renderSavedAddressSelection = (type: 'billing' | 'shipping') => {
        const relevantAddresses = savedAddresses.filter(addr =>
            addr.type === type || addr.type === 'both'
        );

        if (relevantAddresses.length === 0) return null;

        const selectedId = type === 'billing' ? selectedBillingId : selectedShippingId;

        return (
            <Card className="mb-4">
                <CardHeader>
                    <CardTitle className="flex items-center text-base">
                        <BookOpen className="h-4 w-4 mr-2" />
                        Saved {type.charAt(0).toUpperCase() + type.slice(1)} Addresses
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-4">
                        {relevantAddresses.map((addr) => (
                            <div
                                key={addr.id}
                                className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                                    selectedId === addr.id
                                        ? 'border-primary bg-primary/5'
                                        : 'border-border hover:border-primary/50'
                                }`}
                                onClick={() => handleSelectSavedAddress(addr, type)}
                            >
                                <div className="flex items-start justify-between">
                                    <div className="flex-1">
                                        <div className="flex items-center space-x-2 mb-1">
                                            <h4 className="font-medium text-sm">
                                                {addr.first_name} {addr.last_name}
                                            </h4>
                                            {addr.is_default && (
                                                <span className="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded">
                                                    Default
                                                </span>
                                            )}
                                            {addr.label && (
                                                <span className="text-xs bg-muted text-muted-foreground px-2 py-0.5 rounded">
                                                    {addr.label}
                                                </span>
                                            )}
                                        </div>
                                        <p className="text-xs text-muted-foreground">
                                            {addr.address_line_1}
                                            {addr.address_line_2 && `, ${addr.address_line_2}`}
                                        </p>
                                        <p className="text-xs text-muted-foreground">
                                            {addr.city}, {addr.state_province} {addr.postal_code}
                                        </p>
                                    </div>
                                    <div className={`w-4 h-4 rounded-full border-2 ${
                                        selectedId === addr.id
                                            ? 'border-primary bg-primary'
                                            : 'border-muted-foreground'
                                    }`}>
                                        {selectedId === addr.id && (
                                            <div className="w-2 h-2 bg-white rounded-full m-0.5" />
                                        )}
                                    </div>
                                </div>
                            </div>
                        ))}
                    </div>

                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                            if (type === 'billing') {
                                setSelectedBillingId(null);
                                setShowNewBillingForm(true);
                            } else {
                                setSelectedShippingId(null);
                                setShowNewShippingForm(true);
                            }
                        }}
                        className="w-full"
                    >
                        <Plus className="h-4 w-4 mr-2" />
                        Add New {type.charAt(0).toUpperCase() + type.slice(1)} Address
                    </Button>
                </CardContent>
            </Card>
        );
    };

    const renderAddressForm = (
        address: Address,
        type: 'billing_address' | 'shipping_address',
        title: string,
        icon: React.ReactNode
    ) => {
        const addressType = type === 'billing_address' ? 'billing' : 'shipping';
        const selectedId = addressType === 'billing' ? selectedBillingId : selectedShippingId;
        const showNewForm = addressType === 'billing' ? showNewBillingForm : showNewShippingForm;

        // Don't show form if a saved address is selected and we're not showing new form
        if (selectedId && !showNewForm) {
            return null;
        }

        return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    {icon}
                    {title}
                </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor={`${type}-first-name`}>First Name *</Label>
                        <Input
                            id={`${type}-first-name`}
                            value={address.first_name}
                            onChange={(e) => handleAddressChange(type, 'first_name', e.target.value)}
                            className={errors[`${type}.first_name`] ? 'border-destructive' : ''}
                        />
                        {errors[`${type}.first_name`] && (
                            <p className="text-sm text-destructive">{errors[`${type}.first_name`]}</p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor={`${type}-last-name`}>Last Name *</Label>
                        <Input
                            id={`${type}-last-name`}
                            value={address.last_name}
                            onChange={(e) => handleAddressChange(type, 'last_name', e.target.value)}
                            className={errors[`${type}.last_name`] ? 'border-destructive' : ''}
                        />
                        {errors[`${type}.last_name`] && (
                            <p className="text-sm text-destructive">{errors[`${type}.last_name`]}</p>
                        )}
                    </div>
                </div>

                <div className="space-y-2">
                    <Label htmlFor={`${type}-company`}>Company (optional)</Label>
                    <Input
                        id={`${type}-company`}
                        value={address.company || ''}
                        onChange={(e) => handleAddressChange(type, 'company', e.target.value)}
                    />
                </div>

                <div className="space-y-2">
                    <Label htmlFor={`${type}-address1`}>Address Line 1 *</Label>
                    <Input
                        id={`${type}-address1`}
                        placeholder="Street address, P.O. box, company name"
                        value={address.address_line_1}
                        onChange={(e) => handleAddressChange(type, 'address_line_1', e.target.value)}
                        className={errors[`${type}.address_line_1`] ? 'border-destructive' : ''}
                    />
                    {errors[`${type}.address_line_1`] && (
                        <p className="text-sm text-destructive">{errors[`${type}.address_line_1`]}</p>
                    )}
                </div>

                <div className="space-y-2">
                    <Label htmlFor={`${type}-address2`}>Address Line 2 (optional)</Label>
                    <Input
                        id={`${type}-address2`}
                        placeholder="Apartment, suite, unit, building, floor, etc."
                        value={address.address_line_2 || ''}
                        onChange={(e) => handleAddressChange(type, 'address_line_2', e.target.value)}
                    />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                        <Label htmlFor={`${type}-city`}>City *</Label>
                        <Input
                            id={`${type}-city`}
                            value={address.city}
                            onChange={(e) => handleAddressChange(type, 'city', e.target.value)}
                            className={errors[`${type}.city`] ? 'border-destructive' : ''}
                        />
                        {errors[`${type}.city`] && (
                            <p className="text-sm text-destructive">{errors[`${type}.city`]}</p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor={`${type}-state`}>Region *</Label>
                        <Select
                            value={address.state}
                            onValueChange={(value) => handleAddressChange(type, 'state', value)}
                        >
                            <SelectTrigger className={errors[`${type}.state`] ? 'border-destructive' : ''}>
                                <SelectValue placeholder="Select region" />
                            </SelectTrigger>
                            <SelectContent>
                                {PHILIPPINES_REGIONS.map((region) => (
                                    <SelectItem key={region} value={region}>
                                        {region}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                        {errors[`${type}.state`] && (
                            <p className="text-sm text-destructive">{errors[`${type}.state`]}</p>
                        )}
                    </div>

                    <div className="space-y-2">
                        <Label htmlFor={`${type}-postal`}>Postal Code *</Label>
                        <Input
                            id={`${type}-postal`}
                            value={address.postal_code}
                            onChange={(e) => handleAddressChange(type, 'postal_code', e.target.value)}
                            className={errors[`${type}.postal_code`] ? 'border-destructive' : ''}
                        />
                        {errors[`${type}.postal_code`] && (
                            <p className="text-sm text-destructive">{errors[`${type}.postal_code`]}</p>
                        )}
                    </div>
                </div>

                {type === 'billing_address' && (
                    <div className="space-y-2">
                        <Label htmlFor={`${type}-phone`}>Phone Number (optional)</Label>
                        <Input
                            id={`${type}-phone`}
                            type="tel"
                            placeholder="+63 ************"
                            value={address.phone || ''}
                            onChange={(e) => handleAddressChange(type, 'phone', e.target.value)}
                            className={errors[`${type}.phone`] ? 'border-destructive' : ''}
                        />
                        {errors[`${type}.phone`] && (
                            <p className="text-sm text-destructive">{errors[`${type}.phone`]}</p>
                        )}
                    </div>
                )}
            </CardContent>
        </Card>
        );
    };

    return (
        <div className="space-y-6">
            <div>
                <h2 className="text-2xl font-semibold mb-2">Shipping & Billing Information</h2>
                <p className="text-muted-foreground">
                    Enter your addresses and choose a shipping method
                </p>
            </div>

            {/* Saved Billing Addresses */}
            {!loadingAddresses && renderSavedAddressSelection('billing')}

            {/* Billing Address */}
            {renderAddressForm(
                formData.billing_address,
                'billing_address',
                'Billing Address',
                <Building className="h-5 w-5" />
            )}

            {/* Same as Billing Checkbox */}
            <div className="flex items-center space-x-2">
                <Checkbox
                    id="same-as-billing"
                    checked={formData.same_as_billing}
                    onCheckedChange={handleSameAsBillingChange}
                />
                <Label htmlFor="same-as-billing">
                    Shipping address is the same as billing address
                </Label>
            </div>

            {/* Shipping Address (if different) */}
            {!formData.same_as_billing && (
                <>
                    {/* Saved Shipping Addresses */}
                    {!loadingAddresses && renderSavedAddressSelection('shipping')}

                    {renderAddressForm(
                        formData.shipping_address,
                        'shipping_address',
                        'Shipping Address',
                        <MapPin className="h-5 w-5" />
                    )}
                </>
            )}

            {/* Shipping Methods */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Truck className="h-5 w-5" />
                        Shipping Method
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                    {SHIPPING_METHODS.map((method) => (
                        <div
                            key={method.id}
                            className={`p-4 border rounded-lg cursor-pointer transition-all ${
                                formData.shipping_method?.id === method.id
                                    ? 'border-primary bg-primary/5'
                                    : 'border-border hover:border-primary/50'
                            }`}
                            onClick={() => handleShippingMethodChange(method)}
                        >
                            <div className="flex items-center justify-between">
                                <div className="flex-1">
                                    <div className="flex items-center gap-3">
                                        <div className={`w-4 h-4 rounded-full border-2 ${
                                            formData.shipping_method?.id === method.id
                                                ? 'border-primary bg-primary'
                                                : 'border-muted-foreground'
                                        }`}>
                                            {formData.shipping_method?.id === method.id && (
                                                <div className="w-2 h-2 bg-white rounded-full m-0.5" />
                                            )}
                                        </div>
                                        <div>
                                            <h4 className="font-medium">{method.name}</h4>
                                            <p className="text-sm text-muted-foreground">{method.description}</p>
                                            <p className="text-sm text-muted-foreground">
                                                {method.estimated_days} • {method.carrier}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div className="text-right">
                                    <p className="font-semibold">
                                        {method.price === 0 ? 'Free' : `₱${method.price.toLocaleString()}`}
                                    </p>
                                </div>
                            </div>
                        </div>
                    ))}
                    {errors.shipping_method && (
                        <p className="text-sm text-destructive">{errors.shipping_method}</p>
                    )}
                </CardContent>
            </Card>

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-4">
                <Button variant="outline" onClick={onPrev} size="lg" className="px-8">
                    Back to Contact
                </Button>
                <Button onClick={handleNext} size="lg" className="px-8">
                    Continue to Payment
                </Button>
            </div>
        </div>
    );
};

export default ShippingStep;
