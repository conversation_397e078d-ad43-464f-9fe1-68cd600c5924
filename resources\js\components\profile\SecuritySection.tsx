import { Shield, Key, Smartphone, Monitor, Save, LoaderCircle } from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { apiClient } from '@/bootstrap';

import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';

import { Separator } from '@/components/ui/separator';
import InputError from '@/components/input-error';

interface SecuritySettings {
    two_factor_enabled: boolean;
    login_notifications: boolean;
    logout_other_devices: boolean;
    session_timeout_minutes: number;
}

export default function SecuritySection() {
    const [securitySettings, setSecuritySettings] = useState<SecuritySettings | null>(null);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState<string | null>(null);
    
    // Password change form
    const [passwordForm, setPasswordForm] = useState({
        current_password: '',
        password: '',
        password_confirmation: '',
    });
    const [passwordErrors, setPasswordErrors] = useState<Record<string, string>>({});
    const [changingPassword, setChangingPassword] = useState(false);

    const fetchSecuritySettings = async () => {
        try {
            setLoading(true);
            const response = await apiClient.get('/security');
            if (response.data.success) {
                setSecuritySettings(response.data.data);
            } else {
                setError('Failed to load security settings');
            }
        } catch (err) {
            setError('Failed to load security settings');
            console.error('Error fetching security settings:', err);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchSecuritySettings();
    }, []);

    const handleSaveSettings = async () => {
        if (!securitySettings) return;

        try {
            setSaving(true);
            const response = await apiClient.put('/security', securitySettings);
            if (response.data.success) {
                // Success feedback could be added here
            }
        } catch (err) {
            console.error('Error saving security settings:', err);
            alert('Failed to save security settings');
        } finally {
            setSaving(false);
        }
    };

    const handlePasswordChange = async (e: React.FormEvent) => {
        e.preventDefault();
        setPasswordErrors({});

        if (passwordForm.password !== passwordForm.password_confirmation) {
            setPasswordErrors({ password_confirmation: 'Passwords do not match' });
            return;
        }

        try {
            setChangingPassword(true);
            const response = await apiClient.put('/security/password', passwordForm);
            if (response.data.success) {
                setPasswordForm({
                    current_password: '',
                    password: '',
                    password_confirmation: '',
                });
                alert('Password changed successfully');
            }
        } catch (err: unknown) {
            if (err && typeof err === 'object' && 'response' in err) {
                const error = err as { response?: { data?: { errors?: Record<string, string> } } };
                if (error.response?.data?.errors) {
                    setPasswordErrors(error.response.data.errors);
                } else {
                    setPasswordErrors({ general: 'Failed to change password' });
                }
            } else {
                setPasswordErrors({ general: 'Failed to change password' });
            }
        } finally {
            setChangingPassword(false);
        }
    };

    const handleToggleTwoFactor = async () => {
        if (!securitySettings) return;

        try {
            if (securitySettings.two_factor_enabled) {
                // Disable 2FA
                const password = prompt('Enter your password to disable two-factor authentication:');
                if (!password) return;

                const response = await apiClient.delete('/security/two-factor/disable', {
                    data: { password }
                });
                if (response.data.success) {
                    setSecuritySettings({ ...securitySettings, two_factor_enabled: false });
                }
            } else {
                // Enable 2FA
                const response = await apiClient.post('/security/two-factor/enable');
                if (response.data.success) {
                    setSecuritySettings({ ...securitySettings, two_factor_enabled: true });
                    alert('Two-factor authentication enabled successfully');
                }
            }
        } catch (err) {
            console.error('Error toggling 2FA:', err);
            alert('Failed to update two-factor authentication');
        }
    };

    const updateSetting = (key: keyof SecuritySettings, value: boolean | number) => {
        if (!securitySettings) return;
        setSecuritySettings({ ...securitySettings, [key]: value });
    };

    if (loading) {
        return (
            <Card>
                <CardContent className="p-6">
                    <div className="flex items-center justify-center">
                        <LoaderCircle className="w-6 h-6 animate-spin mr-2" />
                        Loading security settings...
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (error || !securitySettings) {
        return (
            <Card>
                <CardContent className="p-6">
                    <div className="text-center text-red-600">
                        <p>{error || 'Failed to load security settings'}</p>
                        <Button onClick={fetchSecuritySettings} className="mt-2">
                            Try Again
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            {/* Password Change */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center">
                        <Key className="w-5 h-5 mr-2" />
                        Change Password
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                        Update your password to keep your account secure
                    </p>
                </CardHeader>
                <CardContent>
                    <form onSubmit={handlePasswordChange} className="space-y-4">
                        {passwordErrors.general && (
                            <div className="bg-red-50 border border-red-200 rounded-md p-3">
                                <p className="text-sm text-red-600">{passwordErrors.general}</p>
                            </div>
                        )}
                        
                        <div className="space-y-2">
                            <Label htmlFor="current_password">Current Password</Label>
                            <Input
                                id="current_password"
                                type="password"
                                value={passwordForm.current_password}
                                onChange={(e) => setPasswordForm({ ...passwordForm, current_password: e.target.value })}
                                disabled={changingPassword}
                                required
                            />
                            <InputError message={passwordErrors.current_password} />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="password">New Password</Label>
                            <Input
                                id="password"
                                type="password"
                                value={passwordForm.password}
                                onChange={(e) => setPasswordForm({ ...passwordForm, password: e.target.value })}
                                disabled={changingPassword}
                                required
                            />
                            <InputError message={passwordErrors.password} />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="password_confirmation">Confirm New Password</Label>
                            <Input
                                id="password_confirmation"
                                type="password"
                                value={passwordForm.password_confirmation}
                                onChange={(e) => setPasswordForm({ ...passwordForm, password_confirmation: e.target.value })}
                                disabled={changingPassword}
                                required
                            />
                            <InputError message={passwordErrors.password_confirmation} />
                        </div>

                        <Button type="submit" disabled={changingPassword}>
                            {changingPassword ? (
                                <LoaderCircle className="w-4 h-4 mr-2 animate-spin" />
                            ) : (
                                <Save className="w-4 h-4 mr-2" />
                            )}
                            Change Password
                        </Button>
                    </form>
                </CardContent>
            </Card>

            {/* Two-Factor Authentication */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center">
                        <Smartphone className="w-5 h-5 mr-2" />
                        Two-Factor Authentication
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                        Add an extra layer of security to your account
                    </p>
                </CardHeader>
                <CardContent>
                    <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                            <div className={`w-3 h-3 rounded-full ${
                                securitySettings.two_factor_enabled ? 'bg-green-500' : 'bg-gray-300'
                            }`} />
                            <div>
                                <p className="font-medium">
                                    Two-factor authentication is {securitySettings.two_factor_enabled ? 'enabled' : 'disabled'}
                                </p>
                                <p className="text-sm text-muted-foreground">
                                    {securitySettings.two_factor_enabled 
                                        ? 'Your account is protected with 2FA'
                                        : 'Enable 2FA to secure your account'
                                    }
                                </p>
                            </div>
                        </div>
                        <Button
                            variant={securitySettings.two_factor_enabled ? 'destructive' : 'default'}
                            onClick={handleToggleTwoFactor}
                        >
                            {securitySettings.two_factor_enabled ? 'Disable' : 'Enable'} 2FA
                        </Button>
                    </div>
                </CardContent>
            </Card>

            {/* Login & Session Settings */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center">
                        <Monitor className="w-5 h-5 mr-2" />
                        Login & Session Settings
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                        Manage your login preferences and session security
                    </p>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="login_notifications"
                                checked={securitySettings.login_notifications}
                                onCheckedChange={(checked) => updateSetting('login_notifications', checked)}
                            />
                            <Label htmlFor="login_notifications">
                                Send me notifications when someone logs into my account
                            </Label>
                        </div>
                        
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="logout_other_devices"
                                checked={securitySettings.logout_other_devices}
                                onCheckedChange={(checked) => updateSetting('logout_other_devices', checked)}
                            />
                            <Label htmlFor="logout_other_devices">
                                Automatically log out other devices when I change my password
                            </Label>
                        </div>
                    </div>

                    <Separator />

                    <div className="space-y-2">
                        <Label htmlFor="session_timeout">Session Timeout (minutes)</Label>
                        <Input
                            id="session_timeout"
                            type="number"
                            min="15"
                            max="480"
                            value={securitySettings.session_timeout_minutes}
                            onChange={(e) => updateSetting('session_timeout_minutes', parseInt(e.target.value))}
                            className="w-32"
                        />
                        <p className="text-xs text-muted-foreground">
                            Automatically log out after this many minutes of inactivity (15-480 minutes)
                        </p>
                    </div>
                </CardContent>
            </Card>

            {/* Account Activity */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center">
                        <Shield className="w-5 h-5 mr-2" />
                        Account Activity
                    </CardTitle>
                    <p className="text-sm text-muted-foreground">
                        Monitor your account activity and manage trusted devices
                    </p>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="font-medium">Recent Login Activity</p>
                                <p className="text-sm text-muted-foreground">
                                    View your recent login sessions and locations
                                </p>
                            </div>
                            <Button variant="outline">
                                View Activity
                            </Button>
                        </div>
                        
                        <Separator />
                        
                        <div className="flex items-center justify-between">
                            <div>
                                <p className="font-medium">Trusted Devices</p>
                                <p className="text-sm text-muted-foreground">
                                    Manage devices that don't require 2FA
                                </p>
                            </div>
                            <Button variant="outline">
                                Manage Devices
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <div className="flex justify-end">
                <Button onClick={handleSaveSettings} disabled={saving}>
                    {saving ? (
                        <LoaderCircle className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                        <Save className="w-4 h-4 mr-2" />
                    )}
                    Save Security Settings
                </Button>
            </div>
        </div>
    );
}
