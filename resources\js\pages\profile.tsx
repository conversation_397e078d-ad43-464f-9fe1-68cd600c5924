import { Head, usePage } from '@inertiajs/react';
import { User, MapPin, CreditCard, Shield, Package, Bell } from 'lucide-react';
import React, { useState } from 'react';

// Import customer components
import Footer from '@/components/customer/Footer';
import Header from '@/components/customer/Header';
import { CartProviderWrapper } from '@/components/CartProviderWrapper';

// Import UI components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

// Import profile components
import PersonalInfoSection from '@/components/profile/PersonalInfoSection';
import AddressManagementSection from '@/components/profile/AddressManagementSection';
import PaymentMethodsSection from '@/components/profile/PaymentMethodsSection';
import PreferencesSection from '@/components/profile/PreferencesSection';
import SecuritySection from '@/components/profile/SecuritySection';
import OrderHistorySection from '@/components/profile/OrderHistorySection';

// Types
import { type SharedData } from '@/types';

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
interface ProfilePageContentProps {}

const ProfilePageContent: React.FC<ProfilePageContentProps> = () => {
    const { auth } = usePage<SharedData>().props;
    const [activeTab, setActiveTab] = useState('personal');

    if (!auth?.user) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <Card className="w-full max-w-md">
                    <CardContent className="p-6 text-center">
                        <h2 className="text-xl font-semibold mb-2">Access Denied</h2>
                        <p className="text-muted-foreground mb-4">
                            You need to be logged in to access your profile.
                        </p>
                        <Button asChild>
                            <a href="/login">Sign In</a>
                        </Button>
                    </CardContent>
                </Card>
            </div>
        );
    }

    const tabItems = [
        {
            value: 'personal',
            label: 'Personal Info',
            icon: User,
            component: PersonalInfoSection,
        },
        {
            value: 'addresses',
            label: 'Addresses',
            icon: MapPin,
            component: AddressManagementSection,
        },
        {
            value: 'payment',
            label: 'Payment Methods',
            icon: CreditCard,
            component: PaymentMethodsSection,
        },
        {
            value: 'orders',
            label: 'Order History',
            icon: Package,
            component: OrderHistorySection,
        },
        {
            value: 'preferences',
            label: 'Preferences',
            icon: Bell,
            component: PreferencesSection,
        },
        {
            value: 'security',
            label: 'Security',
            icon: Shield,
            component: SecuritySection,
        },
    ];

    return (
        <div className="min-h-screen bg-background">
            <Header />
            
            <main className="container mx-auto px-4 py-8">
                {/* Page Header */}
                <div className="mb-8">
                    <h1 className="text-3xl font-bold tracking-tight">My Profile</h1>
                    <p className="text-muted-foreground mt-2">
                        Manage your account settings and preferences
                    </p>
                </div>

                {/* Profile Content */}
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
                    {/* Profile Sidebar */}
                    <div className="lg:col-span-1">
                        <Card>
                            <CardHeader className="pb-4">
                                <div className="flex items-center space-x-4">
                                    <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                                        <User className="w-6 h-6 text-primary" />
                                    </div>
                                    <div>
                                        <h3 className="font-semibold">{auth.user.name}</h3>
                                        <p className="text-sm text-muted-foreground">{auth.user.email}</p>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent className="pt-0">
                                <nav className="space-y-2">
                                    {tabItems.map((item) => {
                                        const Icon = item.icon;
                                        return (
                                            <Button
                                                key={item.value}
                                                variant={activeTab === item.value ? 'secondary' : 'ghost'}
                                                className="w-full justify-start"
                                                onClick={() => setActiveTab(item.value)}
                                            >
                                                <Icon className="w-4 h-4 mr-3" />
                                                {item.label}
                                            </Button>
                                        );
                                    })}
                                </nav>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Profile Content */}
                    <div className="lg:col-span-3">
                        <Tabs value={activeTab} onValueChange={setActiveTab}>
                            <TabsList className="hidden">
                                {tabItems.map((item) => (
                                    <TabsTrigger key={item.value} value={item.value}>
                                        {item.label}
                                    </TabsTrigger>
                                ))}
                            </TabsList>

                            {tabItems.map((item) => {
                                const Component = item.component;
                                return (
                                    <TabsContent key={item.value} value={item.value} className="mt-0">
                                        <Component />
                                    </TabsContent>
                                );
                            })}
                        </Tabs>
                    </div>
                </div>
            </main>

            <Footer />
        </div>
    );
};

export default function Profile() {
    return (
        <CartProviderWrapper>
            <Head title="My Profile" />
            <ProfilePageContent />
        </CartProviderWrapper>
    );
}
