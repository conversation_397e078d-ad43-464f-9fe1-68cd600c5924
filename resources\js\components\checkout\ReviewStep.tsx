import React, { useState, useCallback } from 'react';
import { Package, MapPin, CreditCard, FileText, AlertCircle, CheckCircle } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import ProductImage from '@/components/customer/ProductImage';

import { type CheckoutData, type CartItem, type Address } from '@/types/ecommerce';

interface ReviewStepProps {
    checkoutData: Partial<CheckoutData>;
    cartItems: CartItem[];
    onPrev: () => void;
    onPlaceOrder: () => void;
    loading: boolean;
}

const ReviewStep: React.FC<ReviewStepProps> = ({
    checkoutData,
    cartItems,
    onPrev,
    onPlaceOrder,
    loading
}) => {
    const [agreedToTerms, setAgreedToTerms] = useState(false);
    const [agreedToPrivacy, setAgreedToPrivacy] = useState(false);

    const subtotal = cartItems.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
    const shippingAmount = checkoutData.shipping_method?.price || 0;
    const taxAmount = (subtotal + shippingAmount) * 0.12; // 12% VAT
    const discountAmount = checkoutData.applied_coupon?.discount_amount || 0;
    const processingFee = (checkoutData.payment_method?.processing_fee || 0);
    const totalAmount = subtotal + shippingAmount + taxAmount - discountAmount + processingFee;

    const canPlaceOrder = agreedToTerms && agreedToPrivacy && !loading;

    // Check if we have minimum required data
    const hasRequiredData = checkoutData.customer_info?.email &&
                           checkoutData.billing_address &&
                           checkoutData.shipping_address &&
                           checkoutData.payment_method;

    const handlePlaceOrder = useCallback(() => {
        if (canPlaceOrder && hasRequiredData) {
            onPlaceOrder();
        }
    }, [canPlaceOrder, hasRequiredData, onPlaceOrder]);

    const formatAddress = (address: Address | undefined) => {
        if (!address) return 'Not provided';

        const parts = [
            address.address_line_1,
            address.address_line_2,
            address.city,
            address.state,
            address.postal_code,
            address.country
        ].filter(Boolean);

        return parts.join(', ');
    };

    const getPaymentMethodDisplay = () => {
        if (!checkoutData.payment_method) return 'Not selected';
        
        const method = checkoutData.payment_method;
        let display = method.name;
        
        if ((method.processing_fee || 0) > 0) {
            display += ` (+₱${method.processing_fee} fee)`;
        }
        
        return display;
    };

    // Show error if required data is missing
    if (!hasRequiredData) {
        return (
            <div className="space-y-6">
                <div>
                    <h2 className="text-2xl font-semibold mb-2">Review Your Order</h2>
                    <p className="text-muted-foreground">
                        Please review all details before placing your order
                    </p>
                </div>

                <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                        Some required information is missing. Please go back and complete all previous steps.
                    </AlertDescription>
                </Alert>

                <div className="flex justify-between pt-4">
                    <Button variant="outline" onClick={onPrev} size="lg" className="px-8">
                        Back to Payment
                    </Button>
                    <Button disabled size="lg" className="px-8">
                        Complete Required Steps First
                    </Button>
                </div>
            </div>
        );
    }

    return (
        <div className="space-y-6">
            <div>
                <h2 className="text-2xl font-semibold mb-2">Review Your Order</h2>
                <p className="text-muted-foreground">
                    Please review all details before placing your order
                </p>
            </div>

            {/* Order Items */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <Package className="h-5 w-5" />
                        Order Items ({cartItems.length})
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    {cartItems.map((item) => (
                        <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg">
                            <div className="flex-shrink-0">
                                <ProductImage
                                    src={item.product.images[0]}
                                    alt={item.product.name}
                                    className="w-16 h-16 rounded-lg object-cover"
                                />
                            </div>
                            <div className="flex-1 min-w-0">
                                <h4 className="font-medium truncate">{item.product.name}</h4>
                                <p className="text-sm text-muted-foreground">SKU: {item.product.sku}</p>
                                <p className="text-sm text-muted-foreground">Quantity: {item.quantity}</p>
                            </div>
                            <div className="text-right">
                                <p className="font-medium">₱{(item.product.price * item.quantity).toLocaleString()}</p>
                                <p className="text-sm text-muted-foreground">₱{item.product.price.toLocaleString()} each</p>
                            </div>
                        </div>
                    ))}
                </CardContent>
            </Card>

            {/* Customer Information */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <FileText className="h-5 w-5" />
                        Customer Information
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <Label className="text-sm font-medium text-muted-foreground">Email</Label>
                            <p className="font-medium">{checkoutData.customer_info?.email || 'Not provided'}</p>
                        </div>
                        <div>
                            <Label className="text-sm font-medium text-muted-foreground">Phone</Label>
                            <p className="font-medium">{checkoutData.customer_info?.phone || 'Not provided'}</p>
                        </div>
                    </div>
                    {checkoutData.customer_info?.create_account && (
                        <div className="bg-green-50 dark:bg-green-950/20 p-3 rounded-lg border border-green-200 dark:border-green-800">
                            <p className="text-sm text-green-800 dark:text-green-200">
                                ✓ Account will be created after order completion
                            </p>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Addresses */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <MapPin className="h-5 w-5" />
                        Delivery & Billing Addresses
                    </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <Label className="text-sm font-medium text-muted-foreground">Shipping Address</Label>
                            <div className="mt-1">
                                <p className="font-medium">
                                    {checkoutData.shipping_address?.first_name} {checkoutData.shipping_address?.last_name}
                                </p>
                                {checkoutData.shipping_address?.company && (
                                    <p className="text-sm text-muted-foreground">{checkoutData.shipping_address.company}</p>
                                )}
                                <p className="text-sm">{formatAddress(checkoutData.shipping_address)}</p>
                            </div>
                        </div>
                        <div>
                            <Label className="text-sm font-medium text-muted-foreground">Billing Address</Label>
                            <div className="mt-1">
                                <p className="font-medium">
                                    {checkoutData.billing_address?.first_name} {checkoutData.billing_address?.last_name}
                                </p>
                                {checkoutData.billing_address?.company && (
                                    <p className="text-sm text-muted-foreground">{checkoutData.billing_address.company}</p>
                                )}
                                <p className="text-sm">{formatAddress(checkoutData.billing_address)}</p>
                                {checkoutData.billing_address?.phone && (
                                    <p className="text-sm text-muted-foreground">Phone: {checkoutData.billing_address.phone}</p>
                                )}
                            </div>
                        </div>
                    </div>
                    
                    <Separator />
                    
                    <div>
                        <Label className="text-sm font-medium text-muted-foreground">Shipping Method</Label>
                        <div className="mt-1">
                            <p className="font-medium">{checkoutData.shipping_method?.name || 'Not selected'}</p>
                            <p className="text-sm text-muted-foreground">
                                {checkoutData.shipping_method?.description} • {checkoutData.shipping_method?.estimated_days}
                            </p>
                            <p className="text-sm font-medium">
                                {checkoutData.shipping_method?.price === 0 ? 'Free' : `₱${checkoutData.shipping_method?.price.toLocaleString()}`}
                            </p>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Payment Information */}
            <Card>
                <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                        <CreditCard className="h-5 w-5" />
                        Payment Information
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    <div>
                        <Label className="text-sm font-medium text-muted-foreground">Payment Method</Label>
                        <p className="font-medium mt-1">{getPaymentMethodDisplay()}</p>
                        <p className="text-sm text-muted-foreground mt-1">
                            {checkoutData.payment_method?.description}
                        </p>
                    </div>
                </CardContent>
            </Card>

            {/* Order Summary */}
            <Card>
                <CardHeader>
                    <CardTitle>Order Summary</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                    <div className="flex justify-between">
                        <span>Subtotal ({cartItems.length} items)</span>
                        <span>₱{subtotal.toLocaleString()}</span>
                    </div>
                    
                    <div className="flex justify-between">
                        <span>Shipping</span>
                        <span>
                            {shippingAmount === 0 ? (
                                <span className="text-green-600">Free</span>
                            ) : (
                                `₱${shippingAmount.toLocaleString()}`
                            )}
                        </span>
                    </div>
                    
                    <div className="flex justify-between">
                        <span>Tax (VAT 12%)</span>
                        <span>₱{taxAmount.toLocaleString()}</span>
                    </div>
                    
                    {discountAmount > 0 && (
                        <div className="flex justify-between text-green-600">
                            <span>Discount ({checkoutData.applied_coupon?.coupon.code})</span>
                            <span>-₱{discountAmount.toLocaleString()}</span>
                        </div>
                    )}
                    
                    {processingFee > 0 && (
                        <div className="flex justify-between">
                            <span>Processing Fee</span>
                            <span>₱{processingFee.toLocaleString()}</span>
                        </div>
                    )}
                    
                    <Separator />
                    
                    <div className="flex justify-between text-lg font-bold">
                        <span>Total</span>
                        <span>₱{totalAmount.toLocaleString()}</span>
                    </div>
                </CardContent>
            </Card>

            {/* Terms and Conditions */}
            <Card>
                <CardContent className="p-6 space-y-4">
                    <div className="flex items-start space-x-2">
                        <Checkbox
                            id="terms"
                            checked={agreedToTerms}
                            onCheckedChange={(checked) => setAgreedToTerms(checked === true)}
                        />
                        <Label htmlFor="terms" className="text-sm leading-relaxed">
                            I agree to the{' '}
                            <a href="/terms" target="_blank" className="text-primary hover:underline">
                                Terms and Conditions
                            </a>{' '}
                            and understand the return and refund policy.
                        </Label>
                    </div>
                    
                    <div className="flex items-start space-x-2">
                        <Checkbox
                            id="privacy"
                            checked={agreedToPrivacy}
                            onCheckedChange={(checked) => setAgreedToPrivacy(checked === true)}
                        />
                        <Label htmlFor="privacy" className="text-sm leading-relaxed">
                            I agree to the{' '}
                            <a href="/privacy" target="_blank" className="text-primary hover:underline">
                                Privacy Policy
                            </a>{' '}
                            and consent to the processing of my personal data.
                        </Label>
                    </div>
                </CardContent>
            </Card>

            {/* Order Placement */}
            {(!canPlaceOrder || !hasRequiredData) && (
                <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                        {!hasRequiredData
                            ? 'Please complete all previous steps before placing your order.'
                            : 'Please agree to the Terms and Conditions and Privacy Policy to continue.'
                        }
                    </AlertDescription>
                </Alert>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-4">
                <Button variant="outline" onClick={onPrev} size="lg" className="px-8" disabled={loading}>
                    Back to Payment
                </Button>
                <Button 
                    onClick={handlePlaceOrder} 
                    size="lg" 
                    className="px-8"
                    disabled={!canPlaceOrder || !hasRequiredData}
                >
                    {loading ? (
                        <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                            Placing Order...
                        </>
                    ) : (
                        <>
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Place Order - ₱{totalAmount.toLocaleString()}
                        </>
                    )}
                </Button>
            </div>
        </div>
    );
};

export default ReviewStep;
