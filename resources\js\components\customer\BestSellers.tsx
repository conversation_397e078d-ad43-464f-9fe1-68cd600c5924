import React from 'react';
import ProductCard from './ProductCard';

interface Product {
    id: number;
    name: string;
    price: string;
    originalPrice: string;
    image: string;
    rating: number;
    reviews: number;
}

const BestSellers: React.FC = () => {
    const bestSellers: Product[] = [
        {
            id: 1,
            name: 'Organic Bananas',
            price: '$2.99',
            originalPrice: '$3.49',
            image: '🍌',
            rating: 4.8,
            reviews: 324,
        },
        {
            id: 2,
            name: 'Fresh Strawberries',
            price: '$4.99',
            originalPrice: '$5.99',
            image: '🍓',
            rating: 4.9,
            reviews: 567,
        },
        {
            id: 3,
            name: 'Organic Whole Milk',
            price: '$5.99',
            originalPrice: '$6.49',
            image: '🥛',
            rating: 4.7,
            reviews: 289,
        },
        {
            id: 4,
            name: 'Grass-Fed Ground Beef',
            price: '$8.99',
            originalPrice: '$9.99',
            image: '🥩',
            rating: 4.6,
            reviews: 156,
        },
        {
            id: 5,
            name: 'Fresh Atlantic Salmon',
            price: '$12.99',
            originalPrice: '$14.99',
            image: '🐟',
            rating: 4.8,
            reviews: 203,
        },
        {
            id: 6,
            name: 'Organic Baby Spinach',
            price: '$3.99',
            originalPrice: '$4.49',
            image: '🥬',
            rating: 4.5,
            reviews: 178,
        },
    ];

    const handleAddToCart = (productId: number) => {
        console.log(`Added product ${productId} to cart`);
        // Implement cart logic here
    };

    return (
        <section className="bg-primary/5 py-16">
            <div className="container mx-auto px-4">
                <div className="mb-12 text-center">
                    <h2 className="font-display mb-4 text-3xl font-bold text-foreground md:text-4xl">Bestseller Products</h2>
                    <p className="text-muted-foreground">Our most popular items loved by customers</p>
                </div>

                <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
                    {bestSellers.map((product) => (
                        <ProductCard key={product.id} product={product} onAddToCart={handleAddToCart} />
                    ))}
                </div>
            </div>
        </section>
    );
};

export default BestSellers;
