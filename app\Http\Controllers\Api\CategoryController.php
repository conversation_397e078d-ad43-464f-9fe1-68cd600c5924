<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Category;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class CategoryController extends Controller
{
    /**
     * Display a listing of categories.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Category::with(['parent', 'children'])
            ->where('is_active', true);

        // Filter by parent category
        if ($request->has('parent_id')) {
            if ($request->get('parent_id') === 'null' || $request->get('parent_id') === '') {
                $query->whereNull('parent_id');
            } else {
                $query->where('parent_id', $request->get('parent_id'));
            }
        }

        // Search functionality
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'ILIKE', "%{$search}%")
                  ->orWhere('description', 'ILIKE', "%{$search}%");
            });
        }

        // Hierarchical structure option
        if ($request->get('hierarchical') === 'true') {
            $categories = $query->whereNull('parent_id')
                ->orderBy('sort_order')
                ->orderBy('name')
                ->get();
            
            // Load children recursively
            $categories->load(['children' => function ($query) {
                $query->where('is_active', true)
                    ->orderBy('sort_order')
                    ->orderBy('name');
            }]);
        } else {
            // Flat structure with pagination
            $query->orderBy('sort_order')->orderBy('name');
            $perPage = min($request->get('per_page', 20), 100);
            $categories = $query->paginate($perPage);
            
            return response()->json([
                'success' => true,
                'data' => $categories->items(),
                'meta' => [
                    'current_page' => $categories->currentPage(),
                    'last_page' => $categories->lastPage(),
                    'per_page' => $categories->perPage(),
                    'total' => $categories->total(),
                ]
            ]);
        }

        return response()->json([
            'success' => true,
            'data' => $categories
        ]);
    }

    /**
     * Store a newly created category.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:categories',
            'description' => 'nullable|string',
            'image' => 'nullable|string|max:500',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'parent_id' => 'nullable|exists:categories,id',
        ]);

        // Prevent circular references
        if ($validated['parent_id'] ?? null) {
            $parent = Category::find($validated['parent_id']);
            if ($parent && $parent->parent_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot create more than 2 levels of categories',
                    'errors' => ['parent_id' => ['Maximum category depth exceeded']]
                ], 422);
            }
        }

        $category = Category::create($validated);
        $category->load(['parent', 'children']);

        return response()->json([
            'success' => true,
            'message' => 'Category created successfully',
            'data' => $category
        ], 201);
    }

    /**
     * Display the specified category.
     */
    public function show(Category $category): JsonResponse
    {
        $category->load(['parent', 'children', 'products' => function ($query) {
            $query->active()->with('reviews');
        }]);

        return response()->json([
            'success' => true,
            'data' => $category
        ]);
    }

    /**
     * Update the specified category.
     */
    public function update(Request $request, Category $category): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'slug' => ['sometimes', 'required', 'string', 'max:255', Rule::unique('categories')->ignore($category->id)],
            'description' => 'nullable|string',
            'image' => 'nullable|string|max:500',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'is_active' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
            'parent_id' => 'nullable|exists:categories,id',
        ]);

        // Prevent circular references and self-reference
        if (isset($validated['parent_id'])) {
            if ($validated['parent_id'] == $category->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'Category cannot be its own parent',
                    'errors' => ['parent_id' => ['Cannot set category as its own parent']]
                ], 422);
            }

            if ($validated['parent_id']) {
                $parent = Category::find($validated['parent_id']);
                if ($parent && $parent->parent_id) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Cannot create more than 2 levels of categories',
                        'errors' => ['parent_id' => ['Maximum category depth exceeded']]
                    ], 422);
                }
            }
        }

        $category->update($validated);
        $category->load(['parent', 'children']);

        return response()->json([
            'success' => true,
            'message' => 'Category updated successfully',
            'data' => $category
        ]);
    }

    /**
     * Remove the specified category.
     */
    public function destroy(Category $category): JsonResponse
    {
        // Check if category has products
        if ($category->products()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete category with associated products',
                'errors' => ['category' => ['Category has associated products']]
            ], 422);
        }

        // Check if category has children
        if ($category->children()->count() > 0) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete category with subcategories',
                'errors' => ['category' => ['Category has subcategories']]
            ], 422);
        }

        $category->delete();

        return response()->json([
            'success' => true,
            'message' => 'Category deleted successfully'
        ]);
    }

    /**
     * Get products for a specific category.
     */
    public function products(Category $category, Request $request): JsonResponse
    {
        $query = $category->products()
            ->with(['reviews'])
            ->active();

        // Include products from subcategories
        if ($request->get('include_subcategories') === 'true') {
            $categoryIds = [$category->id];
            $categoryIds = array_merge($categoryIds, $category->children()->pluck('id')->toArray());
            
            $query = $category->products()
                ->with(['reviews'])
                ->active()
                ->whereIn('category_id', $categoryIds);
        }

        // Search within category
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('name', 'ILIKE', "%{$search}%")
                  ->orWhere('description', 'ILIKE', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'name');
        $sortOrder = $request->get('sort_order', 'asc');
        
        $allowedSorts = ['name', 'price', 'created_at'];
        if (in_array($sortBy, $allowedSorts)) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $perPage = min($request->get('per_page', 15), 100);
        $products = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $products->items(),
            'meta' => [
                'current_page' => $products->currentPage(),
                'last_page' => $products->lastPage(),
                'per_page' => $products->perPage(),
                'total' => $products->total(),
            ]
        ]);
    }
}
