import { usePage, useForm } from '@inertiajs/react';
import { Edit, Save, X, LoaderCircle } from 'lucide-react';
import React, { useState } from 'react';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { type SharedData } from '@/types';

interface PersonalInfoForm {
    name: string;
    email: string;
    phone: string;
    date_of_birth: string;
    [key: string]: string; // Add index signature for useForm compatibility
}

export default function PersonalInfoSection() {
    const { auth } = usePage<SharedData>().props;
    const [isEditing, setIsEditing] = useState(false);

    const { data, setData, put, processing, errors, reset, isDirty } = useForm<PersonalInfoForm>({
        name: auth?.user?.name || '',
        email: auth?.user?.email || '',
        phone: auth?.user?.phone || '',
        date_of_birth: auth?.user?.date_of_birth || '',
    });

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        
        put('/api/v1/user', {
            onSuccess: () => {
                setIsEditing(false);
            },
            onError: () => {
                // Keep editing mode on error
            }
        });
    };

    const handleCancel = () => {
        reset();
        setIsEditing(false);
    };

    const formatDate = (dateString: string) => {
        if (!dateString) return 'Not set';
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const formatPhone = (phone: string) => {
        if (!phone) return 'Not set';
        // Format Philippine phone numbers
        const cleaned = phone.replace(/\D/g, '');
        if (cleaned.startsWith('63')) {
            return `+${cleaned.slice(0, 2)} ${cleaned.slice(2, 5)} ${cleaned.slice(5, 8)} ${cleaned.slice(8)}`;
        }
        if (cleaned.startsWith('0')) {
            return `${cleaned.slice(0, 4)} ${cleaned.slice(4, 7)} ${cleaned.slice(7)}`;
        }
        return phone;
    };

    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <div>
                    <CardTitle>Personal Information</CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">
                        Update your personal details and contact information
                    </p>
                </div>
                {!isEditing && (
                    <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsEditing(true)}
                    >
                        <Edit className="w-4 h-4 mr-2" />
                        Edit
                    </Button>
                )}
            </CardHeader>
            <CardContent>
                {isEditing ? (
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div className="space-y-2">
                                <Label htmlFor="name">Full Name</Label>
                                <Input
                                    id="name"
                                    type="text"
                                    value={data.name}
                                    onChange={(e) => setData('name', e.target.value)}
                                    disabled={processing}
                                    placeholder="Enter your full name"
                                />
                                <InputError message={errors.name} />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="email">Email Address</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    value={data.email}
                                    onChange={(e) => setData('email', e.target.value)}
                                    disabled={processing}
                                    placeholder="Enter your email address"
                                />
                                <InputError message={errors.email} />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="phone">Phone Number</Label>
                                <Input
                                    id="phone"
                                    type="tel"
                                    value={data.phone}
                                    onChange={(e) => setData('phone', e.target.value)}
                                    disabled={processing}
                                    placeholder="09XX XXX XXXX"
                                />
                                <InputError message={errors.phone} />
                                <p className="text-xs text-muted-foreground">
                                    Philippine mobile number format
                                </p>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="date_of_birth">Date of Birth</Label>
                                <Input
                                    id="date_of_birth"
                                    type="date"
                                    value={data.date_of_birth}
                                    onChange={(e) => setData('date_of_birth', e.target.value)}
                                    disabled={processing}
                                />
                                <InputError message={errors.date_of_birth} />
                            </div>
                        </div>

                        <Separator />

                        <div className="flex justify-end space-x-3">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={handleCancel}
                                disabled={processing}
                            >
                                <X className="w-4 h-4 mr-2" />
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={processing || !isDirty}
                            >
                                {processing ? (
                                    <LoaderCircle className="w-4 h-4 mr-2 animate-spin" />
                                ) : (
                                    <Save className="w-4 h-4 mr-2" />
                                )}
                                Save Changes
                            </Button>
                        </div>
                    </form>
                ) : (
                    <div className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <Label className="text-sm font-medium text-muted-foreground">
                                    Full Name
                                </Label>
                                <p className="mt-1 text-sm">{auth?.user?.name || 'Not set'}</p>
                            </div>

                            <div>
                                <Label className="text-sm font-medium text-muted-foreground">
                                    Email Address
                                </Label>
                                <p className="mt-1 text-sm">{auth?.user?.email || 'Not set'}</p>
                            </div>

                            <div>
                                <Label className="text-sm font-medium text-muted-foreground">
                                    Phone Number
                                </Label>
                                <p className="mt-1 text-sm">{formatPhone(auth?.user?.phone || '')}</p>
                            </div>

                            <div>
                                <Label className="text-sm font-medium text-muted-foreground">
                                    Date of Birth
                                </Label>
                                <p className="mt-1 text-sm">{formatDate(auth?.user?.date_of_birth || '')}</p>
                            </div>
                        </div>

                        <Separator />

                        <div className="bg-muted/50 rounded-lg p-4">
                            <h4 className="font-medium mb-2">Account Status</h4>
                            <div className="flex items-center space-x-4 text-sm">
                                <div className="flex items-center">
                                    <div className={`w-2 h-2 rounded-full mr-2 ${
                                        auth?.user?.email_verified_at ? 'bg-green-500' : 'bg-yellow-500'
                                    }`} />
                                    Email {auth?.user?.email_verified_at ? 'Verified' : 'Unverified'}
                                </div>
                                <div className="flex items-center">
                                    <div className="w-2 h-2 rounded-full bg-blue-500 mr-2" />
                                    {auth?.user?.role === 'admin' ? 'Administrator' : 'Customer'}
                                </div>
                            </div>
                        </div>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
