import React from 'react';
import { usePage } from '@inertiajs/react';
import { type SharedData } from '@/types';
import { CartProvider } from '@/hooks/use-cart';

interface CartProviderWrapperProps {
    children: React.ReactNode;
}

export const CartProviderWrapper: React.FC<CartProviderWrapperProps> = ({ children }) => {
    const { auth } = usePage<SharedData>().props;
    const isAuthenticated = !!auth?.user;

    return (
        <CartProvider isAuthenticated={isAuthenticated}>
            {children}
        </CartProvider>
    );
};
