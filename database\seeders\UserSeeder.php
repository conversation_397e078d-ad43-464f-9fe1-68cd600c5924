<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin users
        $adminUsers = [
            [
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Store Manager',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Customer Service',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
        ];

        foreach ($adminUsers as $admin) {
            User::updateOrCreate(
                ['email' => $admin['email']],
                $admin
            );
        }

        // Create regular customers with diverse data
        $customers = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Maria Garcia',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'David Johnson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Sarah Wilson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Michael Brown',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Lisa Davis',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Robert Miller',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Jennifer Taylor',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Christopher Anderson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Amanda Martinez',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'James Thompson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Michelle White',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Daniel Harris',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Jessica Clark',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Matthew Lewis',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Ashley Robinson',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Joshua Walker',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Emily Hall',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Andrew Young',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
            [
                'name' => 'Stephanie King',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => now(),
            ],
        ];

        foreach ($customers as $customer) {
            User::updateOrCreate(
                ['email' => $customer['email']],
                $customer
            );
        }

        // Create some unverified users
        $unverifiedUsers = [
            [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => null,
            ],
            [
                'name' => 'Demo User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'email_verified_at' => null,
            ],
        ];

        foreach ($unverifiedUsers as $user) {
            User::updateOrCreate(
                ['email' => $user['email']],
                $user
            );
        }

        $this->command->info('Created ' . count($adminUsers) . ' admin users, ' . count($customers) . ' customers, and ' . count($unverifiedUsers) . ' unverified users.');
    }
}
