<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\ShoppingCart;
use App\Models\User;
use App\Models\Coupon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class CheckoutController extends Controller
{
    /**
     * Get available shipping methods
     */
    public function getShippingMethods(Request $request): JsonResponse
    {
        // In a real application, this would be dynamic based on location and cart contents
        $shippingMethods = [
            [
                'id' => 'standard',
                'name' => 'Standard Delivery',
                'description' => 'Regular delivery within Metro Manila',
                'price' => 150,
                'estimated_days' => '3-5 business days',
                'carrier' => 'LBC Express',
                'is_available' => true,
            ],
            [
                'id' => 'express',
                'name' => 'Express Delivery',
                'description' => 'Fast delivery within Metro Manila',
                'price' => 250,
                'estimated_days' => '1-2 business days',
                'carrier' => 'J&T Express',
                'is_available' => true,
            ],
            [
                'id' => 'same_day',
                'name' => 'Same Day Delivery',
                'description' => 'Same day delivery (Metro Manila only)',
                'price' => 400,
                'estimated_days' => 'Same day',
                'carrier' => 'Grab Express',
                'is_available' => true,
            ],
            [
                'id' => 'pickup',
                'name' => 'Store Pickup',
                'description' => 'Pick up from our store in Makati',
                'price' => 0,
                'estimated_days' => 'Ready in 2-4 hours',
                'carrier' => 'Self Pickup',
                'is_available' => true,
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $shippingMethods
        ]);
    }

    /**
     * Get available payment methods
     */
    public function getPaymentMethods(Request $request): JsonResponse
    {
        $paymentMethods = [
            [
                'id' => 'cod',
                'name' => 'Cash on Delivery',
                'description' => 'Pay when your order is delivered to your doorstep',
                'icon' => '💵',
                'is_available' => true,
                'requires_online_payment' => false,
                'processing_fee' => 0,
            ],
            [
                'id' => 'gcash',
                'name' => 'GCash',
                'description' => 'Pay securely using your GCash wallet',
                'icon' => '📱',
                'is_available' => true,
                'requires_online_payment' => true,
                'processing_fee' => 0,
            ],
            [
                'id' => 'paymaya',
                'name' => 'PayMaya',
                'description' => 'Pay using your PayMaya account or card',
                'icon' => '💳',
                'is_available' => true,
                'requires_online_payment' => true,
                'processing_fee' => 0,
            ],
            [
                'id' => 'bpi_online',
                'name' => 'BPI Online Banking',
                'description' => 'Pay directly from your BPI account',
                'icon' => '🏦',
                'is_available' => true,
                'requires_online_payment' => true,
                'processing_fee' => 15,
            ],
            [
                'id' => 'bdo_online',
                'name' => 'BDO Online Banking',
                'description' => 'Pay directly from your BDO account',
                'icon' => '🏦',
                'is_available' => true,
                'requires_online_payment' => true,
                'processing_fee' => 15,
            ],
            [
                'id' => 'paypal',
                'name' => 'PayPal',
                'description' => 'Pay securely with your PayPal account',
                'icon' => '🅿️',
                'is_available' => true,
                'requires_online_payment' => true,
                'processing_fee' => 0,
            ],
            [
                'id' => 'credit_card',
                'name' => 'Credit/Debit Card',
                'description' => 'Visa, Mastercard, American Express',
                'icon' => '💳',
                'is_available' => true,
                'requires_online_payment' => true,
                'processing_fee' => 0,
            ],
        ];

        return response()->json([
            'success' => true,
            'data' => $paymentMethods
        ]);
    }

    /**
     * Validate and apply coupon code
     */
    public function validateCoupon(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'code' => 'required|string',
            'subtotal' => 'required|numeric|min:0',
        ]);

        $coupon = Coupon::where('code', $validated['code'])
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('starts_at')
                    ->orWhere('starts_at', '<=', now());
            })
            ->where(function ($query) {
                $query->whereNull('expires_at')
                    ->orWhere('expires_at', '>=', now());
            })
            ->first();

        if (!$coupon) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired coupon code'
            ], 422);
        }

        // Check minimum amount
        if ($coupon->minimum_amount && $validated['subtotal'] < $coupon->minimum_amount) {
            return response()->json([
                'success' => false,
                'message' => "Minimum order amount of ₱{$coupon->minimum_amount} required for this coupon"
            ], 422);
        }

        // Check usage limits
        if ($coupon->usage_limit && $coupon->used_count >= $coupon->usage_limit) {
            return response()->json([
                'success' => false,
                'message' => 'This coupon has reached its usage limit'
            ], 422);
        }

        // Calculate discount
        $discountAmount = 0;
        if ($coupon->type === 'percentage') {
            $discountAmount = ($validated['subtotal'] * $coupon->value) / 100;
        } else {
            $discountAmount = $coupon->value;
        }

        // Apply maximum discount limit
        if ($coupon->maximum_discount && $discountAmount > $coupon->maximum_discount) {
            $discountAmount = $coupon->maximum_discount;
        }

        return response()->json([
            'success' => true,
            'data' => [
                'coupon' => $coupon,
                'discount_amount' => round($discountAmount, 2)
            ]
        ]);
    }

    /**
     * Calculate order totals
     */
    public function calculateTotals(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'cart_items' => 'required|array',
            'cart_items.*.product_id' => 'required|exists:products,id',
            'cart_items.*.quantity' => 'required|integer|min:1',
            'shipping_method_id' => 'nullable|string',
            'coupon_code' => 'nullable|string',
        ]);

        $subtotal = 0;
        $items = [];

        // Calculate subtotal
        foreach ($validated['cart_items'] as $cartItem) {
            $product = Product::findOrFail($cartItem['product_id']);
            $itemTotal = $product->price * $cartItem['quantity'];
            $subtotal += $itemTotal;
            
            $items[] = [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'quantity' => $cartItem['quantity'],
                'unit_price' => $product->price,
                'total_price' => $itemTotal,
            ];
        }

        // Calculate shipping
        $shippingAmount = 0;
        if ($validated['shipping_method_id']) {
            $shippingMethods = [
                'standard' => 150,
                'express' => 250,
                'same_day' => 400,
                'pickup' => 0,
            ];
            $shippingAmount = $shippingMethods[$validated['shipping_method_id']] ?? 0;
        }

        // Apply coupon if provided
        $discountAmount = 0;
        $appliedCoupon = null;
        if (!empty($validated['coupon_code'])) {
            $couponResponse = $this->validateCoupon(new Request([
                'code' => $validated['coupon_code'],
                'subtotal' => $subtotal
            ]));
            
            if ($couponResponse->getStatusCode() === 200) {
                $couponData = json_decode($couponResponse->getContent(), true);
                $discountAmount = $couponData['data']['discount_amount'];
                $appliedCoupon = $couponData['data']['coupon'];
            }
        }

        // Calculate tax (12% VAT on subtotal + shipping - discount)
        $taxableAmount = $subtotal + $shippingAmount - $discountAmount;
        $taxAmount = $taxableAmount * 0.12;

        // Calculate total
        $totalAmount = $subtotal + $shippingAmount + $taxAmount - $discountAmount;

        return response()->json([
            'success' => true,
            'data' => [
                'items' => $items,
                'subtotal' => round($subtotal, 2),
                'shipping_amount' => round($shippingAmount, 2),
                'tax_amount' => round($taxAmount, 2),
                'discount_amount' => round($discountAmount, 2),
                'total_amount' => round($totalAmount, 2),
                'applied_coupon' => $appliedCoupon,
            ]
        ]);
    }

    /**
     * Process checkout and create order
     */
    public function processCheckout(Request $request): JsonResponse
    {
        $validated = $request->validate([
            // Customer information
            'customer_info.email' => 'required|email',
            'customer_info.phone' => 'nullable|string',
            'customer_info.create_account' => 'boolean',
            'customer_info.password' => 'required_if:customer_info.create_account,true|min:8',
            
            // Addresses
            'billing_address.first_name' => 'required|string|max:255',
            'billing_address.last_name' => 'required|string|max:255',
            'billing_address.company' => 'nullable|string|max:255',
            'billing_address.address_line_1' => 'required|string|max:255',
            'billing_address.address_line_2' => 'nullable|string|max:255',
            'billing_address.city' => 'required|string|max:255',
            'billing_address.state' => 'required|string|max:255',
            'billing_address.postal_code' => 'required|string|max:20',
            'billing_address.country' => 'required|string|max:255',
            'billing_address.phone' => 'nullable|string|max:20',
            
            'shipping_address.first_name' => 'required|string|max:255',
            'shipping_address.last_name' => 'required|string|max:255',
            'shipping_address.company' => 'nullable|string|max:255',
            'shipping_address.address_line_1' => 'required|string|max:255',
            'shipping_address.address_line_2' => 'nullable|string|max:255',
            'shipping_address.city' => 'required|string|max:255',
            'shipping_address.state' => 'required|string|max:255',
            'shipping_address.postal_code' => 'required|string|max:20',
            'shipping_address.country' => 'required|string|max:255',
            
            // Order details
            'shipping_method_id' => 'required|string',
            'payment_method' => 'required|string',
            'payment_details' => 'nullable|array',
            'coupon_code' => 'nullable|string',
            'special_instructions' => 'nullable|string',
            'cart_items' => 'required|array|min:1',
            'cart_items.*.product_id' => 'required|exists:products,id',
            'cart_items.*.quantity' => 'required|integer|min:1',
        ]);

        return DB::transaction(function () use ($validated, $request) {
            $user = $request->user();
            
            // Create account if requested and user is not authenticated
            if (!$user && $validated['customer_info']['create_account']) {
                $user = User::create([
                    'name' => $validated['billing_address']['first_name'] . ' ' . $validated['billing_address']['last_name'],
                    'email' => $validated['customer_info']['email'],
                    'password' => Hash::make($validated['customer_info']['password']),
                    'email_verified_at' => now(),
                ]);
            }

            // Calculate totals
            $totalsResponse = $this->calculateTotals(new Request([
                'cart_items' => $validated['cart_items'],
                'shipping_method_id' => $validated['shipping_method_id'],
                'coupon_code' => $validated['coupon_code'] ?? null,
            ]));

            $totalsData = json_decode($totalsResponse->getContent(), true)['data'];

            // Generate order number
            $orderNumber = 'ORD-' . date('Y') . '-' . str_pad(Order::count() + 1, 6, '0', STR_PAD_LEFT);

            // Create order
            $order = Order::create([
                'order_number' => $orderNumber,
                'user_id' => $user?->id,
                'status' => 'pending',
                'subtotal' => $totalsData['subtotal'],
                'tax_amount' => $totalsData['tax_amount'],
                'shipping_amount' => $totalsData['shipping_amount'],
                'discount_amount' => $totalsData['discount_amount'],
                'total_amount' => $totalsData['total_amount'],
                'currency' => 'PHP',
                'payment_status' => $validated['payment_method'] === 'cod' ? 'pending' : 'pending',
                'payment_method' => $validated['payment_method'],
                'payment_reference' => $this->generatePaymentReference($validated['payment_method']),
                
                // Billing address
                'billing_first_name' => $validated['billing_address']['first_name'],
                'billing_last_name' => $validated['billing_address']['last_name'],
                'billing_email' => $validated['customer_info']['email'],
                'billing_phone' => $validated['billing_address']['phone'],
                'billing_company' => $validated['billing_address']['company'],
                'billing_address_line_1' => $validated['billing_address']['address_line_1'],
                'billing_address_line_2' => $validated['billing_address']['address_line_2'],
                'billing_city' => $validated['billing_address']['city'],
                'billing_state' => $validated['billing_address']['state'],
                'billing_postal_code' => $validated['billing_address']['postal_code'],
                'billing_country' => $validated['billing_address']['country'],
                
                // Shipping address
                'shipping_first_name' => $validated['shipping_address']['first_name'],
                'shipping_last_name' => $validated['shipping_address']['last_name'],
                'shipping_company' => $validated['shipping_address']['company'],
                'shipping_address_line_1' => $validated['shipping_address']['address_line_1'],
                'shipping_address_line_2' => $validated['shipping_address']['address_line_2'],
                'shipping_city' => $validated['shipping_address']['city'],
                'shipping_state' => $validated['shipping_address']['state'],
                'shipping_postal_code' => $validated['shipping_address']['postal_code'],
                'shipping_country' => $validated['shipping_address']['country'],
                'shipping_method' => $this->getShippingMethodName($validated['shipping_method_id']),
                
                'notes' => $validated['special_instructions'],
            ]);

            // Create order items
            foreach ($totalsData['items'] as $item) {
                $product = Product::find($item['product_id']);
                
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $item['product_id'],
                    'product_name' => $item['product_name'],
                    'product_sku' => $product->sku,
                    'quantity' => $item['quantity'],
                    'unit_price' => $item['unit_price'],
                    'total_price' => $item['total_price'],
                ]);

                // Update product stock
                $product->decrement('stock_quantity', $item['quantity']);
            }

            // Update coupon usage if applied
            if (!empty($validated['coupon_code']) && $totalsData['applied_coupon']) {
                $coupon = Coupon::where('code', $validated['coupon_code'])->first();
                if ($coupon) {
                    $coupon->increment('used_count');
                }
            }

            // Clear cart for authenticated users
            if ($user) {
                ShoppingCart::where('user_id', $user->id)->delete();
            }

            // For guest users, we'll clear the cart on the frontend

            return response()->json([
                'success' => true,
                'message' => 'Order placed successfully',
                'data' => [
                    'order' => $order->load('orderItems.product'),
                    'payment_url' => $this->getPaymentUrl($validated['payment_method'], $order),
                ]
            ]);
        });
    }

    /**
     * Generate payment reference based on payment method
     */
    private function generatePaymentReference(string $paymentMethod): string
    {
        $prefix = strtoupper(substr($paymentMethod, 0, 3));
        return $prefix . '-' . date('Ymd') . '-' . Str::random(8);
    }

    /**
     * Get shipping method name by ID
     */
    private function getShippingMethodName(string $methodId): string
    {
        $methods = [
            'standard' => 'Standard Delivery',
            'express' => 'Express Delivery',
            'same_day' => 'Same Day Delivery',
            'pickup' => 'Store Pickup',
        ];

        return $methods[$methodId] ?? 'Standard Delivery';
    }

    /**
     * Get payment URL for online payment methods
     */
    private function getPaymentUrl(string $paymentMethod, Order $order): ?string
    {
        // In a real application, this would generate actual payment URLs
        $onlinePaymentMethods = ['gcash', 'paymaya', 'bpi_online', 'bdo_online', 'paypal', 'credit_card'];
        
        if (in_array($paymentMethod, $onlinePaymentMethods)) {
            return route('payment.process', ['order' => $order->id, 'method' => $paymentMethod]);
        }

        return null;
    }
}
