import { <PERSON>, Heart, Minus, Plus, ShoppingCart, Star } from 'lucide-react';
import React, { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import ProductImage from './ProductImage';
import { useAddToCartAnimation, getAddToCartButtonText, getAddToCartButtonClasses } from '@/hooks/use-add-to-cart-animation';

// Utility functions for price handling
const formatPrice = (price: string | number | null | undefined): string => {
    if (price === null || price === undefined || price === '') {
        return '₱0.00';
    }

    const numericPrice = typeof price === 'string' ? parseFloat(price) : price;

    if (isNaN(numericPrice)) {
        return '₱0.00';
    }

    return `₱${numericPrice.toFixed(2)}`;
};

const parsePrice = (price: string | number | null | undefined): number => {
    if (price === null || price === undefined || price === '') {
        return 0;
    }

    const numericPrice = typeof price === 'string' ? parseFloat(price) : price;

    return isNaN(numericPrice) ? 0 : numericPrice;
};

interface Product {
    id: number;
    name: string;
    slug: string;
    description: string;
    short_description: string;
    sku: string;
    price: string; // API returns price as string
    compare_price?: string | null; // API returns compare_price as string or null
    stock_quantity: number;
    is_active: boolean;
    is_featured: boolean;
    images: string[];
    attributes: Record<string, unknown>;
    category: {
        id: number;
        name: string;
        slug: string;
    };
    reviews: unknown[];
    discount_percentage?: number;
}

interface ProductQuickViewProps {
    product: Product | null;
    isOpen: boolean;
    onClose: () => void;
    onAddToCart: (productId: number, quantity: number) => void;
}

const ProductQuickView: React.FC<ProductQuickViewProps> = ({ product, isOpen, onClose, onAddToCart }) => {
    const [quantity, setQuantity] = useState(1);
    const [selectedImageIndex, setSelectedImageIndex] = useState(0);

    // Use animation hook for add to cart functionality (always call hooks at top level)
    const {
        state: addToCartState,
        isLoading,
        isSuccess,
        handleAddToCart: animatedAddToCart
    } = useAddToCartAnimation({
        productId: product?.id || 0,
        quantity,
        onSuccess: () => {
            if (product) {
                // Call the original onAddToCart callback if provided
                onAddToCart(product.id, quantity);
                // Close modal after successful add to cart
                setTimeout(() => {
                    onClose();
                }, 1000); // Delay to show success state
            }
        }
    });

    if (!product) return null;

    const handleQuantityChange = (change: number) => {
        const newQuantity = quantity + change;
        if (newQuantity >= 1 && newQuantity <= product.stock_quantity) {
            setQuantity(newQuantity);
        }
    };

    // Get button text and classes based on animation state
    const buttonText = getAddToCartButtonText(addToCartState);
    const buttonClasses = getAddToCartButtonClasses(addToCartState, 'w-full');

    const averageRating = product.reviews.length > 0
        ? product.reviews.reduce((sum: number, review) => {
            const reviewObj = review as { rating?: number };
            return sum + (reviewObj.rating || 0);
        }, 0) / product.reviews.length
        : 0;

    const displayImage = product.images[selectedImageIndex] || '🛒';

    return (
        <Dialog open={isOpen} onOpenChange={onClose}>
            <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
                <DialogHeader>
                    <DialogTitle className="sr-only">Product Quick View</DialogTitle>
                </DialogHeader>

                <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    {/* Product Images */}
                    <div className="space-y-4">
                        {/* Main Image */}
                        <ProductImage
                            src={displayImage}
                            alt={product.name}
                            size="xl"
                            className="aspect-square w-full"
                        />

                        {/* Thumbnail Images */}
                        {product.images.length > 1 && (
                            <div className="flex gap-2 overflow-x-auto">
                                {product.images.map((image, index) => (
                                    <button
                                        key={index}
                                        onClick={() => setSelectedImageIndex(index)}
                                        className={`flex-shrink-0 rounded-md border-2 transition-colors ${
                                            selectedImageIndex === index ? 'border-primary' : 'border-transparent hover:border-gray-300'
                                        }`}
                                    >
                                        <ProductImage
                                            src={image}
                                            alt={`${product.name} thumbnail ${index + 1}`}
                                            size="sm"
                                            className="h-16 w-16"
                                        />
                                    </button>
                                ))}
                            </div>
                        )}
                    </div>

                    {/* Product Details */}
                    <div className="space-y-4">
                        {/* Category */}
                        <Badge variant="secondary">{product.category.name}</Badge>

                        {/* Product Name */}
                        <h2 className="text-2xl font-bold text-foreground">{product.name}</h2>

                        {/* Rating */}
                        {product.reviews.length > 0 && (
                            <div className="flex items-center gap-2">
                                <div className="flex text-yellow-400">
                                    {[...Array(5)].map((_, i) => (
                                        <Star key={i} className={`h-4 w-4 ${i < Math.floor(averageRating) ? 'fill-current' : ''}`} />
                                    ))}
                                </div>
                                <span className="text-sm text-muted-foreground">
                                    {averageRating.toFixed(1)} ({product.reviews.length} reviews)
                                </span>
                            </div>
                        )}

                        {/* Price */}
                        <div className="flex items-center gap-3">
                            <span className="text-3xl font-bold text-primary">{formatPrice(product.price)}</span>
                            {product.compare_price && parsePrice(product.compare_price) > parsePrice(product.price) && (
                                <>
                                    <span className="text-lg text-muted-foreground line-through">{formatPrice(product.compare_price)}</span>
                                    {product.discount_percentage && <Badge variant="destructive">-{product.discount_percentage}%</Badge>}
                                </>
                            )}
                        </div>

                        {/* Stock Status */}
                        <div className="flex items-center gap-2">
                            {product.stock_quantity > 0 ? (
                                <>
                                    <Badge variant="default" className="bg-green-100 text-green-800">
                                        In Stock
                                    </Badge>
                                    <span className="text-sm text-muted-foreground">{product.stock_quantity} available</span>
                                </>
                            ) : (
                                <Badge variant="destructive">Out of Stock</Badge>
                            )}
                        </div>

                        {/* Short Description */}
                        {product.short_description && <p className="text-muted-foreground">{product.short_description}</p>}

                        <Separator />

                        {/* Quantity Selector */}
                        {product.stock_quantity > 0 && (
                            <div className="space-y-4">
                                <div className="flex items-center gap-4">
                                    <span className="font-medium">Quantity:</span>
                                    <div className="flex items-center rounded-md border">
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleQuantityChange(-1)}
                                            disabled={quantity <= 1}
                                            className="h-8 w-8 p-0"
                                        >
                                            <Minus className="h-4 w-4" />
                                        </Button>
                                        <span className="min-w-[3rem] px-4 py-1 text-center">{quantity}</span>
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleQuantityChange(1)}
                                            disabled={quantity >= product.stock_quantity}
                                            className="h-8 w-8 p-0"
                                        >
                                            <Plus className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>

                                {/* Add to Cart Button */}
                                <div className="flex gap-2">
                                    <Button
                                        onClick={animatedAddToCart}
                                        className={buttonClasses.replace('w-full', 'flex-1')}
                                        size="lg"
                                        disabled={isLoading}
                                    >
                                        <div className="flex items-center justify-center gap-2">
                                            {isLoading && (
                                                <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                                            )}
                                            {isSuccess && <Check className="h-4 w-4" />}
                                            {!isLoading && !isSuccess && <ShoppingCart className="h-4 w-4" />}
                                            <span>
                                                {isSuccess ? buttonText : `${buttonText} - ${formatPrice(parsePrice(product.price) * quantity)}`}
                                            </span>
                                        </div>
                                    </Button>
                                    <Button variant="outline" size="lg" className="px-3">
                                        <Heart className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        )}

                        {/* Product Attributes */}
                        {product.attributes && Object.keys(product.attributes).length > 0 && (
                            <>
                                <Separator />
                                <div>
                                    <h3 className="mb-2 font-semibold">Product Details</h3>
                                    <div className="space-y-1">
                                        {Object.entries(product.attributes).map(([key, value]) => (
                                            <div key={key} className="flex justify-between text-sm">
                                                <span className="text-muted-foreground capitalize">{key.replace('_', ' ')}:</span>
                                                <span>{String(value)}</span>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            </>
                        )}

                        {/* SKU */}
                        <div className="text-sm text-muted-foreground">SKU: {product.sku}</div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    );
};

export default ProductQuickView;
