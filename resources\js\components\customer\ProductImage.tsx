import { Package, ImageIcon } from 'lucide-react';
import React, { useState, useCallback } from 'react';
import { cn } from '@/lib/utils';

interface ProductImageProps {
    src?: string | null;
    alt: string;
    className?: string;
    fallbackClassName?: string;
    size?: 'sm' | 'md' | 'lg' | 'xl';
    showLoadingState?: boolean;
}

const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24', 
    lg: 'w-32 h-32',
    xl: 'w-48 h-48'
};

const iconSizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12', 
    xl: 'w-16 h-16'
};

// Helper function to detect if a string is an emoji
const isEmoji = (str: string): boolean => {
    const emojiRegex = /^[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]$/u;
    return emojiRegex.test(str.trim());
};

// Helper function to check if string looks like a valid URL
const isValidImageUrl = (str: string): boolean => {
    if (!str || str.trim() === '') return false;
    if (isEmoji(str)) return false;
    return str.startsWith('http') || str.startsWith('/') || str.startsWith('data:') || str.includes('.');
};

const ProductImage: React.FC<ProductImageProps> = ({
    src,
    alt,
    className,
    fallbackClassName,
    size = 'md',
    showLoadingState = true
}) => {
    const [imageState, setImageState] = useState<'loading' | 'loaded' | 'error'>('loading');
    const [imageSrc, setImageSrc] = useState<string | null>(src || null);

    const handleImageLoad = useCallback(() => {
        setImageState('loaded');
    }, []);

    const handleImageError = useCallback(() => {
        setImageState('error');
        setImageSrc(null);
    }, []);

    // Check if we should show fallback immediately
    const shouldShowFallback = !imageSrc ||
                              imageSrc.trim() === '' ||
                              !isValidImageUrl(imageSrc) ||
                              imageState === 'error';

    // If the src is an emoji, show it as fallback content
    const emojiContent = src && isEmoji(src) ? src : null;

    if (shouldShowFallback) {
        return (
            <div
                className={cn(
                    'flex items-center justify-center bg-muted rounded-lg',
                    emojiContent ? '' : 'border-2 border-dashed border-muted-foreground/20',
                    sizeClasses[size],
                    fallbackClassName,
                    className
                )}
                role="img"
                aria-label={alt}
            >
                {emojiContent ? (
                    <span className="text-6xl">{emojiContent}</span>
                ) : (
                    <div className="flex flex-col items-center justify-center text-muted-foreground">
                        <Package className={cn(iconSizeClasses[size], 'mb-1')} />
                        <span className="text-xs font-medium">No Image</span>
                    </div>
                )}
            </div>
        );
    }

    return (
        <div className={cn('relative overflow-hidden rounded-lg', sizeClasses[size], className)}>
            {/* Loading state */}
            {showLoadingState && imageState === 'loading' && (
                <div className="absolute inset-0 flex items-center justify-center bg-muted animate-pulse">
                    <ImageIcon className={cn(iconSizeClasses[size], 'text-muted-foreground')} />
                </div>
            )}
            
            {/* Actual image */}
            <img
                src={imageSrc}
                alt={alt}
                className={cn(
                    'w-full h-full object-cover transition-opacity duration-200',
                    imageState === 'loading' ? 'opacity-0' : 'opacity-100'
                )}
                onLoad={handleImageLoad}
                onError={handleImageError}
                loading="lazy"
            />
        </div>
    );
};

export default ProductImage;
