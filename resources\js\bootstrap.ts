import axios from 'axios';

// Set up axios defaults for Laravel
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// Get CSRF token from meta tag
const token = document.head.querySelector('meta[name="csrf-token"]') as HTMLMetaElement;

if (token) {
    axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
} else {
    console.error('CSRF token not found: https://laravel.com/docs/csrf#csrf-x-csrf-token');
}

// Set up axios to handle credentials for session-based authentication
axios.defaults.withCredentials = true;

// Create a configured axios instance for API calls
export const apiClient = axios.create({
    baseURL: '/api/v1',
    headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Accept': 'application/json',
        'Content-Type': 'application/json',
    },
    withCredentials: true,
});

// Add request interceptor to ensure <PERSON><PERSON><PERSON> token is always included
apiClient.interceptors.request.use((config) => {
    const token = document.head.querySelector('meta[name="csrf-token"]') as HTMLMetaElement;
    if (token) {
        config.headers['X-CSRF-TOKEN'] = token.content;
    }
    return config;
});

// Add CSRF token to the API client
if (token) {
    apiClient.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
}

export default axios;
