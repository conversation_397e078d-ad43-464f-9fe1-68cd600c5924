import { Button } from '@/components/ui/button';
import React from 'react';

const CTASection: React.FC = () => {
    return (
        <section className="bg-gradient-to-r from-green-400 to-emerald-500 py-16">
            <div className="container mx-auto px-4">
                <div className="grid items-center gap-8 md:grid-cols-2">
                    <div className="text-white">
                        <h2 className="font-display mb-4 text-3xl font-bold md:text-5xl">Get Yours Now!</h2>
                        <p className="mb-8 text-xl text-white/90">Don't miss out on our amazing deals and fresh products</p>
                        <Button size="lg" className="bg-white font-semibold text-green-600 hover:bg-gray-100">
                            Shop Now
                        </Button>
                    </div>
                    <div className="text-center">
                        <div className="animate-bounce-slow text-9xl">🎯</div>
                    </div>
                </div>
            </div>
        </section>
    );
};

export default CTASection;
