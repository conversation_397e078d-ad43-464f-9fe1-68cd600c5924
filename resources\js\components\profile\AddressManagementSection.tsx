import { Plus, MapPin, Edit, Trash2, <PERSON>, LoaderCircle } from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { apiClient } from '@/bootstrap';

import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import AddressForm from '@/components/profile/AddressForm';

interface Address {
    id: number;
    type: string;
    label: string | null;
    first_name: string;
    last_name: string;
    company: string | null;
    address_line_1: string;
    address_line_2: string | null;
    city: string;
    state_province: string;
    postal_code: string;
    country: string;
    phone: string | null;
    is_default: boolean;
    created_at: string;
    updated_at: string;
}

export default function AddressManagementSection() {
    const [addresses, setAddresses] = useState<Address[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [editingAddress, setEditingAddress] = useState<Address | null>(null);

    const fetchAddresses = async () => {
        try {
            setLoading(true);
            console.log('Fetching addresses...');
            const response = await apiClient.get('/addresses');
            console.log('Addresses response:', response);
            if (response.data.success) {
                setAddresses(response.data.data);
            } else {
                setError('Failed to load addresses');
            }
        } catch (err) {
            setError('Failed to load addresses');
            console.error('Error fetching addresses:', err);
            if (err && typeof err === 'object' && 'response' in err) {
                const error = err as { response?: { status?: number, data?: unknown } };
                console.error('Response status:', error.response?.status);
                console.error('Response data:', error.response?.data);
            }
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchAddresses();
    }, []);

    const handleAddressSubmit = async (addressData: Partial<Address>) => {
        try {
            if (editingAddress) {
                // Update existing address
                const response = await apiClient.put(`/addresses/${editingAddress.id}`, addressData);
                if (response.data.success) {
                    await fetchAddresses();
                    setIsDialogOpen(false);
                    setEditingAddress(null);
                }
            } else {
                // Create new address
                const response = await apiClient.post('/addresses', addressData);
                if (response.data.success) {
                    await fetchAddresses();
                    setIsDialogOpen(false);
                }
            }
        } catch (err) {
            console.error('Error saving address:', err);
            throw err; // Let the form handle the error
        }
    };

    const handleDeleteAddress = async (addressId: number) => {
        if (!confirm('Are you sure you want to delete this address?')) {
            return;
        }

        try {
            const response = await apiClient.delete(`/addresses/${addressId}`);
            if (response.data.success) {
                await fetchAddresses();
            }
        } catch (err) {
            console.error('Error deleting address:', err);
            alert('Failed to delete address');
        }
    };

    const handleSetDefault = async (addressId: number) => {
        try {
            const response = await apiClient.put(`/addresses/${addressId}/default`);
            if (response.data.success) {
                await fetchAddresses();
            }
        } catch (err) {
            console.error('Error setting default address:', err);
            alert('Failed to set default address');
        }
    };

    const handleEditAddress = (address: Address) => {
        setEditingAddress(address);
        setIsDialogOpen(true);
    };

    const handleAddNew = () => {
        setEditingAddress(null);
        setIsDialogOpen(true);
    };

    const formatAddress = (address: Address) => {
        const parts = [
            address.address_line_1,
            address.address_line_2,
            address.city,
            address.state_province,
            address.postal_code,
        ].filter(Boolean);
        return parts.join(', ');
    };

    const getTypeColor = (type: string) => {
        switch (type) {
            case 'shipping':
                return 'bg-blue-100 text-blue-800';
            case 'billing':
                return 'bg-green-100 text-green-800';
            case 'both':
                return 'bg-purple-100 text-purple-800';
            default:
                return 'bg-gray-100 text-gray-800';
        }
    };

    if (loading) {
        return (
            <Card>
                <CardContent className="p-6">
                    <div className="flex items-center justify-center">
                        <LoaderCircle className="w-6 h-6 animate-spin mr-2" />
                        Loading addresses...
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (error) {
        return (
            <Card>
                <CardContent className="p-6">
                    <div className="text-center text-red-600">
                        <p>{error}</p>
                        <Button onClick={fetchAddresses} className="mt-2">
                            Try Again
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <Card>
            <CardHeader className="flex flex-row items-center justify-between">
                <div>
                    <CardTitle>Address Management</CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">
                        Manage your shipping and billing addresses
                    </p>
                </div>
                <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                    <DialogTrigger asChild>
                        <Button onClick={handleAddNew}>
                            <Plus className="w-4 h-4 mr-2" />
                            Add Address
                        </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                        <DialogHeader>
                            <DialogTitle>
                                {editingAddress ? 'Edit Address' : 'Add New Address'}
                            </DialogTitle>
                        </DialogHeader>
                        <AddressForm
                            address={editingAddress}
                            onSubmit={handleAddressSubmit}
                            onCancel={() => {
                                setIsDialogOpen(false);
                                setEditingAddress(null);
                            }}
                        />
                    </DialogContent>
                </Dialog>
            </CardHeader>
            <CardContent>
                {addresses.length === 0 ? (
                    <div className="text-center py-8">
                        <MapPin className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                        <h3 className="text-lg font-medium mb-2">No addresses yet</h3>
                        <p className="text-muted-foreground mb-4">
                            Add your first address to make checkout faster
                        </p>
                        <Button onClick={handleAddNew}>
                            <Plus className="w-4 h-4 mr-2" />
                            Add Your First Address
                        </Button>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {addresses.map((address) => (
                            <div
                                key={address.id}
                                className="border rounded-lg p-4 relative"
                            >
                                {address.is_default && (
                                    <div className="absolute top-2 right-2">
                                        <Badge variant="secondary" className="text-xs">
                                            <Star className="w-3 h-3 mr-1 fill-current" />
                                            Default
                                        </Badge>
                                    </div>
                                )}

                                <div className="mb-3">
                                    <div className="flex items-center space-x-2 mb-2">
                                        <Badge className={getTypeColor(address.type)}>
                                            {address.type.charAt(0).toUpperCase() + address.type.slice(1)}
                                        </Badge>
                                        {address.label && (
                                            <Badge variant="outline">{address.label}</Badge>
                                        )}
                                    </div>
                                    <h4 className="font-medium">
                                        {address.first_name} {address.last_name}
                                    </h4>
                                    {address.company && (
                                        <p className="text-sm text-muted-foreground">{address.company}</p>
                                    )}
                                </div>

                                <div className="text-sm text-muted-foreground mb-4">
                                    <p>{formatAddress(address)}</p>
                                    {address.phone && <p>Phone: {address.phone}</p>}
                                </div>

                                <div className="flex justify-between items-center">
                                    <div className="flex space-x-2">
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleEditAddress(address)}
                                        >
                                            <Edit className="w-3 h-3 mr-1" />
                                            Edit
                                        </Button>
                                        <Button
                                            variant="outline"
                                            size="sm"
                                            onClick={() => handleDeleteAddress(address.id)}
                                        >
                                            <Trash2 className="w-3 h-3 mr-1" />
                                            Delete
                                        </Button>
                                    </div>
                                    {!address.is_default && (
                                        <Button
                                            variant="ghost"
                                            size="sm"
                                            onClick={() => handleSetDefault(address.id)}
                                        >
                                            Set as Default
                                        </Button>
                                    )}
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
