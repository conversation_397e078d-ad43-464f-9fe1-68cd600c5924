<?php

namespace Database\Seeders;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\User;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::where('email', 'not like', '%@freshcart.com')->take(10)->get();
        $products = Product::all();

        if ($users->isEmpty() || $products->isEmpty()) {
            $this->command->warn('No users or products found. Please run UserSeeder and ProductSeeder first.');
            return;
        }

        $orderStatuses = ['pending', 'processing', 'shipped', 'delivered', 'cancelled'];
        $paymentMethods = ['credit_card', 'paypal', 'stripe', 'bank_transfer'];
        $shippingMethods = ['standard', 'express', 'overnight', 'pickup'];

        $firstNames = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
        $lastNames = ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'];
        $cities = ['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix', 'Philadelphia'];
        $states = ['NY', 'CA', 'IL', 'TX', 'AZ', 'PA'];

        // Create 10 sample orders
        for ($i = 1; $i <= 10; $i++) {
            $user = $users->random();
            $status = $orderStatuses[array_rand($orderStatuses)];
            $paymentStatus = ($status === 'delivered') ? 'paid' : (($status === 'cancelled') ? 'failed' : 'pending');

            $orderDate = Carbon::now()->subDays(rand(1, 180));
            $shippedAt = null;
            $deliveredAt = null;

            if (in_array($status, ['shipped', 'delivered'])) {
                $shippedAt = $orderDate->copy()->addDays(rand(1, 3));
            }

            if ($status === 'delivered') {
                $deliveredAt = $shippedAt->copy()->addDays(rand(1, 7));
            }

            $subtotal = round(rand(5000, 100000) / 100, 2);
            $taxAmount = round($subtotal * 0.08, 2);
            $shippingAmount = [0, 9.99, 19.99, 29.99][array_rand([0, 9.99, 19.99, 29.99])];
            $discountAmount = [0, 0, 0, 10, 25, 50][array_rand([0, 0, 0, 10, 25, 50])];
            $totalAmount = round($subtotal + $taxAmount + $shippingAmount - $discountAmount, 2);

            $firstName = $firstNames[array_rand($firstNames)];
            $lastName = $lastNames[array_rand($lastNames)];
            $city = $cities[array_rand($cities)];
            $state = $states[array_rand($states)];

            $order = Order::create([
                'order_number' => 'LS-' . str_pad($i, 6, '0', STR_PAD_LEFT),
                'user_id' => $user->id,
                'status' => $status,
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'shipping_amount' => $shippingAmount,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'currency' => 'USD',
                'payment_status' => $paymentStatus,
                'payment_method' => $paymentMethods[array_rand($paymentMethods)],
                'payment_reference' => 'ref_' . uniqid(),

                // Billing information
                'billing_first_name' => $firstName,
                'billing_last_name' => $lastName,
                'billing_email' => $user->email,
                'billing_phone' => '555-' . rand(100, 999) . '-' . rand(1000, 9999),
                'billing_company' => rand(0, 1) ? 'Company ' . rand(1, 100) : null,
                'billing_address_line_1' => rand(100, 9999) . ' Main St',
                'billing_address_line_2' => rand(0, 1) ? 'Apt ' . rand(1, 100) : null,
                'billing_city' => $city,
                'billing_state' => $state,
                'billing_postal_code' => str_pad(rand(10000, 99999), 5, '0', STR_PAD_LEFT),
                'billing_country' => 'US',

                // Shipping information
                'shipping_first_name' => rand(0, 1) ? $firstName : null,
                'shipping_last_name' => rand(0, 1) ? $lastName : null,
                'shipping_company' => null,
                'shipping_address_line_1' => rand(0, 1) ? rand(100, 9999) . ' Oak Ave' : null,
                'shipping_address_line_2' => null,
                'shipping_city' => rand(0, 1) ? $city : null,
                'shipping_state' => rand(0, 1) ? $state : null,
                'shipping_postal_code' => rand(0, 1) ? str_pad(rand(10000, 99999), 5, '0', STR_PAD_LEFT) : null,
                'shipping_country' => rand(0, 1) ? 'US' : null,
                'shipping_method' => $shippingMethods[array_rand($shippingMethods)],
                'tracking_number' => in_array($status, ['shipped', 'delivered']) ? 'TRK' . rand(*********, *********) : null,

                'notes' => rand(0, 1) ? 'Order note ' . $i : null,
                'shipped_at' => $shippedAt,
                'delivered_at' => $deliveredAt,
                'created_at' => $orderDate,
                'updated_at' => $orderDate,
            ]);

            // Create 1-3 order items for each order
            $itemCount = rand(1, 3);
            $orderSubtotal = 0;

            for ($j = 0; $j < $itemCount; $j++) {
                $product = $products->random();
                $quantity = rand(1, 3);
                $unitPrice = $product->price;
                $totalPrice = $quantity * $unitPrice;
                $orderSubtotal += $totalPrice;

                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_sku' => $product->sku,
                    'quantity' => $quantity,
                    'unit_price' => $unitPrice,
                    'total_price' => $totalPrice,
                    'product_options' => null,
                ]);
            }

            // Update order subtotal to match actual items
            $order->update(['subtotal' => $orderSubtotal]);
        }

        $this->command->info('Created 10 orders with order items across different statuses.');
    }
}
