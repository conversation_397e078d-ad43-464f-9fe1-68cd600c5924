<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_payment_methods', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            $table->string('type'); // credit_card, gcash, paymaya, bpi_online, bdo_online, paypal
            $table->string('label')->nullable(); // Personal Card, Business Account, etc.
            
            // Credit/Debit Card fields
            $table->string('card_last_four')->nullable();
            $table->string('card_brand')->nullable(); // visa, mastercard, amex
            $table->string('card_holder_name')->nullable();
            $table->string('expiry_month')->nullable();
            $table->string('expiry_year')->nullable();
            
            // Mobile payment fields (GCash, PayMaya)
            $table->string('mobile_number')->nullable();
            $table->string('account_name')->nullable();
            
            // Bank account fields
            $table->string('bank_name')->nullable();
            $table->string('account_number_last_four')->nullable();
            $table->string('account_holder_name')->nullable();
            
            // PayPal fields
            $table->string('paypal_email')->nullable();
            
            // Security and metadata
            $table->json('encrypted_data')->nullable(); // Store sensitive data encrypted
            $table->string('provider_token')->nullable(); // Token from payment provider
            $table->boolean('is_default')->default(false);
            $table->boolean('is_verified')->default(false);
            $table->timestamp('verified_at')->nullable();
            $table->timestamps();

            // Add indexes for better performance
            $table->index(['user_id']);
            $table->index(['user_id', 'type']);
            $table->index(['user_id', 'is_default']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_payment_methods');
    }
};
