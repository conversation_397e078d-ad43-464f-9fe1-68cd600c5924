<?php

namespace Database\Factories;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->words(3, true);
        
        return [
            'name' => ucwords($name),
            'slug' => str($name)->slug(),
            'description' => $this->faker->paragraphs(3, true),
            'short_description' => $this->faker->sentence(),
            'sku' => strtoupper($this->faker->bothify('??###')),
            'price' => $this->faker->randomFloat(2, 10, 1000),
            'compare_price' => $this->faker->optional(0.3)->randomFloat(2, 15, 1200),
            'cost_price' => $this->faker->randomFloat(2, 5, 500),
            'stock_quantity' => $this->faker->numberBetween(0, 100),
            'min_stock_level' => $this->faker->numberBetween(1, 10),
            'track_inventory' => $this->faker->boolean(80),
            'is_active' => $this->faker->boolean(90),
            'is_featured' => $this->faker->boolean(20),
            'is_digital' => $this->faker->boolean(10),
            'weight' => $this->faker->optional(0.7)->randomFloat(2, 0.1, 50),
            'dimensions' => $this->faker->optional(0.5)->regexify('[0-9]{1,2}x[0-9]{1,2}x[0-9]{1,2}'),
            'meta_title' => $this->faker->optional(0.6)->sentence(),
            'meta_description' => $this->faker->optional(0.6)->sentence(),
            'images' => ['🛒'], // Default emoji image
            'attributes' => [],
            'category_id' => Category::factory(),
        ];
    }

    /**
     * Indicate that the product is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the product is featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_featured' => true,
        ]);
    }

    /**
     * Indicate that the product is out of stock.
     */
    public function outOfStock(): static
    {
        return $this->state(fn (array $attributes) => [
            'stock_quantity' => 0,
            'track_inventory' => true,
        ]);
    }

    /**
     * Indicate that the product has a specific price.
     */
    public function withPrice(float $price): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => $price,
        ]);
    }

    /**
     * Indicate that the product has a specific stock quantity.
     */
    public function withStock(int $quantity): static
    {
        return $this->state(fn (array $attributes) => [
            'stock_quantity' => $quantity,
            'track_inventory' => true,
        ]);
    }
}
