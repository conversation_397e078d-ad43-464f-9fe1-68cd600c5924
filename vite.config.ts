import tailwindcss from '@tailwindcss/vite';
import react from '@vitejs/plugin-react';
import laravel from 'laravel-vite-plugin';
import { resolve } from 'node:path';
import { defineConfig } from 'vite';

// Use HTTP for Vite dev server to avoid SSL certificate issues
// Laravel Herd will handle HTTPS for the main application

export default defineConfig({
    plugins: [
        laravel({
            input: ['resources/css/app.css', 'resources/js/app.tsx'],
            ssr: 'resources/js/ssr.tsx',
            refresh: true,
            buildDirectory: 'build',
        }),
        react(),
        tailwindcss(),
    ],
    esbuild: {
        jsx: 'automatic',
    },
    resolve: {
        alias: {
            'ziggy-js': resolve(__dirname, 'vendor/tightenco/ziggy'),
        },
    },
    build: {
        // Ensure manifest is generated in the root of build directory (not .vite subdirectory)
        manifest: 'manifest.json',
        // Output directory for built assets
        outDir: 'public/build',
        // Ensure assets are properly chunked
        rollupOptions: {
            output: {
                manualChunks: undefined,
            },
        },
        // Ensure proper asset handling in Docker
        assetsDir: 'assets',
        // Generate source maps for debugging if needed
        sourcemap: process.env.APP_ENV === 'local',
    },
    server: {
        // For Docker development and Herd compatibility
        host: '0.0.0.0',
        port: 5173,
        // Use HTTP for Vite dev server
        https: false,
        // CORS configuration to allow requests from Laravel application
        cors: {
            origin: [
                'https://luckystar.local',
                'http://luckystar.local',
                'https://lucky-star.test',
                'http://lucky-star.test',
                'https://localhost',
                'http://localhost'
            ],
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin'],
        },
        hmr: {
            host: 'localhost',
            port: 5173,
            // Use WebSocket for HMR
            protocol: 'ws',
        },
    },
});
