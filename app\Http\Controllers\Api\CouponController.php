<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Coupon;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;

class CouponController extends Controller
{
    /**
     * Display a listing of coupons (admin only).
     */
    public function index(Request $request): JsonResponse
    {
        $query = Coupon::orderBy('created_at', 'desc');

        // Filter by status
        if ($request->has('is_active')) {
            $query->where('is_active', $request->get('is_active') === 'true');
        }

        // Filter by type
        if ($request->has('type')) {
            $query->where('type', $request->get('type'));
        }

        // Search by code or name
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('code', 'ILIKE', "%{$search}%")
                  ->orWhere('name', 'ILIKE', "%{$search}%");
            });
        }

        $perPage = min($request->get('per_page', 20), 100);
        $coupons = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $coupons->items(),
            'meta' => [
                'current_page' => $coupons->currentPage(),
                'last_page' => $coupons->lastPage(),
                'per_page' => $coupons->perPage(),
                'total' => $coupons->total(),
            ]
        ]);
    }

    /**
     * Store a newly created coupon.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'code' => 'required|string|max:50|unique:coupons',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'type' => ['required', Rule::in([Coupon::TYPE_PERCENTAGE, Coupon::TYPE_FIXED_AMOUNT, Coupon::TYPE_FREE_SHIPPING])],
            'value' => 'required|numeric|min:0',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_user' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'starts_at' => 'nullable|date|after_or_equal:today',
            'expires_at' => 'nullable|date|after:starts_at',
        ]);

        // Validate percentage value
        if ($validated['type'] === Coupon::TYPE_PERCENTAGE && $validated['value'] > 100) {
            return response()->json([
                'success' => false,
                'message' => 'Percentage discount cannot exceed 100%',
                'errors' => ['value' => ['Percentage value must be between 0 and 100']]
            ], 422);
        }

        $coupon = Coupon::create($validated);

        return response()->json([
            'success' => true,
            'message' => 'Coupon created successfully',
            'data' => $coupon
        ], 201);
    }

    /**
     * Display the specified coupon.
     */
    public function show(Coupon $coupon): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $coupon
        ]);
    }

    /**
     * Update the specified coupon.
     */
    public function update(Request $request, Coupon $coupon): JsonResponse
    {
        $validated = $request->validate([
            'code' => ['sometimes', 'required', 'string', 'max:50', Rule::unique('coupons')->ignore($coupon->id)],
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'type' => ['sometimes', 'required', Rule::in([Coupon::TYPE_PERCENTAGE, Coupon::TYPE_FIXED_AMOUNT, Coupon::TYPE_FREE_SHIPPING])],
            'value' => 'sometimes|required|numeric|min:0',
            'minimum_amount' => 'nullable|numeric|min:0',
            'maximum_discount' => 'nullable|numeric|min:0',
            'usage_limit' => 'nullable|integer|min:1',
            'usage_limit_per_user' => 'nullable|integer|min:1',
            'is_active' => 'boolean',
            'starts_at' => 'nullable|date',
            'expires_at' => 'nullable|date|after:starts_at',
        ]);

        // Validate percentage value
        if (isset($validated['type']) && $validated['type'] === Coupon::TYPE_PERCENTAGE) {
            $value = $validated['value'] ?? $coupon->value;
            if ($value > 100) {
                return response()->json([
                    'success' => false,
                    'message' => 'Percentage discount cannot exceed 100%',
                    'errors' => ['value' => ['Percentage value must be between 0 and 100']]
                ], 422);
            }
        }

        $coupon->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Coupon updated successfully',
            'data' => $coupon
        ]);
    }

    /**
     * Remove the specified coupon.
     */
    public function destroy(Coupon $coupon): JsonResponse
    {
        $coupon->delete();

        return response()->json([
            'success' => true,
            'message' => 'Coupon deleted successfully'
        ]);
    }

    /**
     * Validate a coupon code.
     */
    public function validate(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'code' => 'required|string',
            'subtotal' => 'required|numeric|min:0',
        ]);

        $coupon = Coupon::where('code', $validated['code'])->first();

        if (!$coupon) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid coupon code',
                'errors' => ['code' => ['Coupon code not found']]
            ], 422);
        }

        if (!$coupon->isValid()) {
            $message = 'Coupon is not valid';
            
            if (!$coupon->is_active) {
                $message = 'Coupon is inactive';
            } elseif ($coupon->starts_at && $coupon->starts_at->isFuture()) {
                $message = 'Coupon is not yet active';
            } elseif ($coupon->expires_at && $coupon->expires_at->isPast()) {
                $message = 'Coupon has expired';
            } elseif ($coupon->usage_limit && $coupon->used_count >= $coupon->usage_limit) {
                $message = 'Coupon usage limit reached';
            }

            return response()->json([
                'success' => false,
                'message' => $message,
                'errors' => ['code' => [$message]]
            ], 422);
        }

        if (!$coupon->canBeUsedForAmount($validated['subtotal'])) {
            return response()->json([
                'success' => false,
                'message' => "Minimum order amount of {$coupon->minimum_amount} required",
                'errors' => ['subtotal' => ['Order amount does not meet minimum requirement']]
            ], 422);
        }

        $discountAmount = $coupon->calculateDiscount($validated['subtotal']);

        return response()->json([
            'success' => true,
            'message' => 'Coupon is valid',
            'data' => [
                'coupon' => $coupon,
                'discount_amount' => $discountAmount,
                'final_amount' => $validated['subtotal'] - $discountAmount,
            ]
        ]);
    }
}
