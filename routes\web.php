<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::get('/products', function () {
    return Inertia::render('products');
})->name('products');

// Shopping cart and checkout routes
Route::get('/cart', function () {
    return Inertia::render('cart');
})->name('cart');

Route::get('/checkout', function () {
    return Inertia::render('checkout');
})->name('checkout');

// Order routes
Route::get('/orders/confirmation/{order}', function ($orderId) {
    // In a real app, this would fetch the order from the database
    $order = \App\Models\Order::with('orderItems.product')->findOrFail($orderId);
    return Inertia::render('order-confirmation', ['order' => $order]);
})->name('order.confirmation');

Route::get('/orders/track/{token?}', function ($token = null) {
    // In a real app, this would fetch the order by tracking token
    $order = null;
    if ($token) {
        $order = \App\Models\Order::with('orderItems.product')
            ->where('order_number', $token)
            ->orWhere('tracking_number', $token)
            ->first();
    }
    return Inertia::render('order-tracking', [
        'order' => $order,
        'tracking_token' => $token
    ]);
})->name('order.tracking');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    // Account order management
    Route::get('/account/orders', function () {
        $orders = \App\Models\Order::with('orderItems.product')
            ->where('user_id', auth()->id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return Inertia::render('account/orders', ['orders' => $orders]);
    })->name('account.orders');

    Route::get('/orders/{order}', function ($orderId) {
        $order = \App\Models\Order::with('orderItems.product')
            ->where('id', $orderId)
            ->where('user_id', auth()->id())
            ->firstOrFail();

        return Inertia::render('order-confirmation', ['order' => $order]);
    })->name('order.show');

    // User profile
    Route::get('/profile', function () {
        return Inertia::render('profile');
    })->name('profile');
});

// Test route to auto-login for debugging (remove in production)
Route::get('/test-login', function () {
    $user = \App\Models\User::where('email', '<EMAIL>')->first();
    if ($user) {
        auth()->login($user);
        return redirect('/profile');
    }
    return 'Test user not found';
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
