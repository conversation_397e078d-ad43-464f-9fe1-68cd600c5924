#!/usr/bin/env bash

echo "🚀 Starting Laravel 12 + React deployment..."

# Install PHP dependencies
echo "📦 Installing PHP dependencies..."
composer install --no-dev --working-dir=/var/www/html

# Install Node.js dependencies and build assets
echo "📦 Installing Node.js dependencies..."
cd /var/www/html
npm ci --only=production

echo "🏗️ Building React assets with Vite..."
npm run build

# Verify that the build directory exists
if [ ! -d "/var/www/html/public/build" ]; then
    echo "❌ Error: Build directory not found after npm run build"
    exit 1
fi

# Verify that the manifest file exists (should be in root of build directory)
if [ ! -f "/var/www/html/public/build/manifest.json" ]; then
    echo "❌ Error: Vite manifest.json not found after build"
    echo "Expected location: /var/www/html/public/build/manifest.json"

    # Debug: Check what files exist in build directory
    echo "🔍 Debugging: Contents of /public/build directory:"
    ls -la /var/www/html/public/build/ || echo "Build directory not found"

    # Check if manifest exists in .vite subdirectory (old location)
    if [ -f "/var/www/html/public/build/.vite/manifest.json" ]; then
        echo "⚠️  Found manifest in .vite subdirectory - this indicates a configuration issue"
        echo "Moving manifest to correct location..."
        mv /var/www/html/public/build/.vite/manifest.json /var/www/html/public/build/manifest.json
        echo "✅ Manifest moved to correct location"
    else
        exit 1
    fi
fi

echo "✅ React assets built successfully"

# Generate application key if not exists
echo "🔑 Checking application key..."
if [ -z "$APP_KEY" ] || [ "$APP_KEY" = "" ]; then
    echo "Generating application key..."
    php artisan key:generate --force
fi

echo "⚡ Caching configuration..."
php artisan config:cache

echo "⚡ Caching events..."
php artisan event:cache

echo "⚡ Caching routes..."
php artisan route:cache

echo "⚡ Caching views..."
php artisan view:cache

echo "🗄️ Running database migrations..."
# Check database connection first
echo "🔍 Testing database connection..."
php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database connection successful';" || {
    echo "❌ Database connection failed. Checking configuration..."
    echo "DB_HOST: $DB_HOST"
    echo "DB_DATABASE: $DB_DATABASE"
    echo "DB_USERNAME: $DB_USERNAME"
    # Continue deployment even if database fails for now
}

# Run migrations with error handling
php artisan migrate --force || {
    echo "⚠️ Migration failed, but continuing deployment..."
}

echo "🚀 Optimizing application..."
php artisan optimize

echo "🔧 Setting proper permissions..."
# Ensure storage and cache directories are writable
chmod -R 775 /var/www/html/storage
chmod -R 775 /var/www/html/bootstrap/cache

# Ensure nginx can read the application files
chown -R www-data:www-data /var/www/html

echo "🌐 Configuring nginx for Laravel..."
/usr/local/bin/configure-nginx.sh || {
    echo "❌ nginx configuration failed!"
    exit 1
}

echo "🔍 Debugging: Checking route cache..."
php artisan route:list --path=api/v1 | head -10

echo "🔍 Debugging: Checking if API routes are accessible..."
echo "Routes should include: GET api/v1/products"

echo "✅ Laravel 12 + React deployment completed successfully!"
