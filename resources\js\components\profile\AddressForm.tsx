import { Save, X, LoaderCircle } from 'lucide-react';
import React, { useState, useEffect } from 'react';

import InputError from '@/components/input-error';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';

interface Address {
    id?: number;
    type: string;
    label: string | null;
    first_name: string;
    last_name: string;
    company: string | null;
    address_line_1: string;
    address_line_2: string | null;
    city: string;
    state_province: string;
    postal_code: string;
    country: string;
    phone: string | null;
    is_default: boolean;
}

interface AddressFormProps {
    address?: Address | null;
    onSubmit: (data: Partial<Address>) => Promise<void>;
    onCancel: () => void;
}

export default function AddressForm({ address, onSubmit, onCancel }: AddressFormProps) {
    const [formData, setFormData] = useState<Partial<Address>>({
        type: 'shipping',
        label: '',
        first_name: '',
        last_name: '',
        company: '',
        address_line_1: '',
        address_line_2: '',
        city: '',
        state_province: '',
        postal_code: '',
        country: 'PH',
        phone: '',
        is_default: false,
    });

    const [errors, setErrors] = useState<Record<string, string>>({});
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (address) {
            setFormData({
                type: address.type,
                label: address.label || '',
                first_name: address.first_name,
                last_name: address.last_name,
                company: address.company || '',
                address_line_1: address.address_line_1,
                address_line_2: address.address_line_2 || '',
                city: address.city,
                state_province: address.state_province,
                postal_code: address.postal_code,
                country: address.country,
                phone: address.phone || '',
                is_default: address.is_default,
            });
        }
    }, [address]);

    const validateForm = () => {
        const newErrors: Record<string, string> = {};

        if (!formData.first_name?.trim()) {
            newErrors.first_name = 'First name is required';
        }
        if (!formData.last_name?.trim()) {
            newErrors.last_name = 'Last name is required';
        }
        if (!formData.address_line_1?.trim()) {
            newErrors.address_line_1 = 'Address line 1 is required';
        }
        if (!formData.city?.trim()) {
            newErrors.city = 'City is required';
        }
        if (!formData.state_province?.trim()) {
            newErrors.state_province = 'State/Province is required';
        }
        if (!formData.postal_code?.trim()) {
            newErrors.postal_code = 'Postal code is required';
        }

        // Philippine phone number validation (optional)
        if (formData.phone && !/^(\+63|0)[0-9]{10}$/.test(formData.phone.replace(/\s/g, ''))) {
            newErrors.phone = 'Please enter a valid Philippine phone number';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        if (!validateForm()) {
            return;
        }

        try {
            setLoading(true);
            await onSubmit(formData);
        } catch (err: unknown) {
            // Handle API validation errors
            if (err && typeof err === 'object' && 'response' in err) {
                const error = err as { response?: { data?: { errors?: Record<string, string> } } };
                if (error.response?.data?.errors) {
                    setErrors(error.response.data.errors);
                } else {
                    setErrors({ general: 'Failed to save address. Please try again.' });
                }
            } else {
                setErrors({ general: 'Failed to save address. Please try again.' });
            }
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (field: keyof Address, value: string | boolean) => {
        setFormData(prev => ({ ...prev, [field]: value }));
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
        }
    };

    const philippineProvinces = [
        'Metro Manila', 'Cebu', 'Davao', 'Laguna', 'Cavite', 'Bulacan', 'Rizal',
        'Batangas', 'Pampanga', 'Pangasinan', 'Iloilo', 'Negros Occidental',
        'Albay', 'Camarines Sur', 'Leyte', 'Bohol', 'Misamis Oriental',
        'Cagayan', 'Nueva Ecija', 'Isabela', 'Tarlac', 'Zambales',
        'La Union', 'Ilocos Norte', 'Ilocos Sur', 'Benguet', 'Abra',
        'Mountain Province', 'Ifugao', 'Kalinga', 'Apayao', 'Bataan',
        'Aurora', 'Nueva Vizcaya', 'Quirino', 'Palawan', 'Occidental Mindoro',
        'Oriental Mindoro', 'Marinduque', 'Romblon', 'Masbate', 'Catanduanes',
        'Sorsogon', 'Aklan', 'Antique', 'Capiz', 'Guimaras', 'Negros Oriental',
        'Samar', 'Eastern Samar', 'Northern Samar', 'Biliran', 'Southern Leyte',
        'Camiguin', 'Misamis Occidental', 'Bukidnon', 'Lanao del Norte',
        'Lanao del Sur', 'North Cotabato', 'South Cotabato', 'Sultan Kudarat',
        'Sarangani', 'General Santos', 'Davao del Norte', 'Davao del Sur',
        'Davao Oriental', 'Compostela Valley', 'Agusan del Norte', 'Agusan del Sur',
        'Surigao del Norte', 'Surigao del Sur', 'Dinagat Islands', 'Basilan',
        'Sulu', 'Tawi-Tawi', 'Zamboanga del Norte', 'Zamboanga del Sur',
        'Zamboanga Sibugay'
    ];

    return (
        <form onSubmit={handleSubmit} className="space-y-6">
            {errors.general && (
                <div className="bg-red-50 border border-red-200 rounded-md p-3">
                    <p className="text-sm text-red-600">{errors.general}</p>
                </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="type">Address Type</Label>
                    <Select
                        value={formData.type}
                        onValueChange={(value) => handleInputChange('type', value)}
                    >
                        <SelectTrigger>
                            <SelectValue placeholder="Select address type" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="shipping">Shipping</SelectItem>
                            <SelectItem value="billing">Billing</SelectItem>
                            <SelectItem value="both">Both</SelectItem>
                        </SelectContent>
                    </Select>
                    <InputError message={errors.type} />
                </div>

                <div className="space-y-2">
                    <Label htmlFor="label">Label (Optional)</Label>
                    <Input
                        id="label"
                        type="text"
                        value={formData.label || ''}
                        onChange={(e) => handleInputChange('label', e.target.value)}
                        placeholder="e.g., Home, Work, Office"
                        disabled={loading}
                    />
                    <InputError message={errors.label} />
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="first_name">First Name</Label>
                    <Input
                        id="first_name"
                        type="text"
                        value={formData.first_name || ''}
                        onChange={(e) => handleInputChange('first_name', e.target.value)}
                        disabled={loading}
                        required
                    />
                    <InputError message={errors.first_name} />
                </div>

                <div className="space-y-2">
                    <Label htmlFor="last_name">Last Name</Label>
                    <Input
                        id="last_name"
                        type="text"
                        value={formData.last_name || ''}
                        onChange={(e) => handleInputChange('last_name', e.target.value)}
                        disabled={loading}
                        required
                    />
                    <InputError message={errors.last_name} />
                </div>
            </div>

            <div className="space-y-2">
                <Label htmlFor="company">Company (Optional)</Label>
                <Input
                    id="company"
                    type="text"
                    value={formData.company || ''}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                    disabled={loading}
                    placeholder="Company name"
                />
                <InputError message={errors.company} />
            </div>

            <div className="space-y-4">
                <div className="space-y-2">
                    <Label htmlFor="address_line_1">Address Line 1</Label>
                    <Input
                        id="address_line_1"
                        type="text"
                        value={formData.address_line_1 || ''}
                        onChange={(e) => handleInputChange('address_line_1', e.target.value)}
                        disabled={loading}
                        placeholder="Street address, P.O. box, company name"
                        required
                    />
                    <InputError message={errors.address_line_1} />
                </div>

                <div className="space-y-2">
                    <Label htmlFor="address_line_2">Address Line 2 (Optional)</Label>
                    <Input
                        id="address_line_2"
                        type="text"
                        value={formData.address_line_2 || ''}
                        onChange={(e) => handleInputChange('address_line_2', e.target.value)}
                        disabled={loading}
                        placeholder="Apartment, suite, unit, building, floor, etc."
                    />
                    <InputError message={errors.address_line_2} />
                </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="city">City</Label>
                    <Input
                        id="city"
                        type="text"
                        value={formData.city || ''}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                        disabled={loading}
                        required
                    />
                    <InputError message={errors.city} />
                </div>

                <div className="space-y-2">
                    <Label htmlFor="state_province">Province</Label>
                    <Select
                        value={formData.state_province}
                        onValueChange={(value) => handleInputChange('state_province', value)}
                    >
                        <SelectTrigger>
                            <SelectValue placeholder="Select province" />
                        </SelectTrigger>
                        <SelectContent>
                            {philippineProvinces.map((province) => (
                                <SelectItem key={province} value={province}>
                                    {province}
                                </SelectItem>
                            ))}
                        </SelectContent>
                    </Select>
                    <InputError message={errors.state_province} />
                </div>

                <div className="space-y-2">
                    <Label htmlFor="postal_code">Postal Code</Label>
                    <Input
                        id="postal_code"
                        type="text"
                        value={formData.postal_code || ''}
                        onChange={(e) => handleInputChange('postal_code', e.target.value)}
                        disabled={loading}
                        placeholder="1234"
                        required
                    />
                    <InputError message={errors.postal_code} />
                </div>
            </div>

            <div className="space-y-2">
                <Label htmlFor="phone">Phone Number (Optional)</Label>
                <Input
                    id="phone"
                    type="tel"
                    value={formData.phone || ''}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    disabled={loading}
                    placeholder="09XX XXX XXXX"
                />
                <InputError message={errors.phone} />
                <p className="text-xs text-muted-foreground">
                    Philippine mobile number format
                </p>
            </div>

            <div className="flex items-center space-x-2">
                <Checkbox
                    id="is_default"
                    checked={formData.is_default || false}
                    onCheckedChange={(checked) => handleInputChange('is_default', checked)}
                    disabled={loading}
                />
                <Label htmlFor="is_default" className="text-sm">
                    Set as default address
                </Label>
            </div>

            <Separator />

            <div className="flex justify-end space-x-3">
                <Button
                    type="button"
                    variant="outline"
                    onClick={onCancel}
                    disabled={loading}
                >
                    <X className="w-4 h-4 mr-2" />
                    Cancel
                </Button>
                <Button type="submit" disabled={loading}>
                    {loading ? (
                        <LoaderCircle className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                        <Save className="w-4 h-4 mr-2" />
                    )}
                    {address ? 'Update Address' : 'Save Address'}
                </Button>
            </div>
        </form>
    );
}
