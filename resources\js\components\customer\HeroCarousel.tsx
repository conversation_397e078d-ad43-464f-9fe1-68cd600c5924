import { Button } from '@/components/ui/button';
import React, { useEffect, useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from './Icons';

interface HeroSlide {
    id: number;
    title: string;
    subtitle: string;
    description: string;
    buttonText: string;
    image: string;
    bgColor: string;
}

const HeroCarousel: React.FC = () => {
    const [currentSlide, setCurrentSlide] = useState(0);

    const heroSlides: HeroSlide[] = [
        {
            id: 1,
            title: 'Fresh & Organic Groceries',
            subtitle: 'Farm to your doorstep',
            description: 'Get the freshest organic produce delivered to your home',
            buttonText: 'Shop Now',
            image: '🥬',
            bgColor: 'from-green-400 to-emerald-500',
        },
        {
            id: 2,
            title: 'Special Weekend Offers',
            subtitle: 'Up to 50% OFF',
            description: 'Amazing deals on your favorite products this weekend',
            buttonText: 'View Offers',
            image: '🎉',
            bgColor: 'from-orange-400 to-red-500',
        },
        {
            id: 3,
            title: 'Premium Quality Products',
            subtitle: 'Trusted by thousands',
            description: 'Experience the best quality products at unbeatable prices',
            buttonText: 'Explore',
            image: '⭐',
            bgColor: 'from-purple-400 to-pink-500',
        },
    ];

    // Auto-advance carousel
    useEffect(() => {
        const timer = setInterval(() => {
            setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
        }, 5000);
        return () => clearInterval(timer);
    }, [heroSlides.length]);

    const nextSlide = () => {
        setCurrentSlide((prev) => (prev + 1) % heroSlides.length);
    };

    const prevSlide = () => {
        setCurrentSlide((prev) => (prev - 1 + heroSlides.length) % heroSlides.length);
    };

    return (
        <section className="relative overflow-hidden">
            <div className="relative h-96 md:h-[500px]">
                {heroSlides.map((slide, index) => (
                    <div
                        key={slide.id}
                        className={`absolute inset-0 transform transition-all duration-700 ${
                            index === currentSlide
                                ? 'translate-x-0 opacity-100'
                                : index < currentSlide
                                  ? '-translate-x-full opacity-0'
                                  : 'translate-x-full opacity-0'
                        }`}
                    >
                        <div className={`h-full bg-gradient-to-r ${slide.bgColor} flex items-center`}>
                            <div className="container mx-auto px-4">
                                <div className="grid items-center gap-8 md:grid-cols-2">
                                    <div className="animate-slide-up text-white">
                                        <h2 className="font-display mb-4 text-4xl font-bold md:text-6xl">{slide.title}</h2>
                                        <p className="mb-4 text-xl font-semibold text-white/90 md:text-2xl">{slide.subtitle}</p>
                                        <p className="mb-8 max-w-md text-lg text-white/80">{slide.description}</p>
                                        <div className="flex flex-col gap-4 sm:flex-row">
                                            <Button size="lg" className="bg-white font-semibold text-green-600 hover:bg-gray-100">
                                                {slide.buttonText}
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="lg"
                                                className="border-white bg-transparent text-white hover:bg-white hover:text-green-600"
                                            >
                                                Learn More
                                            </Button>
                                        </div>
                                    </div>
                                    <div className="text-center">
                                        <div className="animate-bounce-slow text-8xl md:text-9xl">{slide.image}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                ))}
            </div>

            {/* Carousel controls */}
            <Button
                variant="ghost"
                size="icon"
                onClick={prevSlide}
                className="absolute top-1/2 left-4 -translate-y-1/2 transform rounded-full bg-white/20 p-2 text-white transition-all hover:bg-white/30"
            >
                <ChevronLeftIcon />
            </Button>
            <Button
                variant="ghost"
                size="icon"
                onClick={nextSlide}
                className="absolute top-1/2 right-4 -translate-y-1/2 transform rounded-full bg-white/20 p-2 text-white transition-all hover:bg-white/30"
            >
                <ChevronRightIcon />
            </Button>

            {/* Carousel indicators */}
            <div className="absolute bottom-4 left-1/2 flex -translate-x-1/2 transform space-x-2">
                {heroSlides.map((_, index) => (
                    <Button
                        key={index}
                        variant="ghost"
                        size="sm"
                        onClick={() => setCurrentSlide(index)}
                        className={`h-3 w-3 rounded-full p-0 transition-all ${index === currentSlide ? 'bg-white' : 'bg-white/50'}`}
                    />
                ))}
            </div>
        </section>
    );
};

export default HeroCarousel;
