import { Head } from '@inertiajs/react';

// Import customer components
import BestSellers from '@/components/customer/BestSellers';
import BrandSlider from '@/components/customer/BrandSlider';
import CategoryGrid from '@/components/customer/CategoryGrid';
import CTASection from '@/components/customer/CTASection';
import Footer from '@/components/customer/Footer';
import Header from '@/components/customer/Header';
import HeroCarousel from '@/components/customer/HeroCarousel';
import SpecialOffer from '@/components/customer/SpecialOffer';
import { CartProviderWrapper } from '@/components/CartProviderWrapper';

export default function Welcome() {
    // Note: auth is available via usePage<SharedData>().props if needed for user-specific content

    return (
        <CartProviderWrapper>
            <Head title="Lucky Star Grocery - Fresh & Quality Products">
                <meta
                    name="description"
                    content="Shop fresh groceries, organic produce, and quality products at Lucky Star Grocery. Fast delivery, great prices, and trusted brands."
                />
                <link rel="preconnect" href="https://fonts.bunny.net" />
                <link href="https://fonts.bunny.net/css?family=instrument-sans:400,500,600" rel="stylesheet" />
            </Head>

            <div className="min-h-screen bg-background">
                {/* Header with navigation */}
                <Header />

                {/* Hero Carousel Section */}
                <HeroCarousel />

                {/* Category Grid Section */}
                <CategoryGrid />

                {/* Best Sellers Section */}
                <BestSellers />

                {/* Special Offer Section */}
                <SpecialOffer />

                {/* Brand Slider Section */}
                <BrandSlider />

                {/* Call to Action Section */}
                <CTASection />

                {/* Footer */}
                <Footer />
            </div>
        </CartProviderWrapper>
    );
}
