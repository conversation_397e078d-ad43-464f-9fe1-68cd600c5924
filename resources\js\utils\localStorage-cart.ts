/**
 * Utilities for managing guest cart data in localStorage
 */

export interface LocalStorageCartItem {
    id: string; // Temporary ID for localStorage items (product_id + timestamp)
    product_id: number;
    quantity: number;
    product_options?: Record<string, unknown>;
    added_at: string; // ISO timestamp
}

export interface LocalStorageCartData {
    items: LocalStorageCartItem[];
    total_items: number;
    subtotal: number;
    updated_at: string; // ISO timestamp
}

const CART_STORAGE_KEY = 'guest_cart';
const CART_VERSION = '1.0'; // For future migrations if needed

/**
 * Get guest cart data from localStorage
 */
export function getGuestCartFromStorage(): LocalStorageCartData | null {
    try {
        const stored = localStorage.getItem(CART_STORAGE_KEY);
        if (!stored) {
            return null;
        }

        const parsed = JSON.parse(stored);
        
        // Validate the structure
        if (!parsed || typeof parsed !== 'object' || !Array.isArray(parsed.items)) {
            console.warn('Invalid cart data in localStorage, clearing...');
            clearGuestCartFromStorage();
            return null;
        }

        return parsed as LocalStorageCartData;
    } catch (error) {
        console.error('Error reading guest cart from localStorage:', error);
        clearGuestCartFromStorage();
        return null;
    }
}

/**
 * Save guest cart data to localStorage
 */
export function saveGuestCartToStorage(cartData: LocalStorageCartData): void {
    try {
        const dataToStore = {
            ...cartData,
            version: CART_VERSION,
            updated_at: new Date().toISOString()
        };
        
        localStorage.setItem(CART_STORAGE_KEY, JSON.stringify(dataToStore));
    } catch (error) {
        console.error('Error saving guest cart to localStorage:', error);
        // Handle quota exceeded or other storage errors
        if (error instanceof DOMException && error.code === 22) {
            console.warn('localStorage quota exceeded, clearing old cart data');
            clearGuestCartFromStorage();
        }
    }
}

/**
 * Clear guest cart data from localStorage
 */
export function clearGuestCartFromStorage(): void {
    try {
        localStorage.removeItem(CART_STORAGE_KEY);
    } catch (error) {
        console.error('Error clearing guest cart from localStorage:', error);
    }
}

/**
 * Add item to guest cart in localStorage
 */
export function addItemToGuestCart(productId: number, quantity: number = 1, productOptions?: Record<string, unknown>): LocalStorageCartData {
    const currentCart = getGuestCartFromStorage() || {
        items: [],
        total_items: 0,
        subtotal: 0,
        updated_at: new Date().toISOString()
    };

    // Check if item already exists
    const existingItemIndex = currentCart.items.findIndex(item => 
        item.product_id === productId && 
        JSON.stringify(item.product_options || {}) === JSON.stringify(productOptions || {})
    );

    if (existingItemIndex >= 0) {
        // Update existing item quantity
        currentCart.items[existingItemIndex].quantity += quantity;
    } else {
        // Add new item
        const newItem: LocalStorageCartItem = {
            id: `${productId}_${Date.now()}`,
            product_id: productId,
            quantity,
            product_options: productOptions,
            added_at: new Date().toISOString()
        };
        currentCart.items.push(newItem);
    }

    // Recalculate totals (note: we don't have price info in localStorage, so subtotal will be 0)
    currentCart.total_items = currentCart.items.reduce((sum, item) => sum + item.quantity, 0);
    currentCart.subtotal = 0; // Will be calculated when cart is loaded with product data
    currentCart.updated_at = new Date().toISOString();

    saveGuestCartToStorage(currentCart);
    return currentCart;
}

/**
 * Update item quantity in guest cart
 */
export function updateGuestCartItem(itemId: string, quantity: number): LocalStorageCartData {
    const currentCart = getGuestCartFromStorage() || {
        items: [],
        total_items: 0,
        subtotal: 0,
        updated_at: new Date().toISOString()
    };

    const itemIndex = currentCart.items.findIndex(item => item.id === itemId);
    
    if (itemIndex >= 0) {
        if (quantity <= 0) {
            // Remove item if quantity is 0 or negative
            currentCart.items.splice(itemIndex, 1);
        } else {
            // Update quantity
            currentCart.items[itemIndex].quantity = quantity;
        }

        // Recalculate totals
        currentCart.total_items = currentCart.items.reduce((sum, item) => sum + item.quantity, 0);
        currentCart.updated_at = new Date().toISOString();

        saveGuestCartToStorage(currentCart);
    }

    return currentCart;
}

/**
 * Remove item from guest cart
 */
export function removeItemFromGuestCart(itemId: string): LocalStorageCartData {
    return updateGuestCartItem(itemId, 0);
}

/**
 * Get cart items count from localStorage (for quick access)
 */
export function getGuestCartItemCount(): number {
    const cart = getGuestCartFromStorage();
    return cart ? cart.total_items : 0;
}

/**
 * Check if localStorage is available
 */
export function isLocalStorageAvailable(): boolean {
    try {
        const test = '__localStorage_test__';
        localStorage.setItem(test, test);
        localStorage.removeItem(test);
        return true;
    } catch {
        return false;
    }
}

/**
 * Convert localStorage cart items to format expected by backend API
 */
export function convertGuestCartForTransfer(guestCart: LocalStorageCartData): Array<{
    product_id: number;
    quantity: number;
    product_options?: Record<string, unknown>;
}> {
    return guestCart.items.map(item => ({
        product_id: item.product_id,
        quantity: item.quantity,
        product_options: item.product_options
    }));
}

/**
 * Merge two cart datasets, handling conflicts by combining quantities
 */
export function mergeCartData(
    primaryCart: LocalStorageCartData, 
    secondaryCart: LocalStorageCartData
): LocalStorageCartData {
    const mergedItems = [...primaryCart.items];

    secondaryCart.items.forEach(secondaryItem => {
        const existingIndex = mergedItems.findIndex(item => 
            item.product_id === secondaryItem.product_id &&
            JSON.stringify(item.product_options || {}) === JSON.stringify(secondaryItem.product_options || {})
        );

        if (existingIndex >= 0) {
            // Combine quantities
            mergedItems[existingIndex].quantity += secondaryItem.quantity;
        } else {
            // Add new item with new ID to avoid conflicts
            mergedItems.push({
                ...secondaryItem,
                id: `${secondaryItem.product_id}_${Date.now()}_merged`
            });
        }
    });

    const mergedCart: LocalStorageCartData = {
        items: mergedItems,
        total_items: mergedItems.reduce((sum, item) => sum + item.quantity, 0),
        subtotal: 0, // Will be calculated with product data
        updated_at: new Date().toISOString()
    };

    return mergedCart;
}
