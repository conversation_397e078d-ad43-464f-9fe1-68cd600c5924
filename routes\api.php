<?php

use App\Http\Controllers\Api\CategoryController;
use App\Http\Controllers\Api\CheckoutController;
use App\Http\Controllers\Api\CouponController;
use App\Http\Controllers\Api\OrderController;
use App\Http\Controllers\Api\ProductController;
use App\Http\Controllers\Api\ReviewController;
use App\Http\Controllers\Api\ShoppingCartController;
use App\Http\Controllers\Api\UserController;
use App\Http\Controllers\Api\UserAddressController;
use App\Http\Controllers\Api\UserPaymentMethodController;
use App\Http\Controllers\Api\UserPreferenceController;
use App\Http\Controllers\Api\UserSecurityController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// API Health Check
Route::get('health', function (Request $request) {
    \Log::info('API Health Check Request', [
        'method' => $request->method(),
        'url' => $request->fullUrl(),
        'ip' => $request->ip(),
        'user_agent' => $request->userAgent(),
    ]);

    try {
        // Test database connection
        $dbStatus = 'connected';
        $dbError = null;
        try {
            DB::connection()->getPdo();
            DB::connection()->select('SELECT 1');
        } catch (\Exception $e) {
            $dbStatus = 'error';
            $dbError = $e->getMessage();
        }

        $response = [
            'success' => true,
            'message' => 'API is working correctly',
            'timestamp' => now()->toISOString(),
            'version' => 'v1',
            'environment' => app()->environment(),
            'database' => [
                'status' => $dbStatus,
                'error' => $dbError,
            ],
            'server' => [
                'php_version' => PHP_VERSION,
                'laravel_version' => app()->version(),
            ]
        ];

        \Log::info('API Health Check Success', $response);
        return response()->json($response);
    } catch (\Exception $e) {
        \Log::error('API Health Check Error', [
            'error' => $e->getMessage(),
            'trace' => $e->getTraceAsString(),
        ]);

        return response()->json([
            'success' => false,
            'message' => 'API health check failed',
            'error' => config('app.debug') ? $e->getMessage() : 'Internal server error',
            'timestamp' => now()->toISOString(),
        ], 500);
    }
});

// Public API routes (no authentication required)
Route::prefix('v1')->group(function () {
    // Products
    Route::get('products', [ProductController::class, 'index']);
    Route::post('products/batch', [ProductController::class, 'batch']);
    Route::get('products/{product}', [ProductController::class, 'show']);
    Route::get('products/{product}/reviews', [ProductController::class, 'reviews']);
    
    // Categories
    Route::get('categories', [CategoryController::class, 'index']);
    Route::get('categories/{category}', [CategoryController::class, 'show']);
    Route::get('categories/{category}/products', [CategoryController::class, 'products']);
    
    // Reviews (public read-only)
    Route::get('reviews', [ReviewController::class, 'index']);
    Route::get('reviews/{review}', [ReviewController::class, 'show']);
    
    // Coupons (validation only)
    Route::post('coupons/validate', [CouponController::class, 'validate']);
});

// Unified shopping cart routes (handles both guest and authenticated users)
Route::prefix('v1/cart')->middleware(['web'])->group(function () {
    Route::get('/', [ShoppingCartController::class, 'index']);
    Route::post('/', [ShoppingCartController::class, 'store']);
    Route::put('/{cartItem}', [ShoppingCartController::class, 'update']);
    Route::delete('/{cartItem}', [ShoppingCartController::class, 'destroy']);
    Route::delete('/', [ShoppingCartController::class, 'clear']);

    // Cart transfer endpoint (requires authentication)
    Route::post('/transfer-guest-cart', [ShoppingCartController::class, 'transferGuestCart'])
        ->middleware('auth:sanctum');
});

// Checkout routes (handles both guest and authenticated users)
Route::prefix('v1/checkout')->middleware(['web'])->group(function () {
    Route::get('/shipping-methods', [CheckoutController::class, 'getShippingMethods']);
    Route::get('/payment-methods', [CheckoutController::class, 'getPaymentMethods']);
    Route::post('/validate-coupon', [CheckoutController::class, 'validateCoupon']);
    Route::post('/calculate-totals', [CheckoutController::class, 'calculateTotals']);
    Route::post('/process', [CheckoutController::class, 'processCheckout']);
});

// Authenticated API routes
Route::middleware(['web', 'auth'])->prefix('v1')->group(function () {
    // User profile
    Route::get('user', function (Request $request) {
        return $request->user();
    });

    // Test endpoint to check authentication
    Route::get('test-auth', function (Request $request) {
        return response()->json([
            'authenticated' => auth()->check(),
            'user' => auth()->user(),
            'guard' => auth()->getDefaultDriver(),
        ]);
    });
    Route::put('user', [UserController::class, 'update']);

    // User's orders
    Route::get('orders', [OrderController::class, 'index']);
    Route::get('orders/{order}', [OrderController::class, 'show']);
    Route::post('orders', [OrderController::class, 'store']);
    Route::put('orders/{order}', [OrderController::class, 'update']);

    // User's reviews
    Route::post('reviews', [ReviewController::class, 'store']);
    Route::put('reviews/{review}', [ReviewController::class, 'update']);
    Route::delete('reviews/{review}', [ReviewController::class, 'destroy']);

    // User profile management
    // Addresses
    Route::apiResource('addresses', UserAddressController::class);
    Route::put('addresses/{address}/default', [UserAddressController::class, 'setDefault']);

    // Payment methods
    Route::apiResource('payment-methods', UserPaymentMethodController::class);
    Route::put('payment-methods/{paymentMethod}/default', [UserPaymentMethodController::class, 'setDefault']);
    Route::put('payment-methods/{paymentMethod}/verify', [UserPaymentMethodController::class, 'verify']);

    // User preferences
    Route::get('preferences', [UserPreferenceController::class, 'show']);
    Route::put('preferences', [UserPreferenceController::class, 'update']);
    Route::get('preferences/options', [UserPreferenceController::class, 'options']);
    Route::put('preferences/communication', [UserPreferenceController::class, 'updateCommunication']);
    Route::put('preferences/privacy', [UserPreferenceController::class, 'updatePrivacy']);

    // Security settings
    Route::get('security', [UserSecurityController::class, 'show']);
    Route::put('security', [UserSecurityController::class, 'update']);
    Route::put('security/password', [UserSecurityController::class, 'changePassword']);
    Route::post('security/two-factor/enable', [UserSecurityController::class, 'enableTwoFactor']);
    Route::delete('security/two-factor/disable', [UserSecurityController::class, 'disableTwoFactor']);
    Route::get('security/trusted-devices', [UserSecurityController::class, 'trustedDevices']);
    Route::delete('security/trusted-devices', [UserSecurityController::class, 'removeTrustedDevice']);
    Route::get('security/login-activity', [UserSecurityController::class, 'loginActivity']);
});

// Admin API routes (requires authentication and admin role)
Route::middleware(['auth:sanctum', 'admin'])->prefix('v1/admin')->group(function () {
    // Products management
    Route::apiResource('products', ProductController::class)->except(['index', 'show']);
    
    // Categories management
    Route::apiResource('categories', CategoryController::class)->except(['index', 'show']);
    
    // Orders management
    Route::get('orders', [OrderController::class, 'adminIndex']);
    Route::put('orders/{order}/status', [OrderController::class, 'updateStatus']);
    Route::delete('orders/{order}', [OrderController::class, 'destroy']);
    
    // Reviews management
    Route::get('reviews', [ReviewController::class, 'adminIndex']);
    Route::put('reviews/{review}/approve', [ReviewController::class, 'approve']);
    Route::put('reviews/{review}/reject', [ReviewController::class, 'reject']);
    
    // Coupons management
    Route::apiResource('coupons', CouponController::class);
    
    // Users management
    Route::apiResource('users', UserController::class);
    
    // Shopping cart management (admin view)
    Route::get('carts', [ShoppingCartController::class, 'adminIndex']);
});
