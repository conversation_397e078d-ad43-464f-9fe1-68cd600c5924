import React, { useState, useCallback } from 'react';
import { Mail, Phone, User, Eye, EyeOff } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';

interface CustomerInfo {
    email: string;
    phone?: string;
    create_account?: boolean;
    password?: string;
}

interface CustomerInfoStepProps {
    data: Partial<CustomerInfo>;
    onUpdate: (data: CustomerInfo) => void;
    onNext: () => void;
    isAuthenticated: boolean;
}

const CustomerInfoStep: React.FC<CustomerInfoStepProps> = ({
    data,
    onUpdate,
    onNext,
    isAuthenticated
}) => {
    const [formData, setFormData] = useState<CustomerInfo>({
        email: data.email || '',
        phone: data.phone || '',
        create_account: data.create_account || false,
        password: data.password || '',
    });
    
    const [errors, setErrors] = useState<Record<string, string>>({});
    const [showPassword, setShowPassword] = useState(false);

    const handleInputChange = useCallback((field: keyof CustomerInfo, value: string | boolean) => {
        setFormData(prev => {
            const updated = { ...prev, [field]: value };
            onUpdate(updated);
            return updated;
        });
        
        // Clear error when user starts typing
        if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
        }
    }, [onUpdate, errors]);

    const validateForm = useCallback(() => {
        const newErrors: Record<string, string> = {};

        // Email validation
        if (!formData.email) {
            newErrors.email = 'Email is required';
        } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = 'Please enter a valid email address';
        }

        // Phone validation (optional but if provided, should be valid)
        if (formData.phone && !/^(\+63|0)[0-9]{10}$/.test(formData.phone.replace(/\s/g, ''))) {
            newErrors.phone = 'Please enter a valid Philippine phone number';
        }

        // Password validation (only if creating account)
        if (!isAuthenticated && formData.create_account) {
            if (!formData.password) {
                newErrors.password = 'Password is required when creating an account';
            } else if (formData.password.length < 8) {
                newErrors.password = 'Password must be at least 8 characters long';
            }
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    }, [formData, isAuthenticated]);

    const handleNext = useCallback(() => {
        if (validateForm()) {
            onNext();
        }
    }, [validateForm, onNext]);

    const formatPhoneNumber = (value: string) => {
        // Remove all non-digits
        const digits = value.replace(/\D/g, '');
        
        // Format as Philippine number
        if (digits.startsWith('63')) {
            return `+${digits.slice(0, 2)} ${digits.slice(2, 5)} ${digits.slice(5, 8)} ${digits.slice(8, 12)}`;
        } else if (digits.startsWith('0')) {
            return `${digits.slice(0, 4)} ${digits.slice(4, 7)} ${digits.slice(7, 11)}`;
        }
        
        return value;
    };

    return (
        <div className="space-y-6">
            <div>
                <h2 className="text-2xl font-semibold mb-2">Contact Information</h2>
                <p className="text-muted-foreground">
                    {isAuthenticated 
                        ? 'Verify your contact details for this order'
                        : 'Enter your contact information to continue'
                    }
                </p>
            </div>

            <div className="space-y-4">
                {/* Email Field */}
                <div className="space-y-2">
                    <Label htmlFor="email" className="flex items-center gap-2">
                        <Mail className="h-4 w-4" />
                        Email Address *
                    </Label>
                    <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={formData.email}
                        onChange={(e) => handleInputChange('email', e.target.value)}
                        disabled={isAuthenticated}
                        className={errors.email ? 'border-destructive' : ''}
                    />
                    {errors.email && (
                        <p className="text-sm text-destructive">{errors.email}</p>
                    )}
                    {isAuthenticated && (
                        <p className="text-sm text-muted-foreground">
                            Using your account email address
                        </p>
                    )}
                </div>

                {/* Phone Field */}
                <div className="space-y-2">
                    <Label htmlFor="phone" className="flex items-center gap-2">
                        <Phone className="h-4 w-4" />
                        Phone Number
                        <span className="text-muted-foreground text-sm">(optional)</span>
                    </Label>
                    <Input
                        id="phone"
                        type="tel"
                        placeholder="+63 ************ or 0************"
                        value={formData.phone}
                        onChange={(e) => {
                            const formatted = formatPhoneNumber(e.target.value);
                            handleInputChange('phone', formatted);
                        }}
                        className={errors.phone ? 'border-destructive' : ''}
                    />
                    {errors.phone && (
                        <p className="text-sm text-destructive">{errors.phone}</p>
                    )}
                    <p className="text-sm text-muted-foreground">
                        We'll use this for delivery updates and order notifications
                    </p>
                </div>

                {/* Account Creation Section (for guest users) */}
                {!isAuthenticated && (
                    <>
                        <Separator />
                        
                        <div className="space-y-4">
                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="create-account"
                                    checked={formData.create_account}
                                    onCheckedChange={(checked) => 
                                        handleInputChange('create_account', checked as boolean)
                                    }
                                />
                                <Label htmlFor="create-account" className="flex items-center gap-2">
                                    <User className="h-4 w-4" />
                                    Create an account for faster checkout next time
                                </Label>
                            </div>

                            {formData.create_account && (
                                <div className="space-y-2 ml-6">
                                    <Label htmlFor="password">
                                        Password *
                                    </Label>
                                    <div className="relative">
                                        <Input
                                            id="password"
                                            type={showPassword ? 'text' : 'password'}
                                            placeholder="Enter a secure password"
                                            value={formData.password}
                                            onChange={(e) => handleInputChange('password', e.target.value)}
                                            className={errors.password ? 'border-destructive pr-10' : 'pr-10'}
                                        />
                                        <Button
                                            type="button"
                                            variant="ghost"
                                            size="sm"
                                            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                            onClick={() => setShowPassword(!showPassword)}
                                        >
                                            {showPassword ? (
                                                <EyeOff className="h-4 w-4" />
                                            ) : (
                                                <Eye className="h-4 w-4" />
                                            )}
                                        </Button>
                                    </div>
                                    {errors.password && (
                                        <p className="text-sm text-destructive">{errors.password}</p>
                                    )}
                                    <p className="text-sm text-muted-foreground">
                                        Password must be at least 8 characters long
                                    </p>
                                </div>
                            )}

                            <div className="bg-muted/50 p-4 rounded-lg">
                                <h4 className="font-medium mb-2">Account Benefits</h4>
                                <ul className="text-sm text-muted-foreground space-y-1">
                                    <li>• Track your orders easily</li>
                                    <li>• Save addresses for faster checkout</li>
                                    <li>• View order history and reorder items</li>
                                    <li>• Get exclusive offers and early access</li>
                                </ul>
                            </div>
                        </div>
                    </>
                )}
            </div>

            {/* Continue Button */}
            <div className="flex justify-end pt-4">
                <Button 
                    onClick={handleNext}
                    size="lg"
                    className="px-8"
                >
                    Continue to Shipping
                </Button>
            </div>
        </div>
    );
};

export default CustomerInfoStep;
