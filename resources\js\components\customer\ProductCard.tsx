import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import { Check, Eye, Heart, ShoppingCart } from 'lucide-react';
import React from 'react';
import { StarIcon } from './Icons';
import ProductImage from './ProductImage';
import { useAddToCartAnimation, getAddToCartButtonText, getAddToCartButtonClasses } from '@/hooks/use-add-to-cart-animation';

interface Product {
    id: number;
    name: string;
    price: string;
    originalPrice: string;
    image: string;
    rating: number;
    reviews: number;
}

interface ProductCardProps {
    product: Product;
    onAddToCart?: (productId: number) => void;
    onQuickView?: (productId: number) => void;
    onAddToWishlist?: (productId: number) => void;
}

const ProductCard: React.FC<ProductCardProps> = ({ product, onAddToCart, onQuickView, onAddToWishlist }) => {
    const { auth } = usePage<SharedData>().props;
    const isAuthenticated = !!auth?.user;

    // Use animation hook for add to cart functionality
    const {
        state: addToCartState,
        isLoading,
        isSuccess,
        handleAddToCart: animatedAddToCart
    } = useAddToCartAnimation({
        productId: product.id,
        quantity: 1,
        onSuccess: () => {
            // Call the original onAddToCart callback if provided
            onAddToCart?.(product.id);
        }
    });

    const handleQuickView = () => {
        onQuickView?.(product.id);
    };

    const handleAddToWishlist = () => {
        onAddToWishlist?.(product.id);
    };

    // Get button text and classes based on animation state
    const buttonText = getAddToCartButtonText(addToCartState);
    const buttonClasses = getAddToCartButtonClasses(addToCartState, 'w-full');

    return (
        <Card className="group overflow-hidden transition-all duration-300 hover:-translate-y-1 hover:shadow-xl">
            <div className="relative">
                <ProductImage
                    src={product.image}
                    alt={product.name}
                    size="xl"
                    className="aspect-square w-full"
                />
                {/* Quick View Button */}
                {onQuickView && (
                    <Button
                        variant="secondary"
                        size="sm"
                        className="absolute top-2 right-2 opacity-0 transition-opacity group-hover:opacity-100"
                        onClick={handleQuickView}
                    >
                        <Eye className="h-4 w-4" />
                    </Button>
                )}
            </div>
            <CardContent className="p-6">
                <h3 className="mb-2 font-semibold text-foreground">{product.name}</h3>
                <div className="mb-3 flex items-center">
                    <div className="flex text-sm text-yellow-400">
                        {[...Array(5)].map((_, i) => (
                            <StarIcon key={i} />
                        ))}
                    </div>
                    <span className="ml-2 text-sm text-muted-foreground">
                        {product.rating} ({product.reviews})
                    </span>
                </div>
                <div className="mb-4 flex items-center justify-between">
                    <div>
                        <span className="text-lg font-bold text-primary">{product.price}</span>
                        <span className="ml-2 text-sm text-muted-foreground line-through">{product.originalPrice}</span>
                    </div>
                    {/* Wishlist button for authenticated users */}
                    {isAuthenticated && onAddToWishlist && (
                        <Button variant="ghost" size="sm" onClick={handleAddToWishlist} className="p-2">
                            <Heart className="h-4 w-4" />
                        </Button>
                    )}
                </div>

                {/* Add to Cart button - available for all users */}
                <Button
                    className={buttonClasses}
                    onClick={animatedAddToCart}
                    disabled={isLoading}
                >
                    <div className="flex items-center justify-center gap-2">
                        {isLoading && (
                            <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                        )}
                        {isSuccess && <Check className="h-4 w-4" />}
                        {!isLoading && !isSuccess && <ShoppingCart className="h-4 w-4" />}
                        <span>{buttonText}</span>
                    </div>
                </Button>
            </CardContent>
        </Card>
    );
};

export default ProductCard;
