<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_security_settings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            
            // Two-factor authentication
            $table->boolean('two_factor_enabled')->default(false);
            $table->string('two_factor_secret')->nullable();
            $table->json('two_factor_recovery_codes')->nullable();
            $table->timestamp('two_factor_confirmed_at')->nullable();
            
            // Login security
            $table->boolean('login_notifications')->default(true);
            $table->json('trusted_devices')->nullable(); // Array of device fingerprints
            $table->timestamp('password_changed_at')->nullable();
            $table->integer('failed_login_attempts')->default(0);
            $table->timestamp('locked_until')->nullable();
            
            // Session management
            $table->boolean('logout_other_devices')->default(false);
            $table->integer('session_timeout_minutes')->default(120); // 2 hours default
            
            // Security questions (optional future feature)
            $table->json('security_questions')->nullable();
            
            $table->timestamps();

            // Add indexes for better performance
            $table->index(['user_id']);
            $table->unique(['user_id']); // One security settings record per user
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_security_settings');
    }
};
