services:
  - type: web
    name: lucky-star-laravel
    runtime: docker
    plan: starter
    region: oregon
    branch: main
    healthCheckPath: /up
    envVars:
      - key: APP_NAME
        value: "Lucky Star E-commerce"
      - key: APP_ENV
        value: production
      - key: APP_DEBUG
        value: false
      - key: LOG_CHANNEL
        value: stderr
      - key: APP_KEY
        value: base64:mZeIIwHp72ahtkbNQmAOFi6LWqFhkrsYKZDvGkQun1U=
      # External Neon.tech Database Configuration
      - key: DB_CONNECTION
        value: pgsql
      - key: DB_HOST
        value: ep-green-snowflake-a1bxdo4q.ap-southeast-1.aws.neon.tech
      - key: DB_PORT
        value: 5432
      - key: DB_DATABASE
        value: luckystar_db
      - key: DB_USERNAME
        value: luckystar_db_owner
      - key: DB_PASSWORD
        value: npg_dw2mgQ6HRotp
      # Force SSL for database connection
      - key: DB_SSLMODE
        value: require
      # Vite Configuration
      - key: VITE_APP_NAME
        value: "Lucky Star E-commerce"
      - key: VITE_APP_ENV
        value: production
      - key: APP_URL
        fromService:
          type: web
          name: lucky-star-laravel
          property: host
      - key: ASSET_URL
        fromService:
          type: web
          name: lucky-star-laravel
          property: host

# Note: No database section needed since using external Neon.tech PostgreSQL
