<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained('users')->onDelete('cascade');
            
            // Communication preferences
            $table->boolean('email_marketing')->default(true);
            $table->boolean('email_order_updates')->default(true);
            $table->boolean('email_promotions')->default(true);
            $table->boolean('sms_marketing')->default(false);
            $table->boolean('sms_order_updates')->default(true);
            $table->boolean('push_notifications')->default(true);
            
            // Language and locale preferences
            $table->string('language', 5)->default('en');
            $table->string('currency', 3)->default('PHP');
            $table->string('timezone')->default('Asia/Manila');
            
            // Shopping preferences
            $table->string('preferred_delivery_time')->nullable(); // morning, afternoon, evening
            $table->json('dietary_restrictions')->nullable(); // vegetarian, halal, etc.
            $table->json('favorite_categories')->nullable(); // Array of category IDs
            
            // Privacy preferences
            $table->boolean('profile_public')->default(false);
            $table->boolean('show_order_history')->default(false);
            $table->boolean('allow_reviews_display')->default(true);
            
            $table->timestamps();

            // Add indexes for better performance
            $table->index(['user_id']);
            $table->unique(['user_id']); // One preference record per user
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_preferences');
    }
};
