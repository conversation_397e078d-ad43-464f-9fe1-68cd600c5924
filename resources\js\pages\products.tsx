import { Head, Link, usePage } from '@inertiajs/react';
import { Filter, Grid, List, Search, User as UserIcon } from 'lucide-react';
import { memo, useCallback, useEffect, useMemo, useState } from 'react';

// Import customer components
import Footer from '@/components/customer/Footer';
import Header from '@/components/customer/Header';
import ProductCard from '@/components/customer/ProductCard';
import ProductListItem from '@/components/customer/ProductListItem';
import ProductQuickView from '@/components/customer/ProductQuickView';
import { CartProviderWrapper } from '@/components/CartProviderWrapper';
import { useCart } from '@/hooks/use-cart';

// Import UI components
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { Skeleton } from '@/components/ui/skeleton';

import { type SharedData } from '@/types';

// Types
interface Product {
    id: number;
    name: string;
    slug: string;
    description: string;
    short_description: string;
    sku: string;
    price: string; // API returns price as string
    compare_price?: string | null; // API returns compare_price as string or null
    stock_quantity: number;
    is_active: boolean;
    is_featured: boolean;
    images: string[];
    attributes: Record<string, unknown>;
    category: {
        id: number;
        name: string;
        slug: string;
    };
    reviews: unknown[];
    discount_percentage?: number;
}

interface Category {
    id: number;
    name: string;
    slug: string;
    is_active: boolean;
}

interface ProductsResponse {
    success: boolean;
    data: Product[];
    meta: {
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        from: number;
        to: number;
    };
    links: {
        first: string;
        last: string;
        prev: string | null;
        next: string | null;
    };
}

interface Filters {
    search: string;
    category: string;
    minPrice: string;
    maxPrice: string;
    inStock: boolean;
    featured: boolean;
}

type SortOption = 'name_asc' | 'name_desc' | 'price_asc' | 'price_desc' | 'created_desc';
type ViewMode = 'grid' | 'list';

// Utility functions for price handling
const formatPrice = (price: string | number | null | undefined): string => {
    if (price === null || price === undefined || price === '') {
        return '₱0.00';
    }

    const numericPrice = typeof price === 'string' ? parseFloat(price) : price;

    if (isNaN(numericPrice)) {
        return '₱0.00';
    }

    return `₱${numericPrice.toFixed(2)}`;
};



// Memoized Product Grid Component
const ProductGrid = memo(
    ({
        products,
        onAddToCart,
        onQuickView,
        onAddToWishlist,
    }: {
        products: Product[];
        onAddToCart: (productId: number) => void;
        onQuickView: (productId: number) => void;
        onAddToWishlist: (productId: number) => void;
    }) => (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {products.map((product) => (
                <ProductCard
                    key={product.id}
                    product={{
                        id: product.id,
                        name: product.name,
                        price: formatPrice(product.price),
                        originalPrice: product.compare_price ? formatPrice(product.compare_price) : '',
                        image: product.images[0] || '🛒',
                        rating: 4.5, // TODO: Calculate from reviews
                        reviews: product.reviews.length,
                    }}
                    onAddToCart={onAddToCart}
                    onQuickView={onQuickView}
                    onAddToWishlist={onAddToWishlist}
                />
            ))}
        </div>
    ),
);

// Memoized Product List Component
const ProductList = memo(
    ({
        products,
        onAddToCart,
        onQuickView,
        onAddToWishlist,
    }: {
        products: Product[];
        onAddToCart: (productId: number) => void;
        onQuickView: (productId: number) => void;
        onAddToWishlist: (productId: number) => void;
    }) => (
        <div className="space-y-4">
            {products.map((product) => (
                <ProductListItem
                    key={product.id}
                    product={{
                        id: product.id,
                        name: product.name,
                        price: formatPrice(product.price),
                        originalPrice: product.compare_price ? formatPrice(product.compare_price) : '',
                        image: product.images[0] || '🛒',
                        rating: 4.5, // TODO: Calculate from reviews
                        reviews: product.reviews.length,
                        description: product.short_description || product.description,
                        category: product.category.name,
                        inStock: product.stock_quantity > 0,
                    }}
                    onAddToCart={onAddToCart}
                    onQuickView={onQuickView}
                    onAddToWishlist={onAddToWishlist}
                />
            ))}
        </div>
    ),
);

// Inner component that uses cart functionality
function ProductsContent() {
    const { auth } = usePage<SharedData>().props;
    const isAuthenticated = !!auth?.user;
    const { addToCartOptimistic } = useCart();

    // Get URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const initialSearch = urlParams.get('search') || '';
    const initialCategory = urlParams.get('category') || '';
    const initialMinPrice = urlParams.get('min_price') || '';
    const initialMaxPrice = urlParams.get('max_price') || '';
    const initialInStock = urlParams.get('in_stock') === '1';
    const initialFeatured = urlParams.get('featured') === '1';
    const initialSort = (urlParams.get('sort') || 'created_desc') as SortOption;
    const initialPage = parseInt(urlParams.get('page') || '1');
    const initialViewMode = (urlParams.get('view') || 'grid') as ViewMode;

    // State management
    const [products, setProducts] = useState<Product[]>([]);
    const [categories, setCategories] = useState<Category[]>([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [viewMode, setViewMode] = useState<ViewMode>(initialViewMode);
    const [currentPage, setCurrentPage] = useState(initialPage);
    const [totalPages, setTotalPages] = useState(1);
    const [totalProducts, setTotalProducts] = useState(0);
    const [perPage] = useState(12);

    // Filter and sort state
    const [filters, setFilters] = useState<Filters>({
        search: initialSearch,
        category: initialCategory,
        minPrice: initialMinPrice,
        maxPrice: initialMaxPrice,
        inStock: initialInStock,
        featured: initialFeatured,
    });
    const [sortBy, setSortBy] = useState<SortOption>(initialSort);
    const [searchInput, setSearchInput] = useState(initialSearch);

    // Debounced search
    const [debouncedSearch, setDebouncedSearch] = useState('');

    // Quick view state
    const [quickViewProduct, setQuickViewProduct] = useState<Product | null>(null);
    const [isQuickViewOpen, setIsQuickViewOpen] = useState(false);

    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedSearch(searchInput);
        }, 300);

        return () => clearTimeout(timer);
    }, [searchInput]);

    useEffect(() => {
        setFilters((prev) => ({ ...prev, search: debouncedSearch }));
        setCurrentPage(1); // Reset to first page when search changes
    }, [debouncedSearch]);

    // Function to update URL with current state
    const updateURL = useCallback((newFilters: Filters, newPage: number, newSort: SortOption, newViewMode: ViewMode) => {
        const params = new URLSearchParams();

        if (newFilters.search) params.set('search', newFilters.search);
        if (newFilters.category) params.set('category', newFilters.category);
        if (newFilters.minPrice) params.set('min_price', newFilters.minPrice);
        if (newFilters.maxPrice) params.set('max_price', newFilters.maxPrice);
        if (newFilters.inStock) params.set('in_stock', '1');
        if (newFilters.featured) params.set('featured', '1');
        if (newSort !== 'created_desc') params.set('sort', newSort);
        if (newPage > 1) params.set('page', newPage.toString());
        if (newViewMode !== 'grid') params.set('view', newViewMode);

        const newUrl = `${window.location.pathname}${params.toString() ? '?' + params.toString() : ''}`;
        window.history.replaceState({}, '', newUrl);
    }, []);

    // Fetch products function
    const fetchProducts = useCallback(async () => {
        setLoading(true);
        setError(null);

        try {
            const params = new URLSearchParams();
            params.append('page', currentPage.toString());
            params.append('per_page', perPage.toString());

            if (filters.search) params.append('search', filters.search);
            if (filters.category) params.append('category', filters.category);
            if (filters.minPrice) params.append('min_price', filters.minPrice);
            if (filters.maxPrice) params.append('max_price', filters.maxPrice);
            if (filters.inStock) params.append('in_stock', '1');
            if (filters.featured) params.append('featured', '1');

            // Handle sorting
            const [sortField, sortDirection] = sortBy.split('_');
            params.append('sort', sortField);
            params.append('direction', sortDirection);

            const response = await fetch(`/api/v1/products?${params.toString()}`);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data: ProductsResponse = await response.json();

            if (data.success) {
                setProducts(data.data);
                setTotalPages(data.meta.last_page);
                setTotalProducts(data.meta.total);
            } else {
                throw new Error('Failed to fetch products');
            }
        } catch (err) {
            console.error('Error fetching products:', err);
            setError(err instanceof Error ? err.message : 'Failed to load products');
            setProducts([]);
        } finally {
            setLoading(false);
        }
    }, [currentPage, perPage, filters, sortBy]);

    // Fetch categories function
    const fetchCategories = useCallback(async () => {
        try {
            const response = await fetch('/api/v1/categories');

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                setCategories(data.data);
            }
        } catch (err) {
            console.error('Error fetching categories:', err);
        }
    }, []);

    // Effects
    useEffect(() => {
        fetchProducts();
    }, [fetchProducts]);

    useEffect(() => {
        fetchCategories();
    }, [fetchCategories]);

    // Filter handlers
    const handleFilterChange = (key: keyof Filters, value: string | boolean) => {
        const newFilters = { ...filters, [key]: value };
        setFilters(newFilters);
        setCurrentPage(1); // Reset to first page when filters change
        updateURL(newFilters, 1, sortBy, viewMode);
    };

    const clearFilters = () => {
        const newFilters = {
            search: '',
            category: '',
            minPrice: '',
            maxPrice: '',
            inStock: false,
            featured: false,
        };
        setFilters(newFilters);
        setSearchInput('');
        setCurrentPage(1);
        updateURL(newFilters, 1, sortBy, viewMode);
    };

    // Pagination handlers
    const handlePageChange = (page: number) => {
        setCurrentPage(page);
        updateURL(filters, page, sortBy, viewMode);
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    // Sort handler
    const handleSortChange = (newSort: SortOption) => {
        setSortBy(newSort);
        updateURL(filters, currentPage, newSort, viewMode);
    };

    // View mode handler
    const handleViewModeChange = (newViewMode: ViewMode) => {
        setViewMode(newViewMode);
        updateURL(filters, currentPage, sortBy, newViewMode);
    };

    // Add to cart handler
    const handleAddToCart = async (productId: number, quantity: number = 1) => {
        try {
            const success = await addToCartOptimistic(productId, quantity);
            if (success) {
                // Show success message (you could add a toast notification here)
                console.log('Product added to cart successfully');
            } else {
                // Error is already handled by the cart hook
                console.error('Failed to add product to cart');
            }
        } catch (error) {
            console.error('Error adding product to cart:', error);
        }
    };

    // Add to wishlist handler
    const handleAddToWishlist = (productId: number) => {
        if (!isAuthenticated) {
            // Redirect to login if not authenticated
            window.location.href = route('login');
            return;
        }

        // TODO: Implement wishlist functionality
        console.log('Add to wishlist:', productId);
    };

    // Quick view handlers
    const handleQuickView = (productId: number) => {
        const product = products.find((p) => p.id === productId);
        if (product) {
            setQuickViewProduct(product);
            setIsQuickViewOpen(true);
        }
    };

    const handleCloseQuickView = () => {
        setIsQuickViewOpen(false);
        setQuickViewProduct(null);
    };

    // Memoized values
    const hasActiveFilters = useMemo(() => {
        return filters.search || filters.category || filters.minPrice || filters.maxPrice || filters.inStock || filters.featured;
    }, [filters]);

    const sortOptions = [
        { value: 'created_desc', label: 'Newest First' },
        { value: 'name_asc', label: 'Name A-Z' },
        { value: 'name_desc', label: 'Name Z-A' },
        { value: 'price_asc', label: 'Price Low to High' },
        { value: 'price_desc', label: 'Price High to Low' },
    ];

    return (
        <>
            <Head title="Products" />

            <div className="min-h-screen bg-background">
                {/* Header */}
                <Header />

                {/* Main Content */}
                <main className="container mx-auto px-4 py-8">
                    {/* Page Header */}
                    <div className="mb-8">
                        <h1 className="mb-2 text-3xl font-bold text-foreground">Products</h1>
                        <p className="text-muted-foreground">Discover our wide range of fresh groceries and quality products</p>

                        {/* Guest User Banner */}
                        {!isAuthenticated && (
                            <Card className="mt-4 border-primary/20 bg-gradient-to-r from-primary/10 to-primary/5">
                                <CardContent className="p-4">
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-3">
                                            <div className="rounded-full bg-primary/20 p-2">
                                                <UserIcon className="h-5 w-5 text-primary" />
                                            </div>
                                            <div>
                                                <h3 className="font-semibold text-foreground">Join Lucky Star for Better Shopping!</h3>
                                                <p className="text-sm text-muted-foreground">
                                                    Create an account to save favorites, track orders, and get personalized recommendations.
                                                </p>
                                            </div>
                                        </div>
                                        <div className="flex gap-2">
                                            <Link href={route('register')}>
                                                <Button size="sm">Sign Up</Button>
                                            </Link>
                                            <Link href={route('login')}>
                                                <Button variant="outline" size="sm">
                                                    Login
                                                </Button>
                                            </Link>
                                        </div>
                                    </div>
                                </CardContent>
                            </Card>
                        )}
                    </div>

                    {/* Search and Filters Bar */}
                    <div className="mb-6">
                        <div className="flex flex-col items-start justify-between gap-4 lg:flex-row lg:items-center">
                            {/* Search */}
                            <div className="max-w-md flex-1">
                                <div className="relative">
                                    <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-muted-foreground" />
                                    <Input
                                        type="text"
                                        placeholder="Search products..."
                                        value={searchInput}
                                        onChange={(e) => setSearchInput(e.target.value)}
                                        className="pl-10"
                                        aria-label="Search products"
                                        role="searchbox"
                                    />
                                </div>
                            </div>

                            {/* Controls */}
                            <div className="flex items-center gap-2">
                                {/* Mobile Filter Button */}
                                <Sheet>
                                    <SheetTrigger asChild>
                                        <Button variant="outline" size="sm" className="lg:hidden">
                                            <Filter className="mr-2 h-4 w-4" />
                                            Filters
                                            {hasActiveFilters && (
                                                <Badge variant="secondary" className="ml-2 h-5 w-5 p-0 text-xs">
                                                    !
                                                </Badge>
                                            )}
                                        </Button>
                                    </SheetTrigger>
                                    <SheetContent side="left" className="w-80">
                                        <SheetHeader>
                                            <SheetTitle>Filters</SheetTitle>
                                        </SheetHeader>
                                        <div className="mt-6 space-y-6">
                                            {/* Categories Filter */}
                                            <div>
                                                <h3 className="mb-3 font-semibold">Categories</h3>
                                                <div className="space-y-2">
                                                    <Button
                                                        variant={filters.category === '' ? 'default' : 'ghost'}
                                                        size="sm"
                                                        className="w-full justify-start"
                                                        onClick={() => handleFilterChange('category', '')}
                                                    >
                                                        All Categories
                                                    </Button>
                                                    {categories.map((category) => (
                                                        <Button
                                                            key={category.id}
                                                            variant={filters.category === category.id.toString() ? 'default' : 'ghost'}
                                                            size="sm"
                                                            className="w-full justify-start"
                                                            onClick={() => handleFilterChange('category', category.id.toString())}
                                                        >
                                                            {category.name}
                                                        </Button>
                                                    ))}
                                                </div>
                                            </div>

                                            <Separator />

                                            {/* Price Range Filter */}
                                            <div>
                                                <h3 className="mb-3 font-semibold">Price Range</h3>
                                                <div className="space-y-3">
                                                    <div>
                                                        <label className="text-sm text-muted-foreground">Min Price</label>
                                                        <Input
                                                            type="number"
                                                            placeholder="₱0"
                                                            value={filters.minPrice}
                                                            onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                                                            className="mt-1"
                                                        />
                                                    </div>
                                                    <div>
                                                        <label className="text-sm text-muted-foreground">Max Price</label>
                                                        <Input
                                                            type="number"
                                                            placeholder="₱1000"
                                                            value={filters.maxPrice}
                                                            onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                                                            className="mt-1"
                                                        />
                                                    </div>
                                                </div>
                                            </div>

                                            <Separator />

                                            {/* Quick Filters */}
                                            <div>
                                                <h3 className="mb-3 font-semibold">Quick Filters</h3>
                                                <div className="space-y-2">
                                                    <label className="flex cursor-pointer items-center space-x-2">
                                                        <input
                                                            type="checkbox"
                                                            checked={filters.inStock}
                                                            onChange={(e) => handleFilterChange('inStock', e.target.checked)}
                                                            className="rounded border-gray-300"
                                                        />
                                                        <span className="text-sm">In Stock Only</span>
                                                    </label>
                                                    <label className="flex cursor-pointer items-center space-x-2">
                                                        <input
                                                            type="checkbox"
                                                            checked={filters.featured}
                                                            onChange={(e) => handleFilterChange('featured', e.target.checked)}
                                                            className="rounded border-gray-300"
                                                        />
                                                        <span className="text-sm">Featured Products</span>
                                                    </label>
                                                </div>
                                            </div>

                                            {/* Clear Filters */}
                                            {hasActiveFilters && (
                                                <>
                                                    <Separator />
                                                    <Button variant="outline" size="sm" onClick={clearFilters} className="w-full">
                                                        Clear All Filters
                                                    </Button>
                                                </>
                                            )}
                                        </div>
                                    </SheetContent>
                                </Sheet>

                                {/* Sort */}
                                <Select value={sortBy} onValueChange={handleSortChange}>
                                    <SelectTrigger className="w-48" aria-label="Sort products">
                                        <SelectValue placeholder="Sort by" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {sortOptions.map((option) => (
                                            <SelectItem key={option.value} value={option.value}>
                                                {option.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>

                                {/* View Mode Toggle */}
                                <div className="flex rounded-md border" role="group" aria-label="View mode">
                                    <Button
                                        variant={viewMode === 'grid' ? 'default' : 'ghost'}
                                        size="sm"
                                        onClick={() => handleViewModeChange('grid')}
                                        className="rounded-r-none"
                                        aria-label="Grid view"
                                        aria-pressed={viewMode === 'grid'}
                                    >
                                        <Grid className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        variant={viewMode === 'list' ? 'default' : 'ghost'}
                                        size="sm"
                                        onClick={() => handleViewModeChange('list')}
                                        className="rounded-l-none"
                                        aria-label="List view"
                                        aria-pressed={viewMode === 'list'}
                                    >
                                        <List className="h-4 w-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className="flex gap-6">
                        {/* Desktop Sidebar Filters */}
                        <aside className="hidden w-64 flex-shrink-0 lg:block">
                            <Card className="p-6">
                                <div className="space-y-6">
                                    {/* Categories Filter */}
                                    <div>
                                        <h3 className="mb-3 font-semibold">Categories</h3>
                                        <div className="space-y-2">
                                            <Button
                                                variant={filters.category === '' ? 'default' : 'ghost'}
                                                size="sm"
                                                className="w-full justify-start"
                                                onClick={() => handleFilterChange('category', '')}
                                            >
                                                All Categories
                                            </Button>
                                            {categories.map((category) => (
                                                <Button
                                                    key={category.id}
                                                    variant={filters.category === category.id.toString() ? 'default' : 'ghost'}
                                                    size="sm"
                                                    className="w-full justify-start"
                                                    onClick={() => handleFilterChange('category', category.id.toString())}
                                                >
                                                    {category.name}
                                                </Button>
                                            ))}
                                        </div>
                                    </div>

                                    <Separator />

                                    {/* Price Range Filter */}
                                    <div>
                                        <h3 className="mb-3 font-semibold">Price Range</h3>
                                        <div className="space-y-3">
                                            <div>
                                                <label className="text-sm text-muted-foreground">Min Price</label>
                                                <Input
                                                    type="number"
                                                    placeholder="₱0"
                                                    value={filters.minPrice}
                                                    onChange={(e) => handleFilterChange('minPrice', e.target.value)}
                                                    className="mt-1"
                                                />
                                            </div>
                                            <div>
                                                <label className="text-sm text-muted-foreground">Max Price</label>
                                                <Input
                                                    type="number"
                                                    placeholder="₱1000"
                                                    value={filters.maxPrice}
                                                    onChange={(e) => handleFilterChange('maxPrice', e.target.value)}
                                                    className="mt-1"
                                                />
                                            </div>
                                        </div>
                                    </div>

                                    <Separator />

                                    {/* Quick Filters */}
                                    <div>
                                        <h3 className="mb-3 font-semibold">Quick Filters</h3>
                                        <div className="space-y-2">
                                            <label className="flex cursor-pointer items-center space-x-2">
                                                <input
                                                    type="checkbox"
                                                    checked={filters.inStock}
                                                    onChange={(e) => handleFilterChange('inStock', e.target.checked)}
                                                    className="rounded border-gray-300"
                                                />
                                                <span className="text-sm">In Stock Only</span>
                                            </label>
                                            <label className="flex cursor-pointer items-center space-x-2">
                                                <input
                                                    type="checkbox"
                                                    checked={filters.featured}
                                                    onChange={(e) => handleFilterChange('featured', e.target.checked)}
                                                    className="rounded border-gray-300"
                                                />
                                                <span className="text-sm">Featured Products</span>
                                            </label>
                                        </div>
                                    </div>

                                    {/* Clear Filters */}
                                    {hasActiveFilters && (
                                        <>
                                            <Separator />
                                            <Button variant="outline" size="sm" onClick={clearFilters} className="w-full">
                                                Clear All Filters
                                            </Button>
                                        </>
                                    )}
                                </div>
                            </Card>
                        </aside>

                        {/* Products Grid/List */}
                        <div className="flex-1">
                            {/* Results Header */}
                            <div className="mb-6 flex items-center justify-between">
                                <div className="text-sm text-muted-foreground">
                                    {loading ? 'Loading...' : `Showing ${products.length} of ${totalProducts} products`}
                                </div>

                                {hasActiveFilters && (
                                    <Button variant="outline" size="sm" onClick={clearFilters}>
                                        Clear Filters
                                    </Button>
                                )}
                            </div>

                            {/* Error State */}
                            {error && (
                                <Card className="p-6 text-center">
                                    <p className="mb-4 text-destructive">{error}</p>
                                    <Button onClick={fetchProducts}>Try Again</Button>
                                </Card>
                            )}

                            {/* Loading State */}
                            {loading && (
                                <div
                                    className={
                                        viewMode === 'grid' ? 'grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4' : 'space-y-4'
                                    }
                                >
                                    {Array.from({ length: 8 }).map((_, i) => (
                                        <Card key={i} className="overflow-hidden">
                                            <Skeleton className="aspect-square" />
                                            <CardContent className="p-4">
                                                <Skeleton className="mb-2 h-4 w-3/4" />
                                                <Skeleton className="mb-2 h-4 w-1/2" />
                                                <Skeleton className="h-8 w-full" />
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            )}

                            {/* Products Display */}
                            {!loading && !error && products.length > 0 && (
                                <>
                                    {viewMode === 'grid' ? (
                                        <ProductGrid
                                            products={products}
                                            onAddToCart={handleAddToCart}
                                            onQuickView={handleQuickView}
                                            onAddToWishlist={handleAddToWishlist}
                                        />
                                    ) : (
                                        <ProductList
                                            products={products}
                                            onAddToCart={handleAddToCart}
                                            onQuickView={handleQuickView}
                                            onAddToWishlist={handleAddToWishlist}
                                        />
                                    )}
                                </>
                            )}

                            {/* Empty State */}
                            {!loading && !error && products.length === 0 && (
                                <Card className="p-12 text-center">
                                    <div className="mb-4 text-6xl">🔍</div>
                                    <h3 className="mb-2 text-lg font-semibold">No products found</h3>
                                    <p className="mb-4 text-muted-foreground">
                                        Try adjusting your search or filters to find what you're looking for.
                                    </p>
                                    {hasActiveFilters && <Button onClick={clearFilters}>Clear Filters</Button>}
                                </Card>
                            )}

                            {/* Pagination */}
                            {!loading && !error && totalPages > 1 && (
                                <div className="mt-8">
                                    <div className="flex flex-col items-center justify-between gap-4 sm:flex-row">
                                        {/* Page Info */}
                                        <div className="text-sm text-muted-foreground">
                                            Page {currentPage} of {totalPages} ({totalProducts} total products)
                                        </div>

                                        {/* Pagination Controls */}
                                        <div className="flex items-center gap-2">
                                            <Button variant="outline" size="sm" onClick={() => handlePageChange(1)} disabled={currentPage === 1}>
                                                First
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handlePageChange(currentPage - 1)}
                                                disabled={currentPage === 1}
                                            >
                                                Previous
                                            </Button>

                                            {/* Page Numbers */}
                                            {(() => {
                                                const maxVisiblePages = 5;
                                                const halfVisible = Math.floor(maxVisiblePages / 2);
                                                let startPage = Math.max(1, currentPage - halfVisible);
                                                const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

                                                // Adjust start page if we're near the end
                                                if (endPage - startPage < maxVisiblePages - 1) {
                                                    startPage = Math.max(1, endPage - maxVisiblePages + 1);
                                                }

                                                const pages = [];
                                                for (let i = startPage; i <= endPage; i++) {
                                                    pages.push(i);
                                                }

                                                return pages.map((page) => (
                                                    <Button
                                                        key={page}
                                                        variant={currentPage === page ? 'default' : 'outline'}
                                                        size="sm"
                                                        onClick={() => handlePageChange(page)}
                                                    >
                                                        {page}
                                                    </Button>
                                                ));
                                            })()}

                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handlePageChange(currentPage + 1)}
                                                disabled={currentPage === totalPages}
                                            >
                                                Next
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handlePageChange(totalPages)}
                                                disabled={currentPage === totalPages}
                                            >
                                                Last
                                            </Button>
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </main>

                {/* Footer */}
                <Footer />
            </div>

            {/* Quick View Modal */}
            <ProductQuickView product={quickViewProduct} isOpen={isQuickViewOpen} onClose={handleCloseQuickView} onAddToCart={handleAddToCart} />
        </>
    );
}

// Main Products component that wraps ProductsContent with CartProvider
export default function Products() {
    return (
        <CartProviderWrapper>
            <ProductsContent />
        </CartProviderWrapper>
    );
}
