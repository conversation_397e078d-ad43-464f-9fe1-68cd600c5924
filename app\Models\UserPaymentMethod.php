<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Crypt;

class UserPaymentMethod extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'type',
        'label',
        'card_last_four',
        'card_brand',
        'card_holder_name',
        'expiry_month',
        'expiry_year',
        'mobile_number',
        'account_name',
        'bank_name',
        'account_number_last_four',
        'account_holder_name',
        'paypal_email',
        'encrypted_data',
        'provider_token',
        'is_default',
        'is_verified',
        'verified_at',
    ];

    protected $casts = [
        'encrypted_data' => 'array',
        'is_default' => 'boolean',
        'is_verified' => 'boolean',
        'verified_at' => 'datetime',
    ];

    protected $hidden = [
        'encrypted_data',
        'provider_token',
    ];

    /**
     * Get the user that owns the payment method.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the display name for the payment method.
     */
    public function getDisplayNameAttribute(): string
    {
        switch ($this->type) {
            case 'credit_card':
                return ($this->card_brand ? ucfirst($this->card_brand) : 'Card') . ' ending in ' . $this->card_last_four;
            case 'gcash':
                return 'GCash - ' . $this->getMaskedMobileNumber();
            case 'paymaya':
                return 'PayMaya - ' . $this->getMaskedMobileNumber();
            case 'bpi_online':
                return 'BPI Online - ' . $this->account_number_last_four;
            case 'bdo_online':
                return 'BDO Online - ' . $this->account_number_last_four;
            case 'paypal':
                return 'PayPal - ' . $this->getMaskedEmail();
            default:
                return ucfirst(str_replace('_', ' ', $this->type));
        }
    }

    /**
     * Get masked mobile number for display.
     */
    private function getMaskedMobileNumber(): string
    {
        if (!$this->mobile_number) {
            return '****';
        }

        $number = preg_replace('/\D/', '', $this->mobile_number);
        if (strlen($number) >= 4) {
            return str_repeat('*', strlen($number) - 4) . substr($number, -4);
        }

        return str_repeat('*', strlen($number));
    }

    /**
     * Get masked email for display.
     */
    private function getMaskedEmail(): string
    {
        if (!$this->paypal_email) {
            return '****@****.com';
        }

        $parts = explode('@', $this->paypal_email);
        if (count($parts) !== 2) {
            return '****@****.com';
        }

        $username = $parts[0];
        $domain = $parts[1];

        $maskedUsername = strlen($username) > 2 
            ? substr($username, 0, 2) . str_repeat('*', strlen($username) - 2)
            : str_repeat('*', strlen($username));

        return $maskedUsername . '@' . $domain;
    }

    /**
     * Encrypt sensitive data.
     */
    public function setEncryptedDataAttribute($value)
    {
        $this->attributes['encrypted_data'] = $value ? Crypt::encryptString(json_encode($value)) : null;
    }

    /**
     * Decrypt sensitive data.
     */
    public function getEncryptedDataAttribute($value)
    {
        return $value ? json_decode(Crypt::decryptString($value), true) : null;
    }

    /**
     * Scope to get payment methods by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get default payment methods.
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope to get verified payment methods.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Ensure only one default payment method per user
        static::saving(function ($paymentMethod) {
            if ($paymentMethod->is_default) {
                static::where('user_id', $paymentMethod->user_id)
                    ->where('id', '!=', $paymentMethod->id)
                    ->update(['is_default' => false]);
            }
        });
    }
}
