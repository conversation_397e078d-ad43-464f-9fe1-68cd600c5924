import { Head, Link, usePage } from '@inertiajs/react';
import { CheckCircle, Package, MapPin, CreditCard, Download, Mail, ArrowRight } from 'lucide-react';
import React from 'react';

// Import customer components
import Footer from '@/components/customer/Footer';
import Header from '@/components/customer/Header';
import ProductImage from '@/components/customer/ProductImage';
import { CartProviderWrapper } from '@/components/CartProviderWrapper';

// Import UI components
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

import { type SharedData } from '@/types';
import { type Order } from '@/types/ecommerce';

interface OrderConfirmationPageProps {
    order: Order;
}

interface OrderConfirmationContentProps {
    order: Order;
}

const OrderConfirmationContent: React.FC<OrderConfirmationContentProps> = ({ order }) => {
    const { auth } = usePage<SharedData>().props;

    const getStatusBadgeVariant = (status: string) => {
        switch (status) {
            case 'pending':
                return 'secondary';
            case 'processing':
                return 'default';
            case 'shipped':
                return 'default';
            case 'delivered':
                return 'default';
            case 'cancelled':
                return 'destructive';
            default:
                return 'secondary';
        }
    };

    const getPaymentStatusBadgeVariant = (status: string) => {
        switch (status) {
            case 'paid':
                return 'default';
            case 'pending':
                return 'secondary';
            case 'failed':
                return 'destructive';
            default:
                return 'secondary';
        }
    };

    const formatAddress = (order: Order, type: 'shipping' | 'billing') => {
        const prefix = type === 'shipping' ? 'shipping_' : 'billing_';
        const parts = [
            order[`${prefix}address_line_1` as keyof Order],
            order[`${prefix}address_line_2` as keyof Order],
            order[`${prefix}city` as keyof Order],
            order[`${prefix}state` as keyof Order],
            order[`${prefix}postal_code` as keyof Order],
            order[`${prefix}country` as keyof Order]
        ].filter(Boolean);
        
        return parts.join(', ');
    };

    return (
        <div className="min-h-screen bg-background">
            <Header />
            
            <div className="container mx-auto px-4 py-8">
                {/* Success Header */}
                <div className="text-center mb-8">
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full mb-4">
                        <CheckCircle className="h-8 w-8 text-green-600" />
                    </div>
                    <h1 className="text-3xl font-bold mb-2">Order Confirmed!</h1>
                    <p className="text-muted-foreground text-lg">
                        Thank you for your order. We'll send you a confirmation email shortly.
                    </p>
                </div>

                {/* Order Details */}
                <div className="max-w-4xl mx-auto space-y-6">
                    {/* Order Summary Card */}
                    <Card>
                        <CardHeader>
                            <div className="flex items-center justify-between">
                                <div>
                                    <CardTitle className="text-xl">Order #{order.order_number}</CardTitle>
                                    <p className="text-muted-foreground">
                                        Placed on {new Date(order.created_at).toLocaleDateString('en-PH', {
                                            year: 'numeric',
                                            month: 'long',
                                            day: 'numeric',
                                            hour: '2-digit',
                                            minute: '2-digit'
                                        })}
                                    </p>
                                </div>
                                <div className="text-right">
                                    <Badge variant={getStatusBadgeVariant(order.status)} className="mb-2">
                                        {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
                                    </Badge>
                                    <br />
                                    <Badge variant={getPaymentStatusBadgeVariant(order.payment_status)}>
                                        Payment {order.payment_status.charAt(0).toUpperCase() + order.payment_status.slice(1)}
                                    </Badge>
                                </div>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="text-center">
                                    <div className="text-2xl font-bold text-primary">₱{order.total_amount.toLocaleString()}</div>
                                    <p className="text-sm text-muted-foreground">Total Amount</p>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold">{order.orderItems?.length || 0}</div>
                                    <p className="text-sm text-muted-foreground">Items</p>
                                </div>
                                <div className="text-center">
                                    <div className="text-2xl font-bold">{order.payment_method.toUpperCase()}</div>
                                    <p className="text-sm text-muted-foreground">Payment Method</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Quick Actions */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <Link href={`/orders/${order.id}`}>
                            <Button variant="outline" className="w-full h-12">
                                <Package className="h-4 w-4 mr-2" />
                                Track Order
                            </Button>
                        </Link>
                        <Button variant="outline" className="w-full h-12">
                            <Download className="h-4 w-4 mr-2" />
                            Download Receipt
                        </Button>
                        <Button variant="outline" className="w-full h-12">
                            <Mail className="h-4 w-4 mr-2" />
                            Email Receipt
                        </Button>
                    </div>

                    {/* Order Items */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Package className="h-5 w-5" />
                                Order Items
                            </CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            {order.orderItems?.map((item) => (
                                <div key={item.id} className="flex items-center gap-4 p-4 border rounded-lg">
                                    <div className="flex-shrink-0">
                                        <ProductImage
                                            src={item.product?.images?.[0] || ''}
                                            alt={item.product_name}
                                            className="w-16 h-16 rounded-lg object-cover"
                                        />
                                    </div>
                                    <div className="flex-1 min-w-0">
                                        <h4 className="font-medium truncate">{item.product_name}</h4>
                                        <p className="text-sm text-muted-foreground">SKU: {item.product_sku}</p>
                                        <p className="text-sm text-muted-foreground">Quantity: {item.quantity}</p>
                                        {item.product_options && Object.keys(item.product_options).length > 0 && (
                                            <p className="text-sm text-muted-foreground">
                                                Options: {Object.entries(item.product_options).map(([key, value]) => 
                                                    `${key}: ${value}`
                                                ).join(', ')}
                                            </p>
                                        )}
                                    </div>
                                    <div className="text-right">
                                        <p className="font-medium">₱{item.total_price.toLocaleString()}</p>
                                        <p className="text-sm text-muted-foreground">₱{item.unit_price.toLocaleString()} each</p>
                                    </div>
                                </div>
                            ))}
                        </CardContent>
                    </Card>

                    {/* Delivery Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <MapPin className="h-5 w-5" />
                                    Delivery Address
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-2">
                                    <p className="font-medium">
                                        {order.shipping_first_name} {order.shipping_last_name}
                                    </p>
                                    {order.shipping_company && (
                                        <p className="text-sm text-muted-foreground">{order.shipping_company}</p>
                                    )}
                                    <p className="text-sm">{formatAddress(order, 'shipping')}</p>
                                    {order.shipping_method && (
                                        <div className="mt-3 pt-3 border-t">
                                            <p className="text-sm font-medium">Shipping Method</p>
                                            <p className="text-sm text-muted-foreground">{order.shipping_method}</p>
                                        </div>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle className="flex items-center gap-2">
                                    <CreditCard className="h-5 w-5" />
                                    Payment Information
                                </CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="space-y-3">
                                    <div>
                                        <p className="text-sm font-medium">Payment Method</p>
                                        <p className="text-sm text-muted-foreground">
                                            {order.payment_method.replace('_', ' ').toUpperCase()}
                                        </p>
                                    </div>
                                    {order.payment_reference && (
                                        <div>
                                            <p className="text-sm font-medium">Reference Number</p>
                                            <p className="text-sm text-muted-foreground font-mono">
                                                {order.payment_reference}
                                            </p>
                                        </div>
                                    )}
                                    <div>
                                        <p className="text-sm font-medium">Billing Address</p>
                                        <p className="text-sm text-muted-foreground">
                                            {order.billing_first_name} {order.billing_last_name}
                                        </p>
                                        <p className="text-sm text-muted-foreground">
                                            {formatAddress(order, 'billing')}
                                        </p>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>

                    {/* Order Total Breakdown */}
                    <Card>
                        <CardHeader>
                            <CardTitle>Order Summary</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                            <div className="flex justify-between">
                                <span>Subtotal</span>
                                <span>₱{order.subtotal.toLocaleString()}</span>
                            </div>
                            
                            <div className="flex justify-between">
                                <span>Shipping</span>
                                <span>
                                    {order.shipping_amount === 0 ? (
                                        <span className="text-green-600">Free</span>
                                    ) : (
                                        `₱${order.shipping_amount.toLocaleString()}`
                                    )}
                                </span>
                            </div>
                            
                            <div className="flex justify-between">
                                <span>Tax (VAT)</span>
                                <span>₱{order.tax_amount.toLocaleString()}</span>
                            </div>
                            
                            {order.discount_amount > 0 && (
                                <div className="flex justify-between text-green-600">
                                    <span>Discount</span>
                                    <span>-₱{order.discount_amount.toLocaleString()}</span>
                                </div>
                            )}
                            
                            <Separator />
                            
                            <div className="flex justify-between text-lg font-bold">
                                <span>Total</span>
                                <span>₱{order.total_amount.toLocaleString()}</span>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Next Steps */}
                    <Card>
                        <CardHeader>
                            <CardTitle>What's Next?</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-3">
                                <div className="flex items-start gap-3">
                                    <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-medium">
                                        1
                                    </div>
                                    <div>
                                        <p className="font-medium">Order Confirmation</p>
                                        <p className="text-sm text-muted-foreground">
                                            You'll receive an email confirmation with your order details
                                        </p>
                                    </div>
                                </div>
                                
                                <div className="flex items-start gap-3">
                                    <div className="w-6 h-6 bg-muted text-muted-foreground rounded-full flex items-center justify-center text-sm font-medium">
                                        2
                                    </div>
                                    <div>
                                        <p className="font-medium">Order Processing</p>
                                        <p className="text-sm text-muted-foreground">
                                            We'll prepare your items for shipment (1-2 business days)
                                        </p>
                                    </div>
                                </div>
                                
                                <div className="flex items-start gap-3">
                                    <div className="w-6 h-6 bg-muted text-muted-foreground rounded-full flex items-center justify-center text-sm font-medium">
                                        3
                                    </div>
                                    <div>
                                        <p className="font-medium">Shipping & Delivery</p>
                                        <p className="text-sm text-muted-foreground">
                                            Your order will be shipped and delivered to your address
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div className="flex gap-4 pt-4">
                                <Link href="/products" className="flex-1">
                                    <Button variant="outline" className="w-full">
                                        Continue Shopping
                                    </Button>
                                </Link>
                                {auth?.user && (
                                    <Link href="/account/orders" className="flex-1">
                                        <Button className="w-full">
                                            View All Orders
                                            <ArrowRight className="h-4 w-4 ml-2" />
                                        </Button>
                                    </Link>
                                )}
                            </div>
                        </CardContent>
                    </Card>
                </div>
            </div>
            
            <Footer />
        </div>
    );
};

export default function OrderConfirmationPage({ order }: OrderConfirmationPageProps) {
    return (
        <>
            <Head title={`Order Confirmation - ${order.order_number}`} />
            <CartProviderWrapper>
                <OrderConfirmationContent order={order} />
            </CartProviderWrapper>
        </>
    );
}
