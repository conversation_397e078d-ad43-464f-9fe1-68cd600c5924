import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { ArrowLeft, Minus, Plus, ShoppingCart, Trash2, Tag, AlertCircle } from 'lucide-react';
import React, { useState, useCallback } from 'react';

// Import customer components
import Footer from '@/components/customer/Footer';
import Header from '@/components/customer/Header';
import ProductImage from '@/components/customer/ProductImage';
import { CartProviderWrapper } from '@/components/CartProviderWrapper';
import { useCart } from '@/hooks/use-cart';

// Import UI components

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';


import { type CartItem } from '@/types/ecommerce';

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
interface CartPageContentProps {}

const CartPageContent: React.FC<CartPageContentProps> = () => {
    const { cart, loading, error, updateCartItemOptimistic, removeFromCartOptimistic, clearCart } = useCart();
    
    const [couponCode, setCouponCode] = useState('');
    const [couponLoading, setCouponLoading] = useState(false);
    const [couponError, setCouponError] = useState<string | null>(null);
    const [appliedCoupon, setAppliedCoupon] = useState<{ code: string; discount: number } | null>(null);

    const handleQuantityChange = useCallback(async (itemId: number | string, newQuantity: number) => {
        if (newQuantity < 1) {
            await removeFromCartOptimistic(itemId);
        } else {
            await updateCartItemOptimistic(itemId, newQuantity);
        }
    }, [updateCartItemOptimistic, removeFromCartOptimistic]);

    const handleRemoveItem = useCallback(async (itemId: number | string) => {
        await removeFromCartOptimistic(itemId);
    }, [removeFromCartOptimistic]);

    const handleClearCart = useCallback(async () => {
        await clearCart();
    }, [clearCart]);

    // Calculate values first to avoid hoisting issues
    const cartItems = cart?.items || [];
    const totalItems = cart?.total_items || 0;
    const subtotal = cart?.subtotal || 0;

    const handleApplyCoupon = useCallback(async () => {
        if (!couponCode.trim()) return;

        setCouponLoading(true);
        setCouponError(null);

        try {
            // TODO: Implement coupon validation API call
            // For now, simulate a coupon validation
            await new Promise(resolve => setTimeout(resolve, 1000));

            if (couponCode.toLowerCase() === 'save10') {
                setAppliedCoupon({ code: couponCode, discount: subtotal * 0.1 });
                setCouponCode('');
            } else {
                setCouponError('Invalid coupon code');
            }
        } catch {
            setCouponError('Failed to apply coupon');
        } finally {
            setCouponLoading(false);
        }
    }, [couponCode, subtotal]);

    const handleRemoveCoupon = useCallback(() => {
        setAppliedCoupon(null);
        setCouponError(null);
    }, []);
    const discountAmount = appliedCoupon?.discount || 0;
    const taxAmount = (subtotal - discountAmount) * 0.12; // 12% VAT for Philippines
    const shippingAmount = subtotal > 1000 ? 0 : 150; // Free shipping over ₱1000
    const totalAmount = subtotal - discountAmount + taxAmount + shippingAmount;

    if (loading && !cart) {
        return (
            <div className="min-h-screen bg-background">
                <Header />
                <div className="container mx-auto px-4 py-8">
                    <div className="flex items-center justify-center min-h-[400px]">
                        <div className="text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                            <p className="text-muted-foreground">Loading your cart...</p>
                        </div>
                    </div>
                </div>
                <Footer />
            </div>
        );
    }

    return (
        <div className="min-h-screen bg-background">
            <Header />
            
            <div className="container mx-auto px-4 py-8">
                {/* Breadcrumb */}
                <div className="flex items-center gap-2 text-sm text-muted-foreground mb-6">
                    <Link href="/" className="hover:text-foreground transition-colors">
                        Home
                    </Link>
                    <span>/</span>
                    <span className="text-foreground font-medium">Shopping Cart</span>
                </div>

                {/* Page Header */}
                <div className="flex items-center justify-between mb-8">
                    <div className="flex items-center gap-3">
                        <ShoppingCart className="h-8 w-8 text-primary" />
                        <div>
                            <h1 className="text-3xl font-bold">Shopping Cart</h1>
                            <p className="text-muted-foreground">
                                {totalItems > 0 ? `${totalItems} ${totalItems === 1 ? 'item' : 'items'} in your cart` : 'Your cart is empty'}
                            </p>
                        </div>
                    </div>
                    
                    <Link href="/products">
                        <Button variant="outline" className="flex items-center gap-2">
                            <ArrowLeft className="h-4 w-4" />
                            Continue Shopping
                        </Button>
                    </Link>
                </div>

                {error && (
                    <Alert className="mb-6">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{error}</AlertDescription>
                    </Alert>
                )}

                {cartItems.length === 0 ? (
                    <div className="text-center py-16">
                        <ShoppingCart className="h-24 w-24 text-muted-foreground/50 mx-auto mb-6" />
                        <h2 className="text-2xl font-semibold mb-4">Your cart is empty</h2>
                        <p className="text-muted-foreground mb-8 max-w-md mx-auto">
                            Looks like you haven't added any items to your cart yet. Start shopping to fill it up!
                        </p>
                        <Link href="/products">
                            <Button size="lg" className="px-8">
                                Start Shopping
                            </Button>
                        </Link>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        {/* Cart Items */}
                        <div className="lg:col-span-2 space-y-4">
                            <div className="flex items-center justify-between">
                                <h2 className="text-xl font-semibold">Cart Items</h2>
                                <Button 
                                    variant="outline" 
                                    size="sm" 
                                    onClick={handleClearCart}
                                    disabled={loading}
                                    className="text-destructive hover:text-destructive"
                                >
                                    Clear Cart
                                </Button>
                            </div>
                            
                            <div className="space-y-4">
                                {cartItems.map((item: CartItem) => (
                                    <Card key={item.id} className="p-6">
                                        <div className="flex gap-4">
                                            <div className="flex-shrink-0">
                                                <ProductImage
                                                    src={item.product.images[0]}
                                                    alt={item.product.name}
                                                    className="w-20 h-20 rounded-lg object-cover"
                                                />
                                            </div>
                                            
                                            <div className="flex-1 min-w-0">
                                                <h3 className="font-semibold text-lg mb-1 truncate">
                                                    {item.product.name}
                                                </h3>
                                                <p className="text-sm text-muted-foreground mb-2">
                                                    SKU: {item.product.sku}
                                                </p>
                                                <p className="text-lg font-bold text-primary">
                                                    ₱{item.product.price.toLocaleString()}
                                                </p>
                                            </div>
                                            
                                            <div className="flex flex-col items-end gap-3">
                                                <Button
                                                    variant="ghost"
                                                    size="sm"
                                                    onClick={() => handleRemoveItem(item.id)}
                                                    disabled={loading}
                                                    className="text-destructive hover:text-destructive"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                                
                                                <div className="flex items-center gap-2">
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                                                        disabled={loading || item.quantity <= 1}
                                                        className="h-8 w-8 p-0"
                                                    >
                                                        <Minus className="h-3 w-3" />
                                                    </Button>
                                                    
                                                    <span className="w-12 text-center font-medium">
                                                        {item.quantity}
                                                    </span>
                                                    
                                                    <Button
                                                        variant="outline"
                                                        size="sm"
                                                        onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                                                        disabled={loading || item.quantity >= item.product.stock_quantity}
                                                        className="h-8 w-8 p-0"
                                                    >
                                                        <Plus className="h-3 w-3" />
                                                    </Button>
                                                </div>
                                                
                                                <p className="text-lg font-bold">
                                                    ₱{(item.product.price * item.quantity).toLocaleString()}
                                                </p>
                                            </div>
                                        </div>
                                    </Card>
                                ))}
                            </div>
                        </div>

                        {/* Order Summary */}
                        <div className="space-y-6">
                            {/* Coupon Section */}
                            <Card>
                                <CardHeader>
                                    <CardTitle className="flex items-center gap-2">
                                        <Tag className="h-5 w-5" />
                                        Promo Code
                                    </CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {appliedCoupon ? (
                                        <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-950/20 rounded-lg border border-green-200 dark:border-green-800">
                                            <div>
                                                <p className="font-medium text-green-800 dark:text-green-200">
                                                    {appliedCoupon.code}
                                                </p>
                                                <p className="text-sm text-green-600 dark:text-green-400">
                                                    -₱{appliedCoupon.discount.toLocaleString()}
                                                </p>
                                            </div>
                                            <Button
                                                variant="ghost"
                                                size="sm"
                                                onClick={handleRemoveCoupon}
                                                className="text-green-600 hover:text-green-700"
                                            >
                                                Remove
                                            </Button>
                                        </div>
                                    ) : (
                                        <div className="space-y-3">
                                            <div className="flex gap-2">
                                                <Input
                                                    placeholder="Enter promo code"
                                                    value={couponCode}
                                                    onChange={(e) => setCouponCode(e.target.value)}
                                                    disabled={couponLoading}
                                                />
                                                <Button
                                                    onClick={handleApplyCoupon}
                                                    disabled={couponLoading || !couponCode.trim()}
                                                    className="px-6"
                                                >
                                                    {couponLoading ? 'Applying...' : 'Apply'}
                                                </Button>
                                            </div>
                                            {couponError && (
                                                <p className="text-sm text-destructive">{couponError}</p>
                                            )}
                                        </div>
                                    )}
                                </CardContent>
                            </Card>

                            {/* Order Summary */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Order Summary</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-3">
                                        <div className="flex justify-between">
                                            <span>Subtotal ({totalItems} items)</span>
                                            <span>₱{subtotal.toLocaleString()}</span>
                                        </div>
                                        
                                        {discountAmount > 0 && (
                                            <div className="flex justify-between text-green-600">
                                                <span>Discount</span>
                                                <span>-₱{discountAmount.toLocaleString()}</span>
                                            </div>
                                        )}
                                        
                                        <div className="flex justify-between">
                                            <span>Shipping</span>
                                            <span>
                                                {shippingAmount === 0 ? (
                                                    <span className="text-green-600">Free</span>
                                                ) : (
                                                    `₱${shippingAmount.toLocaleString()}`
                                                )}
                                            </span>
                                        </div>
                                        
                                        <div className="flex justify-between">
                                            <span>Tax (VAT 12%)</span>
                                            <span>₱{taxAmount.toLocaleString()}</span>
                                        </div>
                                    </div>
                                    
                                    <Separator />
                                    
                                    <div className="flex justify-between text-lg font-bold">
                                        <span>Total</span>
                                        <span>₱{totalAmount.toLocaleString()}</span>
                                    </div>
                                    
                                    <div className="space-y-3 pt-4">
                                        <Link href="/checkout">
                                            <Button size="lg" className="w-full">
                                                Proceed to Checkout
                                            </Button>
                                        </Link>
                                        
                                        <Link href="/products">
                                            <Button variant="outline" size="lg" className="w-full">
                                                Continue Shopping
                                            </Button>
                                        </Link>
                                    </div>
                                    
                                    {shippingAmount > 0 && (
                                        <p className="text-sm text-muted-foreground text-center">
                                            Add ₱{(1000 - subtotal).toLocaleString()} more for free shipping
                                        </p>
                                    )}
                                </CardContent>
                            </Card>
                        </div>
                    </div>
                )}
            </div>
            
            <Footer />
        </div>
    );
};

export default function CartPage() {
    return (
        <>
            <Head title="Shopping Cart" />
            <CartProviderWrapper>
                <CartPageContent />
            </CartProviderWrapper>
        </>
    );
}
