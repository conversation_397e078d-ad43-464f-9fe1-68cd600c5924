import { Save, LoaderCircle } from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { apiClient } from '@/bootstrap';

import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';

interface UserPreferences {
    email_marketing: boolean;
    email_order_updates: boolean;
    email_promotions: boolean;
    sms_marketing: boolean;
    sms_order_updates: boolean;
    push_notifications: boolean;
    language: string;
    currency: string;
    timezone: string;
    preferred_delivery_time: string | null;
    profile_public: boolean;
    show_order_history: boolean;
    allow_reviews_display: boolean;
}

export default function PreferencesSection() {
    const [preferences, setPreferences] = useState<UserPreferences | null>(null);
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const fetchPreferences = async () => {
        try {
            setLoading(true);
            const response = await apiClient.get('/preferences');
            if (response.data.success) {
                setPreferences(response.data.data);
            } else {
                setError('Failed to load preferences');
            }
        } catch (err) {
            setError('Failed to load preferences');
            console.error('Error fetching preferences:', err);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchPreferences();
    }, []);

    const handleSave = async () => {
        if (!preferences) return;

        try {
            setSaving(true);
            const response = await apiClient.put('/preferences', preferences);
            if (response.data.success) {
                // Success feedback could be added here
            }
        } catch (err) {
            console.error('Error saving preferences:', err);
            alert('Failed to save preferences');
        } finally {
            setSaving(false);
        }
    };

    const updatePreference = (key: keyof UserPreferences, value: boolean | string | null) => {
        if (!preferences) return;
        setPreferences({ ...preferences, [key]: value });
    };

    if (loading) {
        return (
            <Card>
                <CardContent className="p-6">
                    <div className="flex items-center justify-center">
                        <LoaderCircle className="w-6 h-6 animate-spin mr-2" />
                        Loading preferences...
                    </div>
                </CardContent>
            </Card>
        );
    }

    if (error || !preferences) {
        return (
            <Card>
                <CardContent className="p-6">
                    <div className="text-center text-red-600">
                        <p>{error || 'Failed to load preferences'}</p>
                        <Button onClick={fetchPreferences} className="mt-2">
                            Try Again
                        </Button>
                    </div>
                </CardContent>
            </Card>
        );
    }

    return (
        <div className="space-y-6">
            {/* Communication Preferences */}
            <Card>
                <CardHeader>
                    <CardTitle>Communication Preferences</CardTitle>
                    <p className="text-sm text-muted-foreground">
                        Choose how you want to receive updates and notifications
                    </p>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="email_order_updates"
                                checked={preferences.email_order_updates}
                                onCheckedChange={(checked) => updatePreference('email_order_updates', checked)}
                            />
                            <Label htmlFor="email_order_updates">Email order updates</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="email_marketing"
                                checked={preferences.email_marketing}
                                onCheckedChange={(checked) => updatePreference('email_marketing', checked)}
                            />
                            <Label htmlFor="email_marketing">Email marketing and newsletters</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="email_promotions"
                                checked={preferences.email_promotions}
                                onCheckedChange={(checked) => updatePreference('email_promotions', checked)}
                            />
                            <Label htmlFor="email_promotions">Email promotions and special offers</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="sms_order_updates"
                                checked={preferences.sms_order_updates}
                                onCheckedChange={(checked) => updatePreference('sms_order_updates', checked)}
                            />
                            <Label htmlFor="sms_order_updates">SMS order updates</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="sms_marketing"
                                checked={preferences.sms_marketing}
                                onCheckedChange={(checked) => updatePreference('sms_marketing', checked)}
                            />
                            <Label htmlFor="sms_marketing">SMS marketing messages</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="push_notifications"
                                checked={preferences.push_notifications}
                                onCheckedChange={(checked) => updatePreference('push_notifications', checked)}
                            />
                            <Label htmlFor="push_notifications">Push notifications</Label>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Language & Region */}
            <Card>
                <CardHeader>
                    <CardTitle>Language & Region</CardTitle>
                    <p className="text-sm text-muted-foreground">
                        Set your language and regional preferences
                    </p>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                            <Label>Language</Label>
                            <Select
                                value={preferences.language}
                                onValueChange={(value) => updatePreference('language', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="en">English</SelectItem>
                                    <SelectItem value="tl">Filipino</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <Label>Currency</Label>
                            <Select
                                value={preferences.currency}
                                onValueChange={(value) => updatePreference('currency', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="PHP">Philippine Peso (₱)</SelectItem>
                                    <SelectItem value="USD">US Dollar ($)</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                        <div className="space-y-2">
                            <Label>Timezone</Label>
                            <Select
                                value={preferences.timezone}
                                onValueChange={(value) => updatePreference('timezone', value)}
                            >
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="Asia/Manila">Manila (GMT+8)</SelectItem>
                                    <SelectItem value="UTC">UTC (GMT+0)</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Shopping Preferences */}
            <Card>
                <CardHeader>
                    <CardTitle>Shopping Preferences</CardTitle>
                    <p className="text-sm text-muted-foreground">
                        Customize your shopping experience
                    </p>
                </CardHeader>
                <CardContent>
                    <div className="space-y-4">
                        <div className="space-y-2">
                            <Label>Preferred Delivery Time</Label>
                            <Select
                                value={preferences.preferred_delivery_time || ''}
                                onValueChange={(value) => updatePreference('preferred_delivery_time', value || null)}
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select preferred delivery time" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="morning">Morning (8AM - 12PM)</SelectItem>
                                    <SelectItem value="afternoon">Afternoon (12PM - 5PM)</SelectItem>
                                    <SelectItem value="evening">Evening (5PM - 8PM)</SelectItem>
                                    <SelectItem value="anytime">Anytime</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Privacy Settings */}
            <Card>
                <CardHeader>
                    <CardTitle>Privacy Settings</CardTitle>
                    <p className="text-sm text-muted-foreground">
                        Control your privacy and data sharing preferences
                    </p>
                </CardHeader>
                <CardContent className="space-y-4">
                    <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="profile_public"
                                checked={preferences.profile_public}
                                onCheckedChange={(checked) => updatePreference('profile_public', checked)}
                            />
                            <Label htmlFor="profile_public">Make my profile public</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="show_order_history"
                                checked={preferences.show_order_history}
                                onCheckedChange={(checked) => updatePreference('show_order_history', checked)}
                            />
                            <Label htmlFor="show_order_history">Show my order history to others</Label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="allow_reviews_display"
                                checked={preferences.allow_reviews_display}
                                onCheckedChange={(checked) => updatePreference('allow_reviews_display', checked)}
                            />
                            <Label htmlFor="allow_reviews_display">Allow my reviews to be displayed publicly</Label>
                        </div>
                    </div>
                </CardContent>
            </Card>

            <div className="flex justify-end">
                <Button onClick={handleSave} disabled={saving}>
                    {saving ? (
                        <LoaderCircle className="w-4 h-4 mr-2 animate-spin" />
                    ) : (
                        <Save className="w-4 h-4 mr-2" />
                    )}
                    Save Preferences
                </Button>
            </div>
        </div>
    );
}
