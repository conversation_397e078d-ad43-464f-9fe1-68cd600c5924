<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Root categories - Philippine Grocery Store focused
        $rootCategories = [
            [
                'name' => 'Fresh Produce',
                'slug' => 'fresh-produce',
                'description' => 'Fresh fruits, vegetables, and herbs - locally sourced and imported varieties',
                'image' => '/images/categories/fresh-produce.jpg',
                'meta_title' => 'Fresh Produce - Fruits, Vegetables & Herbs',
                'meta_description' => 'Shop the freshest local and imported fruits, vegetables, and herbs delivered daily.',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Meat & Seafood',
                'slug' => 'meat-seafood',
                'description' => 'Fresh meat, poultry, and seafood - premium quality cuts and catch',
                'image' => '/images/categories/meat-seafood.jpg',
                'meta_title' => 'Meat & Seafood - Fresh Cuts & Catch',
                'meta_description' => 'Premium quality fresh meat, poultry, and seafood selections from trusted suppliers.',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Dairy & Eggs',
                'slug' => 'dairy-eggs',
                'description' => 'Fresh dairy products, eggs, and refrigerated items',
                'image' => '/images/categories/dairy-eggs.jpg',
                'meta_title' => 'Dairy & Eggs - Fresh Milk, Cheese & Eggs',
                'meta_description' => 'Premium dairy products, fresh eggs, and refrigerated essentials from local farms.',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Pantry Staples',
                'slug' => 'pantry-staples',
                'description' => 'Essential cooking ingredients, rice, canned goods, and pantry basics',
                'image' => '/images/categories/pantry-staples.jpg',
                'meta_title' => 'Pantry Staples - Rice, Canned Goods & Cooking Essentials',
                'meta_description' => 'Stock up on essential Filipino cooking ingredients, rice, canned goods, and pantry staples.',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name' => 'Beverages',
                'slug' => 'beverages',
                'description' => 'Refreshing drinks, juices, coffee, and beverages',
                'image' => '/images/categories/beverages.jpg',
                'meta_title' => 'Beverages - Drinks, Coffee & Refreshments',
                'meta_description' => 'Wide selection of beverages, local and imported drinks, coffee, and refreshments.',
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'name' => 'Snacks & Sweets',
                'slug' => 'snacks-sweets',
                'description' => 'Filipino snacks, imported treats, candies, and sweet delights',
                'image' => '/images/categories/snacks-sweets.jpg',
                'meta_title' => 'Snacks & Sweets - Filipino & Imported Treats',
                'meta_description' => 'Satisfy your cravings with local Filipino snacks and imported sweet treats.',
                'is_active' => true,
                'sort_order' => 6,
            ],
            [
                'name' => 'Personal Care',
                'slug' => 'personal-care',
                'description' => 'Health, beauty, hygiene, and personal care products',
                'image' => '/images/categories/personal-care.jpg',
                'meta_title' => 'Personal Care - Health, Beauty & Hygiene',
                'meta_description' => 'Essential personal care, health, beauty, and hygiene products for the whole family.',
                'is_active' => true,
                'sort_order' => 7,
            ],
            [
                'name' => 'Household Items',
                'slug' => 'household-items',
                'description' => 'Cleaning supplies, detergents, and household essentials',
                'image' => '/images/categories/household-items.jpg',
                'meta_title' => 'Household Items - Cleaning & Home Essentials',
                'meta_description' => 'Keep your home clean and organized with quality household and cleaning products.',
                'is_active' => true,
                'sort_order' => 8,
            ],
            [
                'name' => 'Instant & Ready-to-Eat',
                'slug' => 'instant-ready-to-eat',
                'description' => 'Instant noodles, ready meals, and convenient food options',
                'image' => '/images/categories/instant-ready-to-eat.jpg',
                'meta_title' => 'Instant & Ready-to-Eat - Quick & Convenient Meals',
                'meta_description' => 'Quick and convenient meal solutions including instant noodles and ready-to-eat options.',
                'is_active' => true,
                'sort_order' => 9,
            ],
        ];

        // Create root categories and store references
        $createdCategories = [];
        foreach ($rootCategories as $categoryData) {
            $category = Category::updateOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );
            $createdCategories[$categoryData['slug']] = $category;
        }

        // Fresh Produce subcategories
        $freshProduceSubcategories = [
            [
                'name' => 'Local Fruits',
                'slug' => 'local-fruits',
                'description' => 'Fresh local Filipino fruits like mango, banana, rambutan',
                'parent_slug' => 'fresh-produce',
                'sort_order' => 1,
            ],
            [
                'name' => 'Imported Fruits',
                'slug' => 'imported-fruits',
                'description' => 'Premium imported fruits like apples, grapes, oranges',
                'parent_slug' => 'fresh-produce',
                'sort_order' => 2,
            ],
            [
                'name' => 'Local Vegetables',
                'slug' => 'local-vegetables',
                'description' => 'Fresh local vegetables like kangkong, pechay, sitaw',
                'parent_slug' => 'fresh-produce',
                'sort_order' => 3,
            ],
            [
                'name' => 'Imported Vegetables',
                'slug' => 'imported-vegetables',
                'description' => 'Premium imported vegetables and specialty produce',
                'parent_slug' => 'fresh-produce',
                'sort_order' => 4,
            ],
        ];

        // Meat & Seafood subcategories
        $meatSeafoodSubcategories = [
            [
                'name' => 'Fresh Chicken',
                'slug' => 'fresh-chicken',
                'description' => 'Fresh chicken cuts, whole chicken, and poultry products',
                'parent_slug' => 'meat-seafood',
                'sort_order' => 1,
            ],
            [
                'name' => 'Pork',
                'slug' => 'pork',
                'description' => 'Fresh pork cuts, ground pork, and specialty cuts',
                'parent_slug' => 'meat-seafood',
                'sort_order' => 2,
            ],
            [
                'name' => 'Beef',
                'slug' => 'beef',
                'description' => 'Premium beef cuts and ground beef',
                'parent_slug' => 'meat-seafood',
                'sort_order' => 3,
            ],
            [
                'name' => 'Fresh Seafood',
                'slug' => 'fresh-seafood',
                'description' => 'Fresh fish, shellfish, and seafood from local waters',
                'parent_slug' => 'meat-seafood',
                'sort_order' => 4,
            ],
        ];

        // Pantry Staples subcategories
        $pantryStaplesSubcategories = [
            [
                'name' => 'Rice & Grains',
                'slug' => 'rice-grains',
                'description' => 'Premium rice varieties, quinoa, and grains',
                'parent_slug' => 'pantry-staples',
                'sort_order' => 1,
            ],
            [
                'name' => 'Canned Goods',
                'slug' => 'canned-goods',
                'description' => 'Canned fish, meat, vegetables, and preserved foods',
                'parent_slug' => 'pantry-staples',
                'sort_order' => 2,
            ],
            [
                'name' => 'Condiments & Sauces',
                'slug' => 'condiments-sauces',
                'description' => 'Filipino condiments, soy sauce, vinegar, and cooking sauces',
                'parent_slug' => 'pantry-staples',
                'sort_order' => 3,
            ],
            [
                'name' => 'Cooking Oils & Vinegar',
                'slug' => 'cooking-oils-vinegar',
                'description' => 'Cooking oils, vinegar, and essential cooking liquids',
                'parent_slug' => 'pantry-staples',
                'sort_order' => 4,
            ],
        ];

        // Create subcategories
        $allSubcategories = array_merge(
            $freshProduceSubcategories,
            $meatSeafoodSubcategories,
            $pantryStaplesSubcategories
        );
        foreach ($allSubcategories as $subcategoryData) {
            $parentCategory = $createdCategories[$subcategoryData['parent_slug']];
            unset($subcategoryData['parent_slug']);

            $subcategoryData['parent_id'] = $parentCategory->id;
            $subcategoryData['is_active'] = true;
            $subcategoryData['image'] = '/images/categories/' . $subcategoryData['slug'] . '.jpg';
            $subcategoryData['meta_title'] = $subcategoryData['name'] . ' - ' . $parentCategory->name;
            $subcategoryData['meta_description'] = $subcategoryData['description'];

            $subcategory = Category::updateOrCreate(
                ['slug' => $subcategoryData['slug']],
                $subcategoryData
            );
            $createdCategories[$subcategoryData['slug']] = $subcategory;
        }

        // Third-level categories (Local Fruits subcategories)
        $localFruitsSubcategories = [
            [
                'name' => 'Tropical Fruits',
                'slug' => 'tropical-fruits',
                'description' => 'Mango, pineapple, papaya, and other tropical fruits',
                'parent_slug' => 'local-fruits',
                'sort_order' => 1,
            ],
            [
                'name' => 'Citrus Fruits',
                'slug' => 'citrus-fruits',
                'description' => 'Calamansi, dalandan, and local citrus varieties',
                'parent_slug' => 'local-fruits',
                'sort_order' => 2,
            ],
            [
                'name' => 'Exotic Fruits',
                'slug' => 'exotic-fruits',
                'description' => 'Rambutan, lanzones, durian, and exotic local fruits',
                'parent_slug' => 'local-fruits',
                'sort_order' => 3,
            ],
        ];

        // Rice & Grains subcategories
        $riceGrainsSubcategories = [
            [
                'name' => 'Premium Rice',
                'slug' => 'premium-rice',
                'description' => 'Jasmine rice, basmati rice, and premium varieties',
                'parent_slug' => 'rice-grains',
                'sort_order' => 1,
            ],
            [
                'name' => 'Local Rice',
                'slug' => 'local-rice',
                'description' => 'Dinorado, red rice, and local rice varieties',
                'parent_slug' => 'rice-grains',
                'sort_order' => 2,
            ],
            [
                'name' => 'Other Grains',
                'slug' => 'other-grains',
                'description' => 'Quinoa, oats, and specialty grains',
                'parent_slug' => 'rice-grains',
                'sort_order' => 3,
            ],
        ];

        // Create third-level categories
        $thirdLevelCategories = array_merge($localFruitsSubcategories, $riceGrainsSubcategories);
        foreach ($thirdLevelCategories as $categoryData) {
            $parentCategory = $createdCategories[$categoryData['parent_slug']];
            unset($categoryData['parent_slug']);

            $categoryData['parent_id'] = $parentCategory->id;
            $categoryData['is_active'] = true;
            $categoryData['image'] = '/images/categories/' . $categoryData['slug'] . '.jpg';
            $categoryData['meta_title'] = $categoryData['name'] . ' - ' . $parentCategory->name;
            $categoryData['meta_description'] = $categoryData['description'];

            Category::updateOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );
        }

        $this->command->info('Created hierarchical category structure with ' . Category::count() . ' total categories.');
    }
}
