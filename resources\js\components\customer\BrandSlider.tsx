import React from 'react';

const BrandSlider: React.FC = () => {
    const brands = [
        '🏪 Nestle',
        '🥤 Coca-Cola',
        '🍫 Cadbury',
        '🥛 Alaska',
        '🍜 Maggi',
        '🧴 Unilever',
        '🍞 Gardenia',
        '🥫 Del Monte',
        '🍕 Pizza Hut',
        "🍔 McDonald's",
    ];

    return (
        <section className="overflow-hidden bg-muted/50 py-12">
            <div className="container mx-auto px-4">
                <h2 className="font-display mb-8 text-center text-2xl font-bold text-foreground">Trusted Brands</h2>
                <div className="relative">
                    <div className="animate-scroll flex">
                        {[...brands, ...brands].map((brand, index) => (
                            <div
                                key={index}
                                className="mx-8 flex-shrink-0 text-2xl font-semibold whitespace-nowrap text-muted-foreground transition-colors hover:text-primary"
                            >
                                {brand}
                            </div>
                        ))}
                    </div>
                </div>
            </div>
        </section>
    );
};

export default BrandSlider;
