APP_NAME="Lucky Star E-commerce"
APP_ENV=production
APP_KEY=base64:mZeIIwHp72ahtkbNQmAOFi6LWqFhkrsYKZDvGkQun1U=
APP_DEBUG=false
APP_URL=https://lucky-star-gktq.onrender.com

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file

BCRYPT_ROUNDS=12

LOG_CHANNEL=stderr
LOG_LEVEL=info

# External Neon.tech PostgreSQL Database Configuration
# Note: Use DATABASE_URL environment variable on Render for automatic parsing
# or set individual DB_* variables if using manual configuration
DB_CONNECTION=pgsql
DB_HOST=ep-green-snowflake-a1bxdo4q.ap-southeast-1.aws.neon.tech
DB_PORT=5432
DB_DATABASE=luckystar_db
DB_USERNAME=luckystar_db_owner
DB_PASSWORD=npg_dw2mgQ6HRotp

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database

# Redis configuration (optional - can use Render Redis add-on)
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# AWS S3 Configuration (optional for file storage)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

# Vite Configuration for React + Inertia.js
VITE_APP_NAME="${APP_NAME}"
VITE_APP_ENV="${APP_ENV}"

# Asset URL for production (Render will serve assets)
ASSET_URL="${APP_URL}"
