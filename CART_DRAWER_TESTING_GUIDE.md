# Cart Drawer Testing Guide

## Overview
This guide outlines how to test the newly implemented cart drawer/sidebar functionality that provides a quick way for users to review and manage their cart items without navigating to a separate cart page.

## Features Implemented

### 1. Cart Drawer Component
- **Slide-out Design**: Uses shadcn/ui Sheet component that slides in from the right
- **Responsive**: Works on both mobile and desktop with appropriate sizing
- **Product Display**: Shows product images, names, prices, and quantities
- **Interactive Controls**: Quantity adjustment (+/-) and remove item buttons

### 2. Integration with Header
- **Desktop Cart Button**: Existing cart button now opens the drawer
- **Mobile Cart Button**: New mobile-optimized cart button added
- **State Management**: Proper open/close state management with React hooks
- **Badge Updates**: Cart count badges update in real-time

### 3. Cart Management Features
- **Quantity Controls**: Increase/decrease quantity with validation
- **Remove Items**: Individual item removal with confirmation
- **Clear Cart**: Option to clear entire cart
- **Real-time Updates**: All changes reflect immediately in cart state

### 4. UI/UX Enhancements
- **Empty State**: Appropriate messaging when cart is empty
- **Loading States**: Loading indicators during cart operations
- **Error Handling**: Error messages for failed operations
- **Accessibility**: Proper ARIA labels and keyboard navigation

## Testing Scenarios

### Scenario 1: Basic Cart Drawer Functionality
1. **Open the application** in your browser
2. **Navigate to Products page** (`/products`)
3. **Add items to cart** by clicking "Add to Cart" on product cards
4. **Click the cart button** in the header (desktop) or mobile cart icon
5. **Verify drawer opens** from the right side with smooth animation
6. **Check cart items display** with correct product images, names, prices, quantities

### Scenario 2: Quantity Management
1. **Open cart drawer** with items in cart
2. **Test quantity increase** by clicking the "+" button
3. **Test quantity decrease** by clicking the "-" button
4. **Verify quantity of 1** cannot be decreased further (should remove item)
5. **Check real-time price updates** as quantities change
6. **Verify cart badge** in header updates with new quantities

### Scenario 3: Item Removal
1. **Add multiple items** to cart
2. **Open cart drawer**
3. **Click trash icon** on individual items to remove them
4. **Verify item disappears** from drawer immediately
5. **Check cart count** updates in header badge
6. **Test removing all items** to see empty state

### Scenario 4: Empty Cart State
1. **Clear all items** from cart (or start with empty cart)
2. **Click cart button** to open drawer
3. **Verify empty state** shows appropriate messaging
4. **Check "Continue Shopping" button** closes the drawer
5. **Verify no checkout options** are shown when cart is empty

### Scenario 5: Cart Actions
1. **Add items to cart** and open drawer
2. **Test "Continue Shopping" button** closes drawer
3. **Test "Clear Cart" button** removes all items
4. **Test "Proceed to Checkout" button** (currently logs to console)
5. **Verify all buttons** are properly disabled during loading states

### Scenario 6: Mobile Responsiveness
1. **Resize browser** to mobile width (< 768px)
2. **Verify mobile cart button** appears and desktop button is hidden
3. **Test cart drawer** opens properly on mobile
4. **Check drawer width** is appropriate for mobile screens
5. **Verify touch interactions** work correctly

### Scenario 7: Guest vs Authenticated Users
1. **Test as guest user** (incognito mode)
   - Add items to cart
   - Open drawer and verify localStorage indicator
   - Test all cart operations
2. **Test as authenticated user**
   - Log in and add items
   - Open drawer and verify no localStorage indicator
   - Test cart operations with API calls

### Scenario 8: Error Handling
1. **Simulate network issues** (disable network in dev tools)
2. **Try cart operations** and verify error messages appear
3. **Re-enable network** and verify operations work again
4. **Check loading states** during slow network conditions

## Technical Validation

### Code Quality Checks
```bash
# Run type checking
npm run types

# Run linting
npm run lint

# Both should pass without errors
```

### Browser Developer Tools Checks
1. **Console**: Should be free of JavaScript errors
2. **Network Tab**: Verify API calls for authenticated users
3. **localStorage**: Check guest cart data persistence
4. **Accessibility**: Test keyboard navigation and screen reader support

## Expected Behavior

### Cart Drawer UI
- ✅ Slides in smoothly from the right side
- ✅ Shows cart items with product images and details
- ✅ Displays correct prices and quantities
- ✅ Updates in real-time as items are modified
- ✅ Shows appropriate empty state when cart is empty

### Interactive Elements
- ✅ Quantity +/- buttons work correctly
- ✅ Remove item buttons work immediately
- ✅ Clear cart button removes all items
- ✅ Continue shopping button closes drawer
- ✅ All buttons show loading states when appropriate

### Responsive Design
- ✅ Desktop: Full-width cart button with text
- ✅ Mobile: Icon-only cart button
- ✅ Drawer adapts to screen size appropriately
- ✅ Touch interactions work on mobile devices

### Integration
- ✅ Works with existing cart hook and context
- ✅ Supports both guest (localStorage) and authenticated (API) modes
- ✅ Cart badge updates reflect drawer changes
- ✅ No conflicts with existing cart functionality

## Files Created/Modified

### New Files
- `resources/js/components/customer/CartDrawer.tsx` - Main cart drawer component
- `CART_DRAWER_TESTING_GUIDE.md` - This testing guide

### Modified Files
- `resources/js/components/customer/Header.tsx` - Added cart drawer integration

## Component Features

### CartDrawer Component Props
```typescript
interface CartDrawerProps {
    isOpen: boolean;      // Controls drawer visibility
    onClose: () => void;  // Callback to close drawer
}
```

### Key Features
- **Product Images**: Uses existing ProductImage component
- **Price Formatting**: Consistent with existing formatPrice utility
- **Loading States**: Proper loading indicators during operations
- **Error Display**: User-friendly error messages
- **Accessibility**: Proper ARIA labels and keyboard support

## Troubleshooting

### Common Issues
1. **Drawer not opening**: Check if cart button onClick is properly connected
2. **Items not displaying**: Verify cart context is available and has data
3. **Quantity changes not working**: Check if cart hook methods are properly called
4. **Mobile button not visible**: Verify responsive classes are correct

### Debug Information
- **Cart State**: Available via `useCart()` hook
- **Drawer State**: Managed by `isCartDrawerOpen` state in Header
- **Loading States**: Check `loading` property from cart context
- **Error Messages**: Check `error` property from cart context

## Success Criteria
- ✅ TypeScript types pass (`npm run types`)
- ✅ ESLint rules pass (`npm run lint`)
- ✅ Cart drawer opens/closes smoothly
- ✅ All cart operations work correctly
- ✅ Responsive design works on all screen sizes
- ✅ Works for both guest and authenticated users
- ✅ No JavaScript console errors
- ✅ Proper loading and error states
- ✅ Accessibility requirements met
