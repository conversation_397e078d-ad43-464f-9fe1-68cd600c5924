#!/bin/bash

echo "🔧 Configuring nginx for Laravel..."

# Remove any existing default configurations to avoid conflicts
rm -f /etc/nginx/sites-enabled/default
rm -f /etc/nginx/sites-enabled/default.conf
rm -f /etc/nginx/conf.d/default.conf

# Create Laravel-specific nginx configuration
cat > /etc/nginx/sites-available/laravel << 'EOF'
server {
    listen 80 default_server;
    listen [::]:80 default_server;

    root /var/www/html/public;
    index index.php index.html index.htm;

    server_name _;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";

    # Handle static assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        try_files $uri =404;
    }

    # Handle all requests (Laravel routing)
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # Handle PHP files
    location ~ \.php$ {
        include fastcgi_params;
        fastcgi_pass unix:/var/run/php-fpm.sock;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;

        # Handle Authorization header for API authentication
        fastcgi_param HTTP_AUTHORIZATION $http_authorization;
        fastcgi_param HTTP_X_XSRF_TOKEN $http_x_xsrf_token;
    }

    # Deny access to hidden files
    location ~ /\. {
        deny all;
    }

    # Error pages
    error_page 404 /index.php;
}
EOF

# Enable the Laravel site
ln -sf /etc/nginx/sites-available/laravel /etc/nginx/sites-enabled/laravel

# Debug: Show nginx configuration files
echo "🔍 Debug: nginx configuration files:"
ls -la /etc/nginx/sites-enabled/
if [ -d "/etc/nginx/conf.d/" ]; then
    ls -la /etc/nginx/conf.d/
else
    echo "conf.d directory does not exist (this is normal for sites-enabled setup)"
fi

# Test nginx configuration
echo "🔍 Testing nginx configuration..."
nginx -t || {
    echo "❌ nginx configuration test failed!"
    echo "🔍 nginx error log:"
    tail -20 /var/log/nginx/error.log 2>/dev/null || echo "No error log found"
    exit 1
}

echo "✅ nginx configured for Laravel"
