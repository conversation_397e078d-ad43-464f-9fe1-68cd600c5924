<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->command->info('🛒 Starting FreshCart Grocery Store Database Seeding...');

        // Seed in order to respect foreign key relationships
        $this->call([
            // 1. Users first (no dependencies)
            UserSeeder::class,

            // 2. Categories (self-referencing, but can handle parent-child relationships)
            CategorySeeder::class,

            // 3. Products (depends on categories)
            ProductSeeder::class,

            // 4. Coupons (no dependencies)
            CouponSeeder::class,

            // 5. Orders (depends on users)
            OrderSeeder::class,

            // 6. Reviews (depends on products, users, and optionally orders)
            ReviewSeeder::class,

            // 7. Shopping Cart (depends on users and products)
            ShoppingCartSeeder::class,
        ]);

        $this->command->info('✅ Database seeding completed successfully!');
        $this->command->info('');
        $this->command->info('🥬 Grocery Store Summary:');
        $this->command->info('   • Users: ' . \App\Models\User::count());
        $this->command->info('   • Categories: ' . \App\Models\Category::count());
        $this->command->info('   • Products: ' . \App\Models\Product::count());
        $this->command->info('   • Coupons: ' . \App\Models\Coupon::count());
        $this->command->info('   • Orders: ' . \App\Models\Order::count());
        $this->command->info('   • Order Items: ' . \App\Models\OrderItem::count());
        $this->command->info('   • Reviews: ' . \App\Models\Review::count());
        $this->command->info('   • Shopping Cart Items: ' . \App\Models\ShoppingCart::count());
        $this->command->info('');
        $this->command->info('🎉 Your grocery store database is now stocked with fresh products!');
        $this->command->info('');
        $this->command->info('🔑 Test Accounts:');
        $this->command->info('   • Admin: <EMAIL> (password: password123)');
        $this->command->info('   • Manager: <EMAIL> (password: password123)');
        $this->command->info('   • Support: <EMAIL> (password: password123)');
        $this->command->info('   • Customer: <EMAIL> (password: password123)');
        $this->command->info('');
        $this->command->info('🛍️ Ready to start shopping for fresh groceries!');
    }
}
