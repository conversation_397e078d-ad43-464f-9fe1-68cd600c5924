<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\UserAddress;
use App\Models\UserPaymentMethod;
use App\Models\UserPreference;
use App\Models\UserSecuritySetting;

class ProfileDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the test user
        $user = User::where('email', '<EMAIL>')->first();

        if (!$user) {
            $this->command->error('Test user not found. Please run UserSeeder first.');
            return;
        }

        // Create sample addresses
        UserAddress::create([
            'user_id' => $user->id,
            'type' => 'both',
            'label' => 'Home',
            'first_name' => 'John',
            'last_name' => 'Smith',
            'address_line_1' => '123 Rizal Street',
            'address_line_2' => 'Barangay San Antonio',
            'city' => 'Makati',
            'state_province' => 'Metro Manila',
            'postal_code' => '1203',
            'country' => 'PH',
            'phone' => '***********',
            'is_default' => true,
        ]);

        UserAddress::create([
            'user_id' => $user->id,
            'type' => 'shipping',
            'label' => 'Office',
            'first_name' => 'John',
            'last_name' => 'Smith',
            'company' => 'Tech Solutions Inc.',
            'address_line_1' => '456 Ayala Avenue',
            'address_line_2' => '15th Floor, Tower A',
            'city' => 'Makati',
            'state_province' => 'Metro Manila',
            'postal_code' => '1226',
            'country' => 'PH',
            'phone' => '***********',
            'is_default' => false,
        ]);

        // Create sample payment methods
        UserPaymentMethod::create([
            'user_id' => $user->id,
            'type' => 'gcash',
            'label' => 'Personal GCash',
            'mobile_number' => '***********',
            'account_name' => 'John Smith',
            'is_default' => true,
            'is_verified' => true,
            'verified_at' => now(),
        ]);

        UserPaymentMethod::create([
            'user_id' => $user->id,
            'type' => 'credit_card',
            'label' => 'Personal Visa',
            'card_last_four' => '1234',
            'card_brand' => 'visa',
            'card_holder_name' => 'John Smith',
            'expiry_month' => '12',
            'expiry_year' => '2026',
            'is_default' => false,
            'is_verified' => true,
            'verified_at' => now(),
        ]);

        // Create user preferences
        UserPreference::create([
            'user_id' => $user->id,
            'email_marketing' => true,
            'email_order_updates' => true,
            'email_promotions' => false,
            'sms_marketing' => false,
            'sms_order_updates' => true,
            'push_notifications' => true,
            'language' => 'en',
            'currency' => 'PHP',
            'timezone' => 'Asia/Manila',
            'preferred_delivery_time' => 'morning',
            'dietary_restrictions' => ['vegetarian'],
            'favorite_categories' => [1, 2, 3],
            'profile_public' => false,
            'show_order_history' => false,
            'allow_reviews_display' => true,
        ]);

        // Create security settings
        UserSecuritySetting::create([
            'user_id' => $user->id,
            'two_factor_enabled' => false,
            'login_notifications' => true,
            'logout_other_devices' => false,
            'session_timeout_minutes' => 120,
            'failed_login_attempts' => 0,
        ]);

        $this->command->info('Profile data seeded successfully for user: ' . $user->email);
    }
}
