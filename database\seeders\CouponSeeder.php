<?php

namespace Database\Seeders;

use App\Models\Coupon;
use Illuminate\Database\Seeder;
use Carbon\Carbon;

class CouponSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $coupons = [
            // Percentage discount coupons
            [
                'code' => 'WELCOME10',
                'name' => 'Welcome 10% Off',
                'description' => 'Get 10% off your first order. Welcome to Lucky Star!',
                'type' => 'percentage',
                'value' => 10.00,
                'minimum_amount' => 50.00,
                'maximum_discount' => 100.00,
                'usage_limit' => 1000,
                'usage_limit_per_user' => 1,
                'used_count' => 45,
                'is_active' => true,
                'starts_at' => Carbon::now()->subDays(30),
                'expires_at' => Carbon::now()->addDays(60),
            ],
            [
                'code' => 'SAVE20',
                'name' => '20% Off Everything',
                'description' => 'Save 20% on all products. Limited time offer!',
                'type' => 'percentage',
                'value' => 20.00,
                'minimum_amount' => 100.00,
                'maximum_discount' => 200.00,
                'usage_limit' => 500,
                'usage_limit_per_user' => 1,
                'used_count' => 123,
                'is_active' => true,
                'starts_at' => Carbon::now()->subDays(7),
                'expires_at' => Carbon::now()->addDays(14),
            ],
            [
                'code' => 'BLACKFRIDAY',
                'name' => 'Black Friday 30% Off',
                'description' => 'Massive Black Friday sale - 30% off everything!',
                'type' => 'percentage',
                'value' => 30.00,
                'minimum_amount' => 200.00,
                'maximum_discount' => 500.00,
                'usage_limit' => 2000,
                'usage_limit_per_user' => 1,
                'used_count' => 856,
                'is_active' => false, // Past event
                'starts_at' => Carbon::now()->subDays(90),
                'expires_at' => Carbon::now()->subDays(85),
            ],
            [
                'code' => 'SUMMER25',
                'name' => 'Summer Sale 25%',
                'description' => 'Beat the heat with 25% off summer essentials!',
                'type' => 'percentage',
                'value' => 25.00,
                'minimum_amount' => 75.00,
                'maximum_discount' => 150.00,
                'usage_limit' => 800,
                'usage_limit_per_user' => 2,
                'used_count' => 234,
                'is_active' => true,
                'starts_at' => Carbon::now()->addDays(30),
                'expires_at' => Carbon::now()->addDays(90),
            ],

            // Fixed amount discount coupons
            [
                'code' => 'SAVE50',
                'name' => '$50 Off Your Order',
                'description' => 'Get $50 off when you spend $300 or more.',
                'type' => 'fixed_amount',
                'value' => 50.00,
                'minimum_amount' => 300.00,
                'maximum_discount' => null,
                'usage_limit' => 200,
                'usage_limit_per_user' => 1,
                'used_count' => 67,
                'is_active' => true,
                'starts_at' => Carbon::now()->subDays(15),
                'expires_at' => Carbon::now()->addDays(45),
            ],
            [
                'code' => 'BIGSPENDER',
                'name' => '$100 Off Big Orders',
                'description' => 'Spend $500 or more and save $100!',
                'type' => 'fixed_amount',
                'value' => 100.00,
                'minimum_amount' => 500.00,
                'maximum_discount' => null,
                'usage_limit' => 100,
                'usage_limit_per_user' => 1,
                'used_count' => 23,
                'is_active' => true,
                'starts_at' => Carbon::now()->subDays(10),
                'expires_at' => Carbon::now()->addDays(30),
            ],
            [
                'code' => 'NEWCUSTOMER25',
                'name' => '$25 Off First Order',
                'description' => 'New customers save $25 on orders over $100.',
                'type' => 'fixed_amount',
                'value' => 25.00,
                'minimum_amount' => 100.00,
                'maximum_discount' => null,
                'usage_limit' => 500,
                'usage_limit_per_user' => 1,
                'used_count' => 189,
                'is_active' => true,
                'starts_at' => Carbon::now()->subDays(60),
                'expires_at' => Carbon::now()->addDays(30),
            ],

            // Free shipping coupons
            [
                'code' => 'FREESHIP',
                'name' => 'Free Shipping',
                'description' => 'Get free shipping on any order!',
                'type' => 'free_shipping',
                'value' => 0.00,
                'minimum_amount' => null,
                'maximum_discount' => null,
                'usage_limit' => 1000,
                'usage_limit_per_user' => 3,
                'used_count' => 456,
                'is_active' => true,
                'starts_at' => Carbon::now()->subDays(20),
                'expires_at' => Carbon::now()->addDays(40),
            ],
            [
                'code' => 'SHIPFREE50',
                'name' => 'Free Shipping Over $50',
                'description' => 'Free shipping on orders over $50.',
                'type' => 'free_shipping',
                'value' => 0.00,
                'minimum_amount' => 50.00,
                'maximum_discount' => null,
                'usage_limit' => null, // Unlimited
                'usage_limit_per_user' => null, // Unlimited per user
                'used_count' => 1234,
                'is_active' => true,
                'starts_at' => Carbon::now()->subDays(90),
                'expires_at' => null, // No expiry
            ],

            // Expired coupons for testing
            [
                'code' => 'EXPIRED10',
                'name' => 'Expired 10% Off',
                'description' => 'This coupon has expired.',
                'type' => 'percentage',
                'value' => 10.00,
                'minimum_amount' => 25.00,
                'maximum_discount' => 50.00,
                'usage_limit' => 100,
                'usage_limit_per_user' => 1,
                'used_count' => 89,
                'is_active' => true,
                'starts_at' => Carbon::now()->subDays(60),
                'expires_at' => Carbon::now()->subDays(10),
            ],

            // Inactive coupon for testing
            [
                'code' => 'INACTIVE15',
                'name' => 'Inactive 15% Off',
                'description' => 'This coupon is currently inactive.',
                'type' => 'percentage',
                'value' => 15.00,
                'minimum_amount' => 75.00,
                'maximum_discount' => 100.00,
                'usage_limit' => 200,
                'usage_limit_per_user' => 1,
                'used_count' => 0,
                'is_active' => false,
                'starts_at' => Carbon::now()->subDays(5),
                'expires_at' => Carbon::now()->addDays(25),
            ],

            // Future coupon for testing
            [
                'code' => 'FUTURE20',
                'name' => 'Future 20% Off',
                'description' => 'This coupon will be active in the future.',
                'type' => 'percentage',
                'value' => 20.00,
                'minimum_amount' => 100.00,
                'maximum_discount' => 150.00,
                'usage_limit' => 300,
                'usage_limit_per_user' => 1,
                'used_count' => 0,
                'is_active' => true,
                'starts_at' => Carbon::now()->addDays(15),
                'expires_at' => Carbon::now()->addDays(45),
            ],

            // High usage limit reached coupon
            [
                'code' => 'MAXEDOUT',
                'name' => 'Maxed Out Coupon',
                'description' => 'This coupon has reached its usage limit.',
                'type' => 'percentage',
                'value' => 15.00,
                'minimum_amount' => 50.00,
                'maximum_discount' => 75.00,
                'usage_limit' => 50,
                'usage_limit_per_user' => 1,
                'used_count' => 50, // Reached limit
                'is_active' => true,
                'starts_at' => Carbon::now()->subDays(30),
                'expires_at' => Carbon::now()->addDays(30),
            ],
        ];

        foreach ($coupons as $couponData) {
            Coupon::updateOrCreate(
                ['code' => $couponData['code']],
                $couponData
            );
        }

        $this->command->info('Created ' . count($coupons) . ' coupons with various types and conditions.');
    }
}
