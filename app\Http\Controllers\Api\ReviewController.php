<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Review;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ReviewController extends Controller
{
    /**
     * Display a listing of approved reviews.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Review::with(['user', 'product'])
            ->where('is_approved', true)
            ->orderBy('created_at', 'desc');

        // Filter by product
        if ($request->has('product_id')) {
            $query->where('product_id', $request->get('product_id'));
        }

        // Filter by rating
        if ($request->has('rating')) {
            $query->where('rating', $request->get('rating'));
        }

        // Filter by verified purchase
        if ($request->has('verified_purchase')) {
            $query->where('is_verified_purchase', $request->get('verified_purchase') === 'true');
        }

        $perPage = min($request->get('per_page', 15), 50);
        $reviews = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $reviews->items(),
            'meta' => [
                'current_page' => $reviews->currentPage(),
                'last_page' => $reviews->lastPage(),
                'per_page' => $reviews->perPage(),
                'total' => $reviews->total(),
            ]
        ]);
    }

    /**
     * Display admin's view of all reviews.
     */
    public function adminIndex(Request $request): JsonResponse
    {
        $query = Review::with(['user', 'product'])
            ->orderBy('created_at', 'desc');

        // Filter by approval status
        if ($request->has('is_approved')) {
            $query->where('is_approved', $request->get('is_approved') === 'true');
        }

        // Filter by product
        if ($request->has('product_id')) {
            $query->where('product_id', $request->get('product_id'));
        }

        // Filter by rating
        if ($request->has('rating')) {
            $query->where('rating', $request->get('rating'));
        }

        // Search by title or comment
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function ($q) use ($search) {
                $q->where('title', 'ILIKE', "%{$search}%")
                  ->orWhere('comment', 'ILIKE', "%{$search}%");
            });
        }

        $perPage = min($request->get('per_page', 20), 100);
        $reviews = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $reviews->items(),
            'meta' => [
                'current_page' => $reviews->currentPage(),
                'last_page' => $reviews->lastPage(),
                'per_page' => $reviews->perPage(),
                'total' => $reviews->total(),
            ]
        ]);
    }

    /**
     * Store a newly created review.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'order_id' => 'nullable|exists:orders,id',
            'rating' => 'required|integer|min:1|max:5',
            'title' => 'required|string|max:255',
            'comment' => 'required|string|max:2000',
            'images' => 'nullable|array|max:5',
            'images.*' => 'string|max:500',
        ]);

        // Check if user has already reviewed this product
        $existingReview = Review::where('user_id', $request->user()->id)
            ->where('product_id', $validated['product_id'])
            ->first();

        if ($existingReview) {
            return response()->json([
                'success' => false,
                'message' => 'You have already reviewed this product',
                'errors' => ['product_id' => ['Product already reviewed by user']]
            ], 422);
        }

        // Check if order belongs to user (if order_id provided)
        $isVerifiedPurchase = false;
        if ($validated['order_id']) {
            $order = $request->user()->orders()
                ->where('id', $validated['order_id'])
                ->whereHas('orderItems', function ($query) use ($validated) {
                    $query->where('product_id', $validated['product_id']);
                })
                ->first();

            if (!$order) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid order for this product',
                    'errors' => ['order_id' => ['Order does not contain this product']]
                ], 422);
            }

            $isVerifiedPurchase = true;
        }

        $review = Review::create([
            'product_id' => $validated['product_id'],
            'user_id' => $request->user()->id,
            'order_id' => $validated['order_id'],
            'rating' => $validated['rating'],
            'title' => $validated['title'],
            'comment' => $validated['comment'],
            'images' => $validated['images'],
            'is_approved' => false, // Reviews need approval by default
            'is_verified_purchase' => $isVerifiedPurchase,
        ]);

        $review->load(['user', 'product']);

        return response()->json([
            'success' => true,
            'message' => 'Review submitted successfully and is pending approval',
            'data' => $review
        ], 201);
    }

    /**
     * Display the specified review.
     */
    public function show(Review $review): JsonResponse
    {
        // Only show approved reviews to public
        if (!$review->is_approved) {
            return response()->json([
                'success' => false,
                'message' => 'Review not found'
            ], 404);
        }

        $review->load(['user', 'product']);

        return response()->json([
            'success' => true,
            'data' => $review
        ]);
    }

    /**
     * Update the specified review.
     */
    public function update(Request $request, Review $review): JsonResponse
    {
        // Ensure user can only update their own reviews
        if ($review->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to review'
            ], 403);
        }

        $validated = $request->validate([
            'rating' => 'sometimes|required|integer|min:1|max:5',
            'title' => 'sometimes|required|string|max:255',
            'comment' => 'sometimes|required|string|max:2000',
            'images' => 'nullable|array|max:5',
            'images.*' => 'string|max:500',
        ]);

        // Reset approval status when review is updated
        $validated['is_approved'] = false;

        $review->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Review updated successfully and is pending approval',
            'data' => $review
        ]);
    }

    /**
     * Remove the specified review.
     */
    public function destroy(Request $request, Review $review): JsonResponse
    {
        // Ensure user can only delete their own reviews
        if ($review->user_id !== $request->user()->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized access to review'
            ], 403);
        }

        $review->delete();

        return response()->json([
            'success' => true,
            'message' => 'Review deleted successfully'
        ]);
    }

    /**
     * Approve a review (admin only).
     */
    public function approve(Review $review): JsonResponse
    {
        $review->update(['is_approved' => true]);

        return response()->json([
            'success' => true,
            'message' => 'Review approved successfully',
            'data' => $review
        ]);
    }

    /**
     * Reject a review (admin only).
     */
    public function reject(Review $review): JsonResponse
    {
        $review->update(['is_approved' => false]);

        return response()->json([
            'success' => true,
            'message' => 'Review rejected successfully',
            'data' => $review
        ]);
    }
}
