import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Input } from '@/components/ui/input';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { type SharedData } from '@/types';
import { Link, usePage } from '@inertiajs/react';
import { Heart, LogOut, MenuIcon, Package, PhoneIcon, SearchIcon, ShoppingCartIcon, User, UserIcon } from 'lucide-react';
import React, { useState } from 'react';
import ThemeToggle from './ThemeToggle';
import { useCart } from '@/hooks/use-cart';
import CartDrawer from './CartDrawer';
import { useCartBadgeAnimation, useCartDrawerFeedback, getCartBadgeClasses, getCartDrawerButtonClasses } from '@/hooks/use-add-to-cart-animation';

const Header: React.FC = () => {
    const { auth } = usePage<SharedData>().props;
    const isAuthenticated = !!auth?.user;
    const currentUrl = usePage().url;
    useCart(); // Keep the cart context active
    const [isCartDrawerOpen, setIsCartDrawerOpen] = useState(false);

    // Animation hooks for cart feedback
    const { isAnimating: isBadgeAnimating, cartCount } = useCartBadgeAnimation();
    const { isHighlighted: isDrawerHighlighted } = useCartDrawerFeedback();

    const navigationItems = [
        { name: 'Home', href: route('home') },
        { name: 'Products', href: route('products') },
        { name: 'Cart', href: '/cart' },
        { name: 'Categories', href: '#' },
        { name: 'About Us', href: '#' },
        { name: 'Contact', href: '#' },
    ];

    const isActiveRoute = (href: string) => {
        if (href === route('home')) {
            return currentUrl === '/' || currentUrl === route('home');
        }
        return currentUrl.startsWith(href);
    };

    return (
        <header className="sticky top-0 z-50 border-b bg-background shadow-lg">
            {/* Top bar */}
            <div className="bg-primary py-2 text-primary-foreground">
                <div className="container mx-auto flex items-center justify-between px-4 text-sm">
                    <div className="flex items-center space-x-4">
                        <span className="flex items-center space-x-1">
                            <PhoneIcon />
                            <span>(*************</span>
                        </span>
                        <span>Free delivery on orders over ₱1,000</span>
                    </div>
                    <div className="flex items-center space-x-4">
                        <ThemeToggle />
                        {isAuthenticated ? (
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button
                                        variant="ghost"
                                        size="sm"
                                        className="flex items-center space-x-1 text-primary-foreground hover:bg-primary/20"
                                    >
                                        <User className="h-4 w-4" />
                                        <span>Hi, {auth.user.name}</span>
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="w-48">
                                    <DropdownMenuItem asChild>
                                        <Link href={route('profile')} className="flex items-center">
                                            <User className="mr-2 h-4 w-4" />
                                            My Profile
                                        </Link>
                                    </DropdownMenuItem>
                                    <DropdownMenuItem asChild>
                                        <Link href={route('dashboard')} className="flex items-center">
                                            <User className="mr-2 h-4 w-4" />
                                            My Account
                                        </Link>
                                    </DropdownMenuItem>
                                    <DropdownMenuItem>
                                        <Heart className="mr-2 h-4 w-4" />
                                        Wishlist
                                    </DropdownMenuItem>
                                    <DropdownMenuItem>
                                        <Package className="mr-2 h-4 w-4" />
                                        My Orders
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem asChild>
                                        <Link href={route('logout')} method="post" className="flex items-center text-destructive">
                                            <LogOut className="mr-2 h-4 w-4" />
                                            Logout
                                        </Link>
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        ) : (
                            <Link href={route('login')}>
                                <Button variant="ghost" size="sm" className="flex items-center space-x-1 text-primary-foreground hover:bg-primary/20">
                                    <UserIcon />
                                    <span>Login / Register</span>
                                </Button>
                            </Link>
                        )}
                    </div>
                </div>
            </div>

            {/* Main navigation */}
            <nav className="container mx-auto px-4 py-4">
                <div className="flex items-center justify-between">
                    {/* Logo */}
                    <div className="flex items-center space-x-3">
                        <div className="rounded-xl bg-gradient-to-r from-green-500 to-emerald-500 p-3 text-white">
                            <span className="text-2xl font-bold">🌟</span>
                        </div>
                        <div>
                            <h1 className="font-display text-2xl font-bold text-foreground">Lucky Star</h1>
                            <p className="text-sm text-muted-foreground">Grocery</p>
                        </div>
                    </div>

                    {/* Search bar */}
                    <div className="mx-8 hidden max-w-2xl flex-1 md:flex">
                        <div className="relative w-full">
                            <Input
                                type="text"
                                placeholder="Search for products..."
                                className="w-full rounded-lg border-2 border-gray-200 px-4 py-3 pr-12 focus:border-green-500 focus:outline-none"
                            />
                            <Button
                                variant="ghost"
                                size="sm"
                                className="absolute top-1/2 right-3 -translate-y-1/2 transform p-1 text-gray-400 hover:text-green-500"
                            >
                                <SearchIcon />
                            </Button>
                        </div>
                    </div>

                    {/* Right side */}
                    <div className="flex items-center space-x-4">
                        {/* Cart Button */}
                        <Button
                            variant="secondary"
                            className={getCartDrawerButtonClasses(
                                isDrawerHighlighted,
                                "relative hidden items-center space-x-2 bg-primary/10 text-primary hover:bg-primary/20 md:flex"
                            )}
                            onClick={() => setIsCartDrawerOpen(true)}
                        >
                            <ShoppingCartIcon />
                            <span className="font-semibold">Cart ({cartCount})</span>
                            {/* Cart badge */}
                            {cartCount > 0 && (
                                <Badge
                                    variant="destructive"
                                    className={getCartBadgeClasses(
                                        isBadgeAnimating,
                                        "absolute -top-2 -right-2 h-5 w-5 p-0 text-xs"
                                    )}
                                >
                                    {cartCount}
                                </Badge>
                            )}
                        </Button>

                        {/* Mobile Cart Button */}
                        <Button
                            variant="secondary"
                            size="sm"
                            className={getCartDrawerButtonClasses(
                                isDrawerHighlighted,
                                "relative bg-primary/10 text-primary hover:bg-primary/20 md:hidden"
                            )}
                            onClick={() => setIsCartDrawerOpen(true)}
                        >
                            <ShoppingCartIcon className="h-5 w-5" />
                            {cartCount > 0 && (
                                <Badge
                                    variant="destructive"
                                    className={getCartBadgeClasses(
                                        isBadgeAnimating,
                                        "absolute -top-2 -right-2 h-4 w-4 p-0 text-xs"
                                    )}
                                >
                                    {cartCount}
                                </Badge>
                            )}
                        </Button>

                        {/* Wishlist Button for authenticated users */}
                        {isAuthenticated && (
                            <Button
                                variant="ghost"
                                size="sm"
                                className="hidden items-center space-x-1 text-muted-foreground hover:text-primary md:flex"
                            >
                                <Heart className="h-4 w-4" />
                                <span className="sr-only">Wishlist</span>
                            </Button>
                        )}

                        <Sheet>
                            <SheetTrigger asChild>
                                <Button variant="ghost" size="sm" className="text-foreground md:hidden">
                                    <MenuIcon />
                                </Button>
                            </SheetTrigger>
                            <SheetContent side="right" className="w-64">
                                <div className="mt-8 flex flex-col space-y-4">
                                    {navigationItems.map((item) => (
                                        <Link
                                            key={item.name}
                                            href={item.href}
                                            className={`py-2 font-medium ${
                                                isActiveRoute(item.href) ? 'text-primary' : 'text-foreground hover:text-primary'
                                            }`}
                                        >
                                            {item.name}
                                        </Link>
                                    ))}
                                </div>
                            </SheetContent>
                        </Sheet>
                    </div>
                </div>

                {/* Navigation links */}
                <div className="mt-4 hidden items-center justify-center space-x-8 border-t border-border pt-4 md:flex">
                    {navigationItems.map((item) => (
                        <Button
                            key={item.name}
                            variant="ghost"
                            className={`py-2 font-medium transition-colors ${
                                isActiveRoute(item.href) ? 'bg-primary/10 text-primary' : 'text-foreground hover:text-primary'
                            }`}
                            asChild
                        >
                            <Link href={item.href}>{item.name}</Link>
                        </Button>
                    ))}
                </div>
            </nav>

            {/* Cart Drawer */}
            <CartDrawer
                isOpen={isCartDrawerOpen}
                onClose={() => setIsCartDrawerOpen(false)}
            />
        </header>
    );
};

export default Header;
