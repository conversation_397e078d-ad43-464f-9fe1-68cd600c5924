// Ecommerce-specific TypeScript interfaces and types

export interface Product {
    id: number;
    name: string;
    description: string;
    price: number;
    sku: string;
    stock_quantity: number;
    is_active: boolean;
    images: string[];
    category_id: number;
    category?: Category;
    reviews?: Review[];
    average_rating?: number;
    reviews_count?: number;
    created_at: string;
    updated_at: string;
}

export interface Category {
    id: number;
    name: string;
    description?: string;
    slug: string;
    image?: string;
    is_active: boolean;
    parent_id?: number;
    parent?: Category;
    children?: Category[];
    products_count?: number;
    created_at: string;
    updated_at: string;
}

export interface Review {
    id: number;
    user_id: number;
    product_id: number;
    order_id?: number;
    rating: number;
    title?: string;
    comment?: string;
    is_approved: boolean;
    user?: User;
    product?: Product;
    order?: Order;
    created_at: string;
    updated_at: string;
}

// Shopping Cart and Checkout Types
export interface CartItem {
    id: number | string;
    product_id: number;
    quantity: number;
    product: {
        id: number;
        name: string;
        price: number;
        images: string[];
        sku: string;
        stock_quantity: number;
    };
    product_options?: Record<string, unknown>;
}

export interface CartData {
    items: CartItem[];
    total_items: number;
    subtotal: number;
}

// Address Types
export interface Address {
    id?: number;
    type?: 'billing' | 'shipping';
    first_name: string;
    last_name: string;
    company?: string;
    address_line_1: string;
    address_line_2?: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
    phone?: string;
    email?: string;
    is_default?: boolean;
}

// Shipping Types
export interface ShippingMethod {
    id: string;
    name: string;
    description: string;
    price: number;
    estimated_days: string;
    carrier?: string;
    is_available: boolean;
}

// Payment Types
export type PaymentMethodType = 
    | 'cod' 
    | 'gcash' 
    | 'paymaya' 
    | 'bpi_online' 
    | 'bdo_online' 
    | 'paypal' 
    | 'credit_card' 
    | 'debit_card';

export interface PaymentMethod {
    id: PaymentMethodType;
    name: string;
    description: string;
    icon?: string;
    is_available: boolean;
    requires_online_payment: boolean;
    processing_fee?: number;
}

export interface PaymentDetails {
    method: PaymentMethodType;
    reference?: string;
    card_last_four?: string;
    card_brand?: string;
    transaction_id?: string;
}

// Coupon Types
export interface Coupon {
    id: number;
    code: string;
    name: string;
    description?: string;
    type: 'percentage' | 'fixed_amount';
    value: number;
    minimum_amount?: number;
    maximum_discount?: number;
    usage_limit?: number;
    usage_limit_per_user?: number;
    used_count: number;
    is_active: boolean;
    starts_at?: string;
    expires_at?: string;
    created_at: string;
    updated_at: string;
}

export interface AppliedCoupon {
    coupon: Coupon;
    discount_amount: number;
}

// Checkout Types
export interface CheckoutData {
    customer_info: {
        email: string;
        phone?: string;
        create_account?: boolean;
        password?: string;
    };
    billing_address: Address;
    shipping_address: Address;
    same_as_billing: boolean;
    shipping_method: ShippingMethod;
    payment_method: PaymentMethod;
    payment_details?: PaymentDetails;
    applied_coupon?: AppliedCoupon;
    special_instructions?: string;
}

export interface CheckoutSummary {
    items: CartItem[];
    subtotal: number;
    shipping_amount: number;
    tax_amount: number;
    discount_amount: number;
    total_amount: number;
    applied_coupon?: AppliedCoupon;
}

export interface CheckoutStep {
    id: string;
    title: string;
    description: string;
    completed: boolean;
    current: boolean;
}

// Order Processing Types
export interface OrderCreateRequest {
    customer_info: CheckoutData['customer_info'];
    billing_address: Address;
    shipping_address: Address;
    shipping_method_id: string;
    payment_method: PaymentMethodType;
    payment_details?: PaymentDetails;
    coupon_code?: string;
    special_instructions?: string;
    cart_items: CartItem[];
}

export interface OrderCreateResponse {
    success: boolean;
    message: string;
    order?: Order;
    payment_url?: string;
    errors?: Record<string, string[]>;
}

// Order Types
export interface Order {
    id: number;
    order_number: string;
    user_id?: number;
    status: 'pending' | 'processing' | 'shipped' | 'delivered' | 'cancelled' | 'refunded';
    subtotal: number;
    tax_amount: number;
    shipping_amount: number;
    discount_amount: number;
    total_amount: number;
    currency: string;
    payment_status: 'pending' | 'paid' | 'failed' | 'refunded' | 'partially_refunded';
    payment_method: PaymentMethodType;
    payment_reference?: string;
    billing_first_name: string;
    billing_last_name: string;
    billing_email: string;
    billing_phone?: string;
    billing_company?: string;
    billing_address_line_1: string;
    billing_address_line_2?: string;
    billing_city: string;
    billing_state: string;
    billing_postal_code: string;
    billing_country: string;
    shipping_first_name: string;
    shipping_last_name: string;
    shipping_company?: string;
    shipping_address_line_1: string;
    shipping_address_line_2?: string;
    shipping_city: string;
    shipping_state: string;
    shipping_postal_code: string;
    shipping_country: string;
    shipping_method?: string;
    tracking_number?: string;
    notes?: string;
    shipped_at?: string;
    delivered_at?: string;
    user?: User;
    orderItems?: OrderItem[];
    reviews?: Review[];
    created_at: string;
    updated_at: string;
}

export interface OrderItem {
    id: number;
    order_id: number;
    product_id: number;
    product_name: string;
    product_sku: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    product_options?: Record<string, unknown>;
    order?: Order;
    product?: Product;
    created_at: string;
    updated_at: string;
}

// Guest Order Types (for guest checkout)
export interface GuestOrder {
    id: number;
    order_number: string;
    email: string;
    phone?: string;
    tracking_token: string;
    status: Order['status'];
    payment_status: Order['payment_status'];
    total_amount: number;
    currency: string;
    created_at: string;
    updated_at: string;
}

// Form Validation Types
export interface CheckoutFormErrors {
    customer_info?: {
        email?: string[];
        phone?: string[];
        password?: string[];
    };
    billing_address?: {
        first_name?: string[];
        last_name?: string[];
        address_line_1?: string[];
        city?: string[];
        state?: string[];
        postal_code?: string[];
        country?: string[];
        phone?: string[];
    };
    shipping_address?: {
        first_name?: string[];
        last_name?: string[];
        address_line_1?: string[];
        city?: string[];
        state?: string[];
        postal_code?: string[];
        country?: string[];
    };
    shipping_method?: string[];
    payment_method?: string[];
    coupon_code?: string[];
}

// API Response Types
export interface ApiResponse<T = unknown> {
    success: boolean;
    message: string;
    data?: T;
    errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
    meta: {
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        from: number;
        to: number;
    };
    links: {
        first: string;
        last: string;
        prev?: string;
        next?: string;
    };
}

// Import User type from main types
import type { User } from './index';
