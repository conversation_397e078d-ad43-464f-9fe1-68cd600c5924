import { Eye, Heart, ShoppingCart, Star } from 'lucide-react';
import React from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { type SharedData } from '@/types';
import { usePage } from '@inertiajs/react';
import ProductImage from './ProductImage';

interface Product {
    id: number;
    name: string;
    price: string;
    originalPrice: string;
    image: string;
    rating: number;
    reviews: number;
    description?: string;
    category?: string;
    inStock?: boolean;
}

interface ProductListItemProps {
    product: Product;
    onAddToCart?: (productId: number) => void;
    onQuickView?: (productId: number) => void;
    onAddToWishlist?: (productId: number) => void;
}

const ProductListItem: React.FC<ProductListItemProps> = ({ product, onAddToCart, onQuickView, onAddToWishlist }) => {
    const { auth } = usePage<SharedData>().props;
    const isAuthenticated = !!auth?.user;

    const handleAddToCart = () => {
        onAddToCart?.(product.id);
    };

    const handleQuickView = () => {
        onQuickView?.(product.id);
    };

    const handleAddToWishlist = () => {
        onAddToWishlist?.(product.id);
    };

    return (
        <Card className="overflow-hidden transition-all duration-300 hover:shadow-lg">
            <CardContent className="p-0">
                <div className="flex flex-col sm:flex-row">
                    {/* Product Image */}
                    <div className="w-full flex-shrink-0 sm:w-48">
                        <ProductImage
                            src={product.image}
                            alt={product.name}
                            size="xl"
                            className="h-48 w-full"
                        />
                    </div>

                    {/* Product Details */}
                    <div className="flex-1 p-6">
                        <div className="flex h-full flex-col">
                            {/* Category Badge */}
                            {product.category && (
                                <Badge variant="secondary" className="mb-2 w-fit">
                                    {product.category}
                                </Badge>
                            )}

                            {/* Product Name */}
                            <h3 className="mb-2 line-clamp-2 text-xl font-semibold text-foreground">{product.name}</h3>

                            {/* Rating */}
                            <div className="mb-3 flex items-center">
                                <div className="flex text-yellow-400">
                                    {[...Array(5)].map((_, i) => (
                                        <Star key={i} className={`h-4 w-4 ${i < Math.floor(product.rating) ? 'fill-current' : ''}`} />
                                    ))}
                                </div>
                                <span className="ml-2 text-sm text-muted-foreground">
                                    {product.rating} ({product.reviews} reviews)
                                </span>
                            </div>

                            {/* Description */}
                            {product.description && <p className="mb-4 line-clamp-2 text-sm text-muted-foreground">{product.description}</p>}

                            {/* Price and Actions */}
                            <div className="mt-auto">
                                <div className="flex items-center justify-between">
                                    {/* Price */}
                                    <div className="flex items-center gap-2">
                                        <span className="text-2xl font-bold text-primary">{product.price}</span>
                                        {product.originalPrice && (
                                            <span className="text-lg text-muted-foreground line-through">{product.originalPrice}</span>
                                        )}
                                    </div>

                                    {/* Action Buttons */}
                                    <div className="flex items-center gap-2">
                                        {/* Quick View Button */}
                                        {onQuickView && (
                                            <Button variant="outline" size="sm" onClick={handleQuickView} className="hidden sm:flex">
                                                <Eye className="mr-2 h-4 w-4" />
                                                Quick View
                                            </Button>
                                        )}

                                        {/* Wishlist Button - only for authenticated users */}
                                        {isAuthenticated && onAddToWishlist && (
                                            <Button variant="outline" size="sm" className="px-3" onClick={handleAddToWishlist}>
                                                <Heart className="h-4 w-4" />
                                            </Button>
                                        )}

                                        {/* Add to Cart Button */}
                                        <Button onClick={handleAddToCart} size="sm" disabled={product.inStock === false}>
                                            <ShoppingCart className="mr-2 h-4 w-4" />
                                            {product.inStock === false ? 'Out of Stock' : 'Add to Cart'}
                                        </Button>
                                    </div>
                                </div>

                                {/* Stock Status */}
                                <div className="mt-2 flex items-center gap-2">
                                    {product.inStock !== false ? (
                                        <Badge variant="default" className="bg-green-100 text-green-800">
                                            In Stock
                                        </Badge>
                                    ) : (
                                        <Badge variant="destructive">Out of Stock</Badge>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};

export default ProductListItem;
