<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add PostgreSQL-specific optimizations and constraints

        // Add check constraints for data integrity
        DB::statement('ALTER TABLE products ADD CONSTRAINT products_price_positive CHECK (price >= 0)');
        DB::statement('ALTER TABLE products ADD CONSTRAINT products_stock_non_negative CHECK (stock_quantity >= 0)');
        DB::statement('ALTER TABLE reviews ADD CONSTRAINT reviews_rating_range CHECK (rating >= 1 AND rating <= 5)');
        DB::statement('ALTER TABLE order_items ADD CONSTRAINT order_items_quantity_positive CHECK (quantity > 0)');
        DB::statement('ALTER TABLE order_items ADD CONSTRAINT order_items_prices_positive CHECK (unit_price >= 0 AND total_price >= 0)');

        // Add partial indexes for better performance on filtered queries
        DB::statement('CREATE INDEX products_active_featured_partial_idx ON products (id) WHERE is_active = true AND is_featured = true');
        DB::statement('CREATE INDEX products_low_stock_idx ON products (id) WHERE stock_quantity <= min_stock_level AND track_inventory = true');
        DB::statement('CREATE INDEX orders_pending_idx ON orders (id) WHERE status = \'pending\'');
        DB::statement('CREATE INDEX orders_processing_idx ON orders (id) WHERE status = \'processing\'');

        // Add composite indexes for common query patterns
        DB::statement('CREATE INDEX products_category_price_idx ON products (category_id, price) WHERE is_active = true');
        DB::statement('CREATE INDEX orders_user_status_date_idx ON orders (user_id, status, created_at)');

        // Try to enable PostgreSQL extensions if permissions allow
        try {
            DB::statement('CREATE EXTENSION IF NOT EXISTS "uuid-ossp"');
            DB::statement('CREATE EXTENSION IF NOT EXISTS "pg_trgm"');

            // Add full-text search indexes if extensions are available
            DB::statement('
                CREATE INDEX products_search_idx ON products
                USING gin(to_tsvector(\'english\', name || \' \' || COALESCE(description, \'\')))
            ');

            DB::statement('
                CREATE INDEX categories_search_idx ON categories
                USING gin(to_tsvector(\'english\', name || \' \' || COALESCE(description, \'\')))
            ');

            // Add trigram indexes for fuzzy search
            DB::statement('CREATE INDEX products_name_trgm_idx ON products USING gin(name gin_trgm_ops)');
            DB::statement('CREATE INDEX categories_name_trgm_idx ON categories USING gin(name gin_trgm_ops)');
        } catch (Exception $e) {
            // Extensions might not be available, skip advanced features
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop custom indexes and constraints
        DB::statement('DROP INDEX IF EXISTS products_search_idx');
        DB::statement('DROP INDEX IF EXISTS categories_search_idx');
        DB::statement('DROP INDEX IF EXISTS products_name_trgm_idx');
        DB::statement('DROP INDEX IF EXISTS categories_name_trgm_idx');
        DB::statement('DROP INDEX IF EXISTS products_active_featured_partial_idx');
        DB::statement('DROP INDEX IF EXISTS products_low_stock_idx');
        DB::statement('DROP INDEX IF EXISTS orders_pending_idx');
        DB::statement('DROP INDEX IF EXISTS orders_processing_idx');
        DB::statement('DROP INDEX IF EXISTS products_category_price_idx');
        DB::statement('DROP INDEX IF EXISTS orders_user_status_date_idx');

        // Drop check constraints
        DB::statement('ALTER TABLE products DROP CONSTRAINT IF EXISTS products_price_positive');
        DB::statement('ALTER TABLE products DROP CONSTRAINT IF EXISTS products_stock_non_negative');
        DB::statement('ALTER TABLE reviews DROP CONSTRAINT IF EXISTS reviews_rating_range');
        DB::statement('ALTER TABLE order_items DROP CONSTRAINT IF EXISTS order_items_quantity_positive');
        DB::statement('ALTER TABLE order_items DROP CONSTRAINT IF EXISTS order_items_prices_positive');
    }
};
