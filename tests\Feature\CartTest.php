<?php

use App\Models\Product;
use App\Models\User;
use App\Models\ShoppingCart;

test('guest user can view empty cart', function () {
    $response = $this->get('/api/v1/cart');
    
    $response->assertStatus(200)
             ->assertJson([
                 'success' => true,
                 'data' => [
                     'items' => [],
                     'total_items' => 0,
                     'subtotal' => 0,
                 ]
             ]);
});

test('guest user can add product to cart', function () {
    $product = Product::factory()->create([
        'price' => 100.00,
        'stock_quantity' => 10,
        'track_inventory' => true,
    ]);
    
    $response = $this->post('/api/v1/cart', [
        'product_id' => $product->id,
        'quantity' => 2,
    ]);
    
    $response->assertStatus(200)
             ->assertJson([
                 'success' => true,
             ]);
    
    // Verify cart item was created
    $this->assertDatabaseHas('shopping_cart', [
        'product_id' => $product->id,
        'quantity' => 2,
        'user_id' => null,
    ]);
});

test('authenticated user can view cart', function () {
    $user = User::factory()->create();
    
    $response = $this->actingAs($user)->get('/api/v1/cart');
    
    $response->assertStatus(200)
             ->assertJson([
                 'success' => true,
                 'data' => [
                     'items' => [],
                     'total_items' => 0,
                     'subtotal' => 0,
                 ]
             ]);
});

test('authenticated user can add product to cart', function () {
    $user = User::factory()->create();
    $product = Product::factory()->create([
        'price' => 150.00,
        'stock_quantity' => 5,
        'track_inventory' => true,
    ]);
    
    $response = $this->actingAs($user)->post('/api/v1/cart', [
        'product_id' => $product->id,
        'quantity' => 1,
    ]);
    
    $response->assertStatus(200)
             ->assertJson([
                 'success' => true,
             ]);
    
    // Verify cart item was created
    $this->assertDatabaseHas('shopping_cart', [
        'product_id' => $product->id,
        'quantity' => 1,
        'user_id' => $user->id,
    ]);
});

test('guest cart can be transferred to authenticated user', function () {
    $user = User::factory()->create();
    $product = Product::factory()->create([
        'price' => 200.00,
        'stock_quantity' => 10,
        'track_inventory' => true,
    ]);
    
    // Create guest cart item
    $sessionId = 'test-session-id';
    ShoppingCart::create([
        'session_id' => $sessionId,
        'product_id' => $product->id,
        'quantity' => 3,
        'user_id' => null,
    ]);
    
    // Mock session ID
    $this->withSession(['_token' => 'test-token']);
    session()->setId($sessionId);
    
    $response = $this->actingAs($user)->post('/api/v1/cart/transfer-guest-cart');
    
    $response->assertStatus(200)
             ->assertJson([
                 'success' => true,
             ]);
    
    // Verify guest cart item was transferred
    $this->assertDatabaseHas('shopping_cart', [
        'product_id' => $product->id,
        'quantity' => 3,
        'user_id' => $user->id,
    ]);
    
    // Verify guest cart item was removed
    $this->assertDatabaseMissing('shopping_cart', [
        'session_id' => $sessionId,
        'user_id' => null,
    ]);
});

test('cart respects stock limits', function () {
    $product = Product::factory()->create([
        'price' => 50.00,
        'stock_quantity' => 2,
        'track_inventory' => true,
    ]);
    
    $response = $this->post('/api/v1/cart', [
        'product_id' => $product->id,
        'quantity' => 5, // More than available stock
    ]);
    
    $response->assertStatus(422)
             ->assertJson([
                 'success' => false,
             ]);
});
