# Laravel 12 + React + Inertia.js Production Deployment on Render

This guide provides step-by-step instructions for deploying your Laravel 12 Lucky Star E-commerce application with React frontend to Render using Docker and external Neon.tech PostgreSQL database.

## Prerequisites

- Git repository with your Laravel 12 + React project
- Render account (free tier available)
- External PostgreSQL database (Neon.tech configured)
- Basic understanding of Docker, Laravel, and React

## Project Structure

The following production deployment files are included in your project:

```
├── Dockerfile                    # Production Docker configuration with React build
├── .dockerignore                # Files to exclude from Docker build
├── scripts/00-laravel-deploy.sh # Deployment script with React build
├── render.yaml                  # Render Infrastructure as Code (external DB)
├── .env.render                  # Environment template for Render
├── vite.config.ts               # Vite configuration optimized for production
└── RENDER_DEPLOYMENT.md         # This guide
```

## Key Features

### React + Vite Integration
- **Multi-stage Docker build**: Builds React assets with Vite in a separate stage
- **Automatic asset compilation**: Frontend assets are built during Docker image creation
- **Manifest generation**: Ensures Vite manifest.json is properly generated for Laravel
- **External database support**: Configured for Neon.tech PostgreSQL (no local DB container)

### Production Optimizations
- **Asset caching**: Built assets are cached in the Docker image
- **Optimized builds**: Uses Node.js Alpine for smaller image size
- **Laravel caching**: All Laravel caches (config, routes, views) are generated

## Deployment Options

### Option 1: Manual Deployment (Recommended for first-time)

#### Step 1: External Database (Already Configured)

Your application is already configured to use Neon.tech PostgreSQL:
- **Host**: `ep-green-snowflake-a1bxdo4q.ap-southeast-1.aws.neon.tech`
- **Database**: `luckystar_db`
- **User**: `luckystar_db_owner`

No additional database setup required on Render.

#### Step 2: Application Key (Already Generated)

Your application key is already configured:
```
base64:mZeIIwHp72ahtkbNQmAOFi6LWqFhkrsYKZDvGkQun1U=
```

#### Step 3: Create Web Service

1. In Render Dashboard, click "New +" and select "Web Service"
2. Connect your Git repository
3. Configure the service:
   - **Name**: `lucky-star-laravel`
   - **Region**: Choose your preferred region
   - **Branch**: `main` (or your default branch)
   - **Runtime**: `Docker`
   - **Plan**: Free or Starter

#### Step 4: Configure Environment Variables

In the "Advanced" section, add these environment variables:

| Key | Value |
|-----|-------|
| `APP_KEY` | `base64:mZeIIwHp72ahtkbNQmAOFi6LWqFhkrsYKZDvGkQun1U=` |
| `APP_NAME` | `Lucky Star E-commerce` |
| `APP_ENV` | `production` |
| `APP_DEBUG` | `false` |
| `LOG_CHANNEL` | `stderr` |
| `DB_CONNECTION` | `pgsql` |
| `DB_HOST` | `ep-green-snowflake-a1bxdo4q.ap-southeast-1.aws.neon.tech` |
| `DB_PORT` | `5432` |
| `DB_DATABASE` | `luckystar_db` |
| `DB_USERNAME` | `luckystar_db_owner` |
| `DB_PASSWORD` | `npg_dw2mgQ6HRotp` |
| `VITE_APP_NAME` | `Lucky Star E-commerce` |

#### Step 5: Deploy

1. Click "Create Web Service"
2. Render will automatically build and deploy your application
3. Monitor the build logs for any issues
4. Once deployed, your app will be available at `https://your-service-name.onrender.com`

### Option 2: Infrastructure as Code (Advanced)

Use the included `render.yaml` file for automated deployment:

1. Push your code to GitHub/GitLab
2. In Render Dashboard, click "New +" and select "Blueprint"
3. Connect your repository
4. Render will automatically create both the database and web service
5. Monitor the deployment in the dashboard

## Local Development Setup

For local development without Docker, follow these steps:

### Prerequisites
- PHP 8.2 or higher
- Node.js 18+ and npm
- Composer

### Local Development Steps

1. Install dependencies:
   ```bash
   composer install
   npm install
   ```

2. Set up environment:
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

3. Configure your `.env` file with the Neon.tech database credentials

4. Run database migrations:
   ```bash
   php artisan migrate
   ```

5. Start development servers:
   ```bash
   # Terminal 1: Laravel backend
   php artisan serve

   # Terminal 2: Vite frontend with hot reload
   npm run dev
   ```

6. Access your application at `http://localhost:8000`

### Database Connection

The application uses your external Neon.tech database, so no local PostgreSQL setup is required.

## Troubleshooting

### Common Issues

1. **Vite Manifest Not Found Error**:
   - Ensure `npm run build` completes successfully during Docker build
   - Check that `/public/build/manifest.json` exists in the container
   - Verify Vite configuration includes `manifest: true`

2. **React Assets Not Loading**:
   - Check that `@vite` directive in `app.blade.php` is correct
   - Verify `APP_URL` matches your Render service URL
   - Ensure HTTPS is forced in production (handled by AppServiceProvider)

3. **Build Fails During Docker Build**:
   - Check that `package.json` and `package-lock.json` are not in `.dockerignore`
   - Verify Node.js dependencies are compatible
   - Check build logs for specific npm/Vite errors
   - Ensure `npm run build` works locally before deploying

4. **Database Connection Error**:
   - Verify Neon.tech database credentials are correct
   - Check that database allows connections from Render's IP ranges
   - Ensure SSL connection is properly configured

5. **TypeScript/React Compilation Errors**:
   - Check that all TypeScript dependencies are in `package.json`
   - Verify React and TypeScript versions are compatible
   - Review Vite configuration for proper TypeScript handling

### Logs and Debugging

- View application logs in Render Dashboard under "Logs" tab
- For local debugging, check Laravel logs in `storage/logs/laravel.log`
- Use `php artisan tinker` for interactive debugging

## Production Optimizations

The deployment script automatically handles:
- Composer dependency installation
- Configuration caching
- Route caching
- View caching
- Database migrations
- Application optimization

## Security Considerations

- Never commit `.env` files to version control
- Use strong, unique `APP_KEY`
- Keep `APP_DEBUG=false` in production
- Regularly update dependencies

## Support

For issues specific to:
- **Laravel**: Check [Laravel Documentation](https://laravel.com/docs/12.x)
- **Render**: Contact [Render Support](mailto:<EMAIL>)
- **Docker**: Refer to [Docker Documentation](https://docs.docker.com)

## Next Steps

After successful deployment:
1. Set up custom domain (optional)
2. Configure email services
3. Set up monitoring and alerts
4. Implement CI/CD pipeline
5. Configure Redis for caching (optional)

Your Laravel 12 application is now ready for production on Render!
