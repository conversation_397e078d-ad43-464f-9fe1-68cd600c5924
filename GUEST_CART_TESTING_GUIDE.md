# Guest Cart Functionality Testing Guide

## Overview
This guide outlines how to test the newly implemented guest cart functionality that allows non-authenticated users to add items to their cart using localStorage persistence.

## Features Implemented

### 1. Guest Cart with localStorage
- **Storage**: Guest cart data is stored in browser localStorage under the key `guest_cart`
- **Persistence**: <PERSON>t persists across browser sessions and page refreshes
- **Fallback**: Graceful handling when localStorage is not available

### 2. Dual Storage System
- **Guest Users**: Cart data stored in localStorage with product details fetched via API
- **Authenticated Users**: Cart data stored in database via existing API endpoints
- **Seamless Switching**: Automatic detection and switching between storage modes

### 3. Cart Synchronization
- **Login Transfer**: When guest users log in, their localStorage cart is automatically merged with their authenticated account cart
- **Conflict Resolution**: Duplicate items have quantities combined
- **Cleanup**: localStorage cart is cleared after successful transfer

### 4. Enhanced API Support
- **Batch Product Endpoint**: New `/api/v1/products/batch` endpoint for fetching multiple products
- **Enhanced Transfer Endpoint**: Updated cart transfer to handle localStorage format

## Testing Scenarios

### Scenario 1: Guest Cart Basic Functionality
1. **Open the application** in an incognito/private browser window
2. **Navigate to Products page** (`/products`)
3. **Add items to cart** by clicking "Add to Cart" on product cards
4. **Verify cart count** updates in the header navigation
5. **Refresh the page** and verify cart items persist
6. **Close and reopen browser** and verify cart items still exist

### Scenario 2: Cart Persistence Across Sessions
1. **Add items to cart** as a guest user
2. **Close the browser completely**
3. **Reopen browser** and navigate back to the site
4. **Verify cart items** are still present in the header count
5. **Check localStorage** in browser dev tools (`Application > Local Storage > guest_cart`)

### Scenario 3: Guest to Authenticated User Transfer
1. **Add items to cart** as a guest user (3-4 different products)
2. **Note the cart count** in the header
3. **Log in** to an existing account
4. **Verify cart items** are transferred and merged with any existing authenticated cart
5. **Check that localStorage** `guest_cart` key is removed after transfer
6. **Verify cart count** reflects the merged total

### Scenario 4: Conflict Resolution
1. **Log in** to an authenticated account
2. **Add some items** to the authenticated cart
3. **Log out**
4. **Add the same items** (and some different ones) to the guest cart
5. **Log back in**
6. **Verify quantities** are combined for duplicate items
7. **Verify new items** are added to the cart

### Scenario 5: Error Handling
1. **Add items to guest cart**
2. **Disable localStorage** in browser dev tools (Application > Storage > Local Storage > Block)
3. **Try to add more items** and verify graceful fallback
4. **Re-enable localStorage** and verify functionality returns

## Technical Validation

### Type Safety and Code Quality
```bash
# Run type checking
npm run types

# Run linting
npm run lint

# Both should pass without errors
```

### API Endpoints to Test
1. **GET /api/v1/products** - Product listing (existing)
2. **POST /api/v1/products/batch** - Batch product fetching (new)
3. **GET /api/v1/cart** - Cart retrieval (enhanced for guest/auth)
4. **POST /api/v1/cart** - Add to cart (enhanced for guest/auth)
5. **POST /api/v1/cart/transfer-guest-cart** - Cart transfer (enhanced)

### Browser Developer Tools Checks
1. **localStorage**: Check `guest_cart` key exists and contains proper JSON structure
2. **Network Tab**: Verify API calls are made correctly for both guest and authenticated modes
3. **Console**: Should be free of JavaScript errors during cart operations

## Expected Behavior

### Guest Users
- ✅ Can add items to cart without authentication
- ✅ Cart persists across browser sessions
- ✅ Cart count displays correctly in header
- ✅ Cart data stored in localStorage with proper structure

### Authenticated Users
- ✅ Cart data stored in database (existing behavior)
- ✅ Can receive transferred guest cart items on login
- ✅ Duplicate items have quantities merged
- ✅ localStorage cleared after successful transfer

### UI Components
- ✅ Header cart button shows correct count for both user types
- ✅ Product cards "Add to Cart" works for both user types
- ✅ Quick view modal "Add to Cart" works for both user types
- ✅ No visual differences between guest and authenticated cart UI

## Troubleshooting

### Common Issues
1. **Cart not persisting**: Check if localStorage is enabled in browser
2. **Transfer not working**: Verify user authentication and API connectivity
3. **Count not updating**: Check if CartProvider is wrapping the components properly
4. **API errors**: Check Laravel logs and network tab for detailed error messages

### Debug Information
- **localStorage key**: `guest_cart`
- **Cart context**: Available via `useCart()` hook
- **Guest mode indicator**: `isGuestMode` property in cart context
- **Transfer endpoint**: `/api/v1/cart/transfer-guest-cart`

## Files Modified/Created

### New Files
- `resources/js/utils/localStorage-cart.ts` - localStorage utilities
- `GUEST_CART_TESTING_GUIDE.md` - This testing guide

### Modified Files
- `resources/js/hooks/use-cart.tsx` - Enhanced for dual storage
- `app/Http/Controllers/Api/ProductController.php` - Added batch endpoint
- `app/Http/Controllers/Api/ShoppingCartController.php` - Enhanced transfer
- `routes/api.php` - Added batch products route

## Success Criteria
- ✅ All TypeScript types pass (`npm run types`)
- ✅ All ESLint rules pass (`npm run lint`)
- ✅ Guest users can add items to cart
- ✅ Cart persists across browser sessions
- ✅ Login transfers guest cart to authenticated account
- ✅ No JavaScript console errors during cart operations
- ✅ UI displays correct cart counts for both user types
